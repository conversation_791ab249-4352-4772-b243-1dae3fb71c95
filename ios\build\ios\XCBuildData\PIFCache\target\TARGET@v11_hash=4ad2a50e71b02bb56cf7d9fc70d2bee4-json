{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98800bea82bda201eb93f9594d3a6581e6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982d0e9d5772cbe0f57bf524b605704ada", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c384768b3f47f3368d7019f2fdcf7ade", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bc796002f0c75f439f522db38228551d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c384768b3f47f3368d7019f2fdcf7ade", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9838dcb18e9ee7da3fc06be05d8baf1bd5", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ece7490dceca9cb9dc6a50c1f995a47e", "guid": "bfdfe7dc352907fc980b868725387e98f295126a016564e011ec3c3ad7bba822", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e985395cd0b23705e89890d23d0464bc916", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988806935b999c7bc5318a1ecb154218e5", "guid": "bfdfe7dc352907fc980b868725387e98c9c148b0c07fb7f5ce099e21dbc9f115"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f815ecc8fcc86efd3e249d73bbdf970", "guid": "bfdfe7dc352907fc980b868725387e9858d26d3337ad97e4bb06cfc98c6cfed5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f8d0110caa917b56e68202a557a6100", "guid": "bfdfe7dc352907fc980b868725387e9869c2b0cbd6f90978e730f7529c67addf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f7047b1b185bb67e0b2491b6bda77e8", "guid": "bfdfe7dc352907fc980b868725387e98c3c333f97d0d6b7beedf9430433625e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980db82f3cc6afce58109a9287e8e79ce2", "guid": "bfdfe7dc352907fc980b868725387e98067c32b22ad157344add83b40ed232d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897c717909a54d7ae41e2bcf32e28a3d2", "guid": "bfdfe7dc352907fc980b868725387e985d781df3e51b37af30ba93a1dea6c340"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818254c240924fb4cf5d6e744fc73ec33", "guid": "bfdfe7dc352907fc980b868725387e987f9c6c484ee9afbdc00c21d24e3dd6a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1674613867e34df0f4ed9b188c2f7c8", "guid": "bfdfe7dc352907fc980b868725387e9884fad9b7b181a2100dbf9d665234912e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831d06d4d825e195b0409f9451dc58280", "guid": "bfdfe7dc352907fc980b868725387e982e1198a91f5c5cee53018a05ad3b79c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adea7c17952528876ef1712745927458", "guid": "bfdfe7dc352907fc980b868725387e98a9013f01949fced9ded709ecaadb2b82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835ccf383167e5019dd65cb0faf506698", "guid": "bfdfe7dc352907fc980b868725387e9853d26b1e4a9b66290e93d1ee2c6babca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb27f9266fb8684bf96b5da4f458b1ce", "guid": "bfdfe7dc352907fc980b868725387e98277a0ff2c909d8cfe561a35e2743b23f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c10091132088fdd08ee7b18e5aac74c", "guid": "bfdfe7dc352907fc980b868725387e98ccba8df0cc8076b42255366fb1f4f1a1"}], "guid": "bfdfe7dc352907fc980b868725387e980ab4b596fe3df2df492564c649e31821", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e98908be7e88ba600903f85dabbf39df94a"}], "guid": "bfdfe7dc352907fc980b868725387e9831bb7058a25ce29b30003fde441ab9dc", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987c2a6fcde8fa9f7fd26fe3ffc60dbca9", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e987bf6554ce7f5206f30441740fb618afa", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}