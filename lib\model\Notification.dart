class NotificationModel {
  final int? id;
  final int? userId;
  final bool is1to1Message;
  final bool isGroupMessage;
  final int? senderId;
  final int? receiverId;
  final int? conversationId;
  final String messageContent;
  final String? senderPhoto;
  final bool isMenu;
  final bool isNewPubReq;
  final bool isNewPub;
  final int? pubId;
  final bool isSuivie;
  final int? parentId;
  final int? eleveId;
  final int? formateurId;
  final bool isEvent;
  final String? dateEvent;
  final String? timeStamp;
  final String content;
  final String? photoUrl;
  final bool isRead;
  final int notifcationCounter;
  final int? notificationId;
  final int messageCounter;
  final String? senderName;

  NotificationModel({
    this.id,
    this.userId,
    required this.is1to1Message,
    required this.isGroupMessage,
    this.senderId,
    this.receiverId,
    this.conversationId,
    required this.messageContent,
    this.senderPhoto,
    required this.isMenu,
    required this.isNewPubReq,
    required this.isNewPub,
    this.pubId,
    required this.isSuivie,
    this.parentId,
    this.eleveId,
    this.formateurId,
    required this.isEvent,
    this.dateEvent,
    this.timeStamp,
    required this.content,
    this.photoUrl,
    this.isRead = false,
    required this.notifcationCounter,
    this.notificationId,
    required this.messageCounter,
    this.senderName,
  });

  /*  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'],
      userId: _parseIntSafely(json['userId']),
      is1to1Message: json['is1to1Message'] ?? false,
      isGroupMessage: json['isGroupMessage'] ?? false,
      senderId: _parseIntSafely(json['senderId']),
      receiverId: _parseIntSafely(json['receiverId']),
      conversationId: _parseIntSafely(json['conversationId']),
      messageContent: json['messageContent'] ?? '',
      senderPhoto: json['senderPhoto'],
      isMenu: json['isMenu'] ?? false,
      isNewPubReq: json['isNewPubReq'] ?? false,
      isNewPub: json['isNewPub'] ?? false,
      pubId: _parseIntSafely(json['pubId']),
      isSuivie: json['isSuivie'] ?? false,
      parentId: _parseIntSafely(json['parentId']),
      eleveId: _parseIntSafely(json['eleveId']),
      formateurId: _parseIntSafely(json['formateurId']),
      isEvent: json['isEvent'] ?? false,
      dateEvent: json['dateEvent'],
      timeStamp: json['timeStamp'],
      content: json['content'] ?? '',
      photoUrl: json['photoUrl'],
      isRead: json['isRead'] ?? false,
      notifcationCounter: _parseIntSafely(json['notifcationCounter']) ?? 0,
      notificationId: _parseIntSafely(json['notificationId']),
      messageCounter: _parseIntSafely(json['messageCounter']) ?? 0,
      senderName: json['senderName'],
    );
  } */

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'],
      userId: _parseIntSafely(json['userId']),
      is1to1Message: _parseBoolSafely(json['is1to1Message']),
      isGroupMessage: _parseBoolSafely(json['isGroupMessage']),
      senderId: _parseIntSafely(json['senderId']),
      receiverId: _parseIntSafely(json['receiverId']),
      conversationId: _parseIntSafely(json['conversationId']),
      messageContent: json['messageContent'] ?? '',
      senderPhoto: json['senderPhoto'],
      isMenu: _parseBoolSafely(json['isMenu']),
      isNewPubReq: _parseBoolSafely(json['isNewPubReq']),
      isNewPub: _parseBoolSafely(json['isNewPub']),
      pubId: _parseIntSafely(json['pubId']),
      isSuivie: _parseBoolSafely(json['isSuivie']),
      parentId: _parseIntSafely(json['parentId']),
      eleveId: _parseIntSafely(json['eleveId']),
      formateurId: _parseIntSafely(json['formateurId']),
      isEvent: _parseBoolSafely(json['isEvent']),
      dateEvent: json['dateEvent'],
      timeStamp: json['timeStamp'],
      content: json['content'] ?? '',
      photoUrl: json['photoUrl'],
      isRead: _parseBoolSafely(json['isRead']),
      notifcationCounter: _parseIntSafely(json['notifcationCounter']) ?? 0,
      notificationId: _parseIntSafely(json['notificationId']),
      messageCounter: _parseIntSafely(json['messageCounter']) ?? 0,
      senderName: json['senderName'],
    );
  }

// Helper method to safely parse booleans (handles strings as well)
  static bool _parseBoolSafely(dynamic value) {
    if (value == null) return false;
    if (value is bool) return value;
    if (value is String) {
      return value.toLowerCase() == 'true';
    }
    return false;
  }

  // Add this helper method to safely parse integers
  static int? _parseIntSafely(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) {
      return int.tryParse(value);
    }
    return null;
  }

  NotificationModel copyWith({
    int? userId,
    bool? is1to1Message,
    bool? isGroupMessage,
    int? senderId,
    int? receiverId,
    int? conversationId,
    String? messageContent,
    String? senderPhoto,
    bool? isMenu,
    bool? isNewPubReq,
    bool? isNewPub,
    int? pubId,
    bool? isSuivie,
    int? parentId,
    int? eleveId,
    int? formateurId,
    bool? isEvent,
    String? dateEvent,
    String? timeStamp,
    String? content,
    String? photoUrl,
    bool? isRead,
    int? notifcationCounter,
    int? notificatationId,
    int? messageCounter,
    String? senderName,
  }) {
    return NotificationModel(
      userId: userId ?? this.userId,
      is1to1Message: is1to1Message ?? this.is1to1Message,
      isGroupMessage: isGroupMessage ?? this.isGroupMessage,
      senderId: senderId ?? this.senderId,
      receiverId: receiverId ?? this.receiverId,
      conversationId: conversationId ?? this.conversationId,
      messageContent: messageContent ?? this.messageContent,
      senderPhoto: senderPhoto ?? (this.senderPhoto as String?),
      isMenu: isMenu ?? this.isMenu,
      isNewPubReq: isNewPubReq ?? this.isNewPubReq,
      isNewPub: isNewPub ?? this.isNewPub,
      pubId: pubId ?? this.pubId,
      isSuivie: isSuivie ?? this.isSuivie,
      parentId: parentId ?? this.parentId,
      eleveId: eleveId ?? this.eleveId,
      formateurId: formateurId ?? this.formateurId,
      isEvent: isEvent ?? this.isEvent,
      dateEvent: dateEvent ?? (this.dateEvent as String?),
      timeStamp: timeStamp ?? this.timeStamp as String?,
      content: content ?? this.content,
      photoUrl: photoUrl ?? (this.photoUrl as String?),
      isRead: isRead ?? this.isRead,
      notifcationCounter: notifcationCounter ?? this.notifcationCounter,
      notificationId: notificationId ?? this.notificationId,
      messageCounter: messageCounter ?? this.messageCounter,
      senderName: senderName ?? this.senderName,
    );
  }

  @override
  String toString() {
    return 'NotificationModel('
        'userId: $userId, '
        'is1to1Message: $is1to1Message, '
        'isGroupMessage: $isGroupMessage, '
        'senderId: $senderId, '
        'receiverId: $receiverId, '
        'conversationId: $conversationId, '
        'messageContent: $messageContent, '
        'senderPhoto: $senderPhoto, '
        'isMenu: $isMenu, '
        'isNewPubReq: $isNewPubReq, '
        'isNewPub: $isNewPub, '
        'pubId: $pubId, '
        'isSuivie: $isSuivie, '
        'parentId: $parentId, '
        'eleveId: $eleveId, '
        'formateurId: $formateurId, '
        'isEvent: $isEvent, '
        'dateEvent: $dateEvent, '
        'timeStamp: $timeStamp, '
        'content: $content, '
        'photoUrl: $photoUrl, '
        'isRead: $isRead, '
        'notifcationCounter: $notifcationCounter, '
        'notificationId: $notificationId, '
        'messageCounter: $messageCounter'
        'senderName: $senderName, '
        ')';
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'is1to1Message': is1to1Message,
      'isGroupMessage': isGroupMessage,
      'senderId': senderId,
      'receiverId': receiverId,
      'conversationId': conversationId,
      'messageContent': messageContent,
      'senderPhoto': senderPhoto,
      'isMenu': isMenu,
      'isNewPubReq': isNewPubReq,
      'isNewPub': isNewPub,
      'pubId': pubId,
      'isSuivie': isSuivie,
      'parentId': parentId,
      'eleveId': eleveId,
      'formateurId': formateurId,
      'isEvent': isEvent,
      'dateEvent': dateEvent,
      'timeStamp': timeStamp,
      'content': content,
      'photoUrl': photoUrl,
      'isRead': isRead,
      'notifcationCounter': notifcationCounter,
      'notificationId': notificationId,
      'messageCounter': messageCounter,
      'senderName': senderName,
    };
  }
}
