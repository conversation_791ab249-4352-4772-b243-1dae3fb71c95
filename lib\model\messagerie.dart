class Message {
  String? senderId;
  String? receiverId;
  String? content;
  String status;
  String timestamp;
  int? conversationId;
  bool read;

  Message({
    required this.senderId,
    required this.receiverId,
    required this.content,
    required this.status,
    required this.timestamp,
    required this.read,
    this.conversationId,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      senderId: json['senderId'].toString(),
      receiverId: json['receiverId'].toString(),
      content: json['content'] as String,
      status: json['status'] as String,
      timestamp: json['timestamp'] as String,
      read: json['read'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'senderId': senderId,
      'receiverId':
          (receiverId == "0") ? null : receiverId, // ✅ Replace "0" with null
      'content': content,
      'status': status,
      'timestamp': timestamp,
      'read': read,
      'conversationId': conversationId,
    };
  }
}
