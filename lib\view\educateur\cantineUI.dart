import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:bee_kids_mobile/model/cantine.dart';
import 'package:bee_kids_mobile/view/educateur/footer.dart';
import 'package:bee_kids_mobile/view_model/cantineService.dart';

class CantineScreenEducateur extends StatefulWidget {
  const CantineScreenEducateur({super.key});

  @override
  State<CantineScreenEducateur> createState() => _CantineScreenState();
}

class _CantineScreenState extends State<CantineScreenEducateur> {
  final ImagePicker _picker = ImagePicker();
  final Map<String, String?> _selectedPlatImages = {};
  final cantineService _menuService = cantineService();
  late Future<List<cantine>> _menus;
  late Future<Map<String, String>> _currentWeek;

  @override
  void initState() {
    super.initState();
    _menus = _menuService.fetchMenus();
    _currentWeek = _menuService.fetchCurrentWeek();
  }

  Future<void> _pickImage(BuildContext context, String day,
      void Function(void Function()) updateDialog) async {
    final XFile? pickedFile =
        await _picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      updateDialog(() {
        _selectedPlatImages[day] = pickedFile.path;
      });
    }
  }

  Future<void> _pickImageFromCamera(BuildContext context, String day,
      void Function(void Function()) updateDialog) async {
    final XFile? pickedFile =
        await _picker.pickImage(source: ImageSource.camera);
    if (pickedFile != null) {
      updateDialog(() {
        _selectedPlatImages[day] = pickedFile.path;
      });
    }
  }

  void _showPopup(BuildContext context, String day, cantine menu) {
    final TextEditingController platController =
        TextEditingController(text: menu.platPrincipale);
    final TextEditingController entreeController =
        TextEditingController(text: menu.entree);
    final TextEditingController dessertController =
        TextEditingController(text: menu.dessert);
    bool isLoading = false;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setStateDialog) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15),
              ),
              child: Padding(
                padding:
                    EdgeInsets.all(MediaQuery.of(context).size.width * 0.03),
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Text(
                        'Menu de $day',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: MediaQuery.of(context).size.width * 0.06,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      SizedBox(
                          height: MediaQuery.of(context).size.height * 0.01),
                      Center(
                        child: GestureDetector(
                          onTap: () {
                            _showImagePickerOptions(
                                context, day, setStateDialog);
                          },
                          child: _selectedPlatImages[day] != null
                              ? Image.file(
                                  File(_selectedPlatImages[day]!),
                                  height:
                                      MediaQuery.of(context).size.width * 0.4,
                                  width:
                                      MediaQuery.of(context).size.width * 0.4,
                                  fit: BoxFit.cover,
                                )
                              : menu.photoUrl.isNotEmpty
                                  ? Image.network(
                                      menu.photoUrl,
                                      height:
                                          MediaQuery.of(context).size.width *
                                              0.4,
                                      width: MediaQuery.of(context).size.width *
                                          0.4,
                                      fit: BoxFit.cover,
                                      errorBuilder:
                                          (context, error, stackTrace) =>
                                              Image.asset(
                                        'lib/resources/images/dish-placeholder-hd-png.png',
                                        height:
                                            MediaQuery.of(context).size.width *
                                                0.4,
                                        width:
                                            MediaQuery.of(context).size.width *
                                                0.4,
                                        fit: BoxFit.cover,
                                      ),
                                    )
                                  : Image.asset(
                                      'lib/resources/images/dish-placeholder-hd-png.png',
                                      height:
                                          MediaQuery.of(context).size.width *
                                              0.4,
                                      width: MediaQuery.of(context).size.width *
                                          0.4,
                                      fit: BoxFit.cover,
                                    ),
                        ),
                      ),
                      SizedBox(
                          height: MediaQuery.of(context).size.height * 0.02),
                      TextField(
                        controller: entreeController,
                        decoration: const InputDecoration(
                          labelText: 'Entrée',
                          border: OutlineInputBorder(),
                        ),
                      ),
                      SizedBox(
                          height: MediaQuery.of(context).size.height * 0.015),
                      TextField(
                        controller: platController,
                        decoration: const InputDecoration(
                          labelText: 'Plat Principal',
                          border: OutlineInputBorder(),
                        ),
                      ),
                      SizedBox(
                          height: MediaQuery.of(context).size.height * 0.015),
                      TextField(
                        controller: dessertController,
                        decoration: const InputDecoration(
                          labelText: 'Dessert',
                          border: OutlineInputBorder(),
                        ),
                      ),
                      SizedBox(
                          height: MediaQuery.of(context).size.height * 0.02),

                      /// **Loader when saving**
                      if (isLoading)
                        LinearProgressIndicator(color: Colors.green),
                      SizedBox(height: isLoading ? 10 : 0),
                      ElevatedButton(
                        onPressed: isLoading
                            ? null
                            : () async {
                                setStateDialog(() => isLoading = true);
                                try {
                                  await _menuService.createMenu(
                                    jour: day,
                                    entree: entreeController.text.isNotEmpty
                                        ? entreeController.text
                                        : menu.entree,
                                    platPrincipale:
                                        platController.text.isNotEmpty
                                            ? platController.text
                                            : menu.platPrincipale,
                                    dessert: dessertController.text.isNotEmpty
                                        ? dessertController.text
                                        : menu.dessert,
                                    photo: _selectedPlatImages[day] != null
                                        ? File(_selectedPlatImages[day]!)
                                        : await _menuService
                                            .getImageFileFromUrl(menu.photoUrl),
                                  );

                                  setState(() {
                                    _menus = _menuService.fetchMenus();
                                  });

                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                          'Données sauvegardées avec succès'),
                                      backgroundColor: Colors.green,
                                    ),
                                  );

                                  Navigator.pop(context);
                                } catch (e) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text('Erreur: $e'),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                } finally {
                                  setStateDialog(() => isLoading = false);
                                }
                              },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green[800],
                          padding: EdgeInsets.symmetric(
                            horizontal:
                                MediaQuery.of(context).size.width * 0.06,
                            vertical:
                                MediaQuery.of(context).size.height * 0.015,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: isLoading
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                    color: Colors.white, strokeWidth: 2),
                              )
                            : const Text(
                                'Enregistrer',
                                style: TextStyle(
                                    color: Colors.white, fontSize: 16),
                              ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  void _showImagePickerOptions(BuildContext context, String day,
      void Function(void Function()) setStateDialog) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: <Widget>[
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Galerie'),
                onTap: () async {
                  Navigator.pop(context);
                  await _pickImage(context, day, setStateDialog);
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_camera),
                title: const Text('Caméra'),
                onTap: () async {
                  Navigator.pop(context);
                  await _pickImageFromCamera(context, day, setStateDialog);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMenuCard(BuildContext context, String day, cantine menu) {
    return Card(
      color: Colors.green[800],
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _showPopup(context, day, menu),
        child: Padding(
          padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.02),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                day,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: MediaQuery.of(context).size.width * 0.045,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: MediaQuery.of(context).size.height * 0.015),
              menu.photoUrl.isNotEmpty
                  ? Image.network(
                      menu.photoUrl,
                      height: MediaQuery.of(context).size.width * 0.35,
                      width: MediaQuery.of(context).size.width * 0.4,
                      fit: BoxFit.cover,
                    )
                  : Image.asset(
                      'lib/resources/images/dish-placeholder-hd-png.png',
                      height: MediaQuery.of(context).size.width * 0.3,
                      width: MediaQuery.of(context).size.width * 0.3,
                      fit: BoxFit.cover,
                    ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pushNamed(context, '/educateur/menu'),
        ),
        title: const Text(
          "Cantine",
          style: TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.green[800],
        elevation: 4,
      ),
      body: Column(
        children: [
          Padding(
            padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.04),
            child: Column(
              children: [
                const Text(
                  'Menu de la semaine',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
                FutureBuilder<Map<String, String>>(
                  future: _currentWeek,
                  builder: (context, snapshot) {
                    if (snapshot.hasData) {
                      return Text(
                        'Du ${snapshot.data!['startOfWeek']} au ${snapshot.data!['endOfWeek']}',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ],
            ),
          ),
          Expanded(
            child: FutureBuilder<List<cantine>>(
              future: _menus,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                } else if (snapshot.hasError) {
                  return Center(child: Text('Erreur: ${snapshot.error}'));
                } else if (snapshot.hasData) {
                  final menus = snapshot.data!
                      .where((menu) => menu.jour.toLowerCase() != "samedi")
                      .toList();

                  return GridView.count(
                    crossAxisCount:
                        MediaQuery.of(context).size.width < 600 ? 2 : 3,
                    childAspectRatio:
                        MediaQuery.of(context).size.width < 600 ? 0.8 : 0.7,
                    children: menus.map((menu) {
                      return _buildMenuCard(context, menu.jour, menu);
                    }).toList(),
                  );
                } else {
                  return const Center(child: Text('Aucun menu disponible'));
                }
              },
            ),
          ),
        ],
      ),
      bottomNavigationBar:
          const MyFooterEducateur(currentRoute: '/educateur/menu'),
    );
  }
}
