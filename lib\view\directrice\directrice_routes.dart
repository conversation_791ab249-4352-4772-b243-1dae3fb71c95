import 'package:bee_kids_mobile/view/directrice/Accueil.dart';
import 'package:bee_kids_mobile/view/directrice/AccueilPoster.dart';
import 'package:bee_kids_mobile/view/directrice/ActivitesEtProgramme.dart';
import 'package:bee_kids_mobile/view/directrice/Messagerie/Conversation.dart';
import 'package:bee_kids_mobile/view/directrice/Messagerie/Discussions.dart';
import 'package:bee_kids_mobile/view/directrice/PubEnAttente.dart';
import 'package:bee_kids_mobile/view/directrice/cantineUI.dart';
import 'package:bee_kids_mobile/view/directrice/evennement.dart';
import 'package:bee_kids_mobile/view/directrice/live.dart';
import 'package:bee_kids_mobile/view/directrice/menu.dart';
import 'package:bee_kids_mobile/view/directrice/suivie_enfant.dart';
import 'package:bee_kids_mobile/view/directrice/suivie_enfants.dart';
import 'package:bee_kids_mobile/view/directrice/profile.dart';
import 'package:bee_kids_mobile/view/directrice/tableau_de_bord.dart';
import 'package:bee_kids_mobile/view/test/websocket_test.dart';
import 'package:flutter/material.dart';

class DirectriceRoutes {
  static Route<dynamic>? onGenerateRoute(RouteSettings settings) {
  switch (settings.name) {
    case '/directrice':
      // Check if arguments contain a postId
      if (settings.arguments != null) {
        final args = settings.arguments as Map<String, dynamic>?;
        final postId = args?['postId'] as int?;
        return MaterialPageRoute(builder: (_) => AccueilScreen(postId: postId));
      }
      return MaterialPageRoute(builder: (_) => AccueilScreen());

    case '/directrice/acceuil':
      // Check if arguments contain a postId
      if (settings.arguments != null) {
        final args = settings.arguments as Map<String, dynamic>?;
        final postId = args?['postId'] as int?;
        return MaterialPageRoute(builder: (_) => AccueilScreen(postId: postId));
      }
      return MaterialPageRoute(builder: (_) => AccueilScreen());

      case '/directrice/poster':
        return MaterialPageRoute(builder: (_) => AcceuilPosterScreen());
      case '/directrice/enAttente':
        return MaterialPageRoute(builder: (_) => PubEnAttenteScreen());
      case '/directrice/suivie_enfants':
        return MaterialPageRoute(builder: (_) => const SuivieEnfants());
      case '/directrice/suivie_enfant':
        final args = settings.arguments;
        if (args is Map<String, dynamic>) {
          return MaterialPageRoute(
            builder: (_) => SuivieEnfant(
              eleveId: args['id'] as int,
              dateDeNaissance: args['dateDeNaissance'] as String,
              nom: args['nom'] as String,
              prenom: args['prenom'] as String,
            ),
          );
        } else {
          print('Invalid or missing arguments: $args');
          return MaterialPageRoute(
            builder: (_) => Scaffold(
              body: Center(
                child: Text(
                  'Invalid arguments for route: ${settings.name}',
                  style: const TextStyle(fontSize: 18, color: Colors.red),
                ),
              ),
            ),
          );
        }

      case '/directrice/cantine':
        return MaterialPageRoute(builder: (_) => const CantineScreen());
      case '/directrice/menu':
        return MaterialPageRoute(builder: (_) => const MenuScreen());
      case '/directrice/Discussions':
        return MaterialPageRoute(builder: (_) => DiscussionsScreen());
      case '/directrice/Evennements':
  if (settings.arguments != null) {
    final args = settings.arguments as Map<String, dynamic>?;
    final selectedDate = args?['selectedDate'];
    if (selectedDate != null) {
      // Convert string to DateTime if needed
      DateTime eventDate;
      if (selectedDate is String) {
        eventDate = DateTime.parse(selectedDate);
      } else if (selectedDate is DateTime) {
        eventDate = selectedDate;
      } else {
        eventDate = DateTime.now();
      }
      return MaterialPageRoute(builder: (_) => CalendarApp(initialDate: eventDate));
    }
  }
  return MaterialPageRoute(builder: (_) => CalendarApp());

      case '/directrice/profile':
        return MaterialPageRoute(builder: (_) => UserProfilePage());
      case '/directrice/tableau-de-bord':
        return MaterialPageRoute(builder: (_) => const TableauDeBordScreen());
      case '/directrice/ActivitesEtProgramme':
        return MaterialPageRoute(builder: (_) => ActivitesEtProgramme());
        case '/directrice/live':
        return MaterialPageRoute(builder: (_) => LiveVideo());
      case '/directrice/Conversation':
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (_) => ConversationScreen(
            currentUserId: args['currentUserId'],
            conversationId: args['conversationId'],
            recipientUserId: args['recipientUserId'],
            recipientUserName: args['recipientUserName'],
            recipientPhotoUrl: args['recipientPhotoUrl'],
          ),
        );

      case '/directrice/websocket-test':
        return MaterialPageRoute(builder: (_) => WebSocketNotificationTest());

      default:
        return MaterialPageRoute(
          builder: (_) => Scaffold(
            body: Center(
              child: Text('Route non trouvée: ${settings.name}'),
            ),
          ),
        );
    }
  }
}
