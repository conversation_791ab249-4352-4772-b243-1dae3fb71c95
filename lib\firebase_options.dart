// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDEQMcywZQNt7T0p9Z6lMtdBaKvDNXxAWw',
    appId: '1:142254664391:web:c357d243d5e4737d5aece2',
    messagingSenderId: '142254664391',
    projectId: 'idts-67182',
    authDomain: 'idts-67182.firebaseapp.com',
    storageBucket: 'idts-67182.firebasestorage.app',
    measurementId: 'G-Q7E43ZM5ZM',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDm-qvY-heUw3aMaAosScSPH-BMByCmJ2M',
    appId: '1:142254664391:android:7b85abaf179bcbdf5aece2',
    messagingSenderId: '142254664391',
    projectId: 'idts-67182',
    storageBucket: 'idts-67182.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAp1D7k_Wy_Zk16ieVc4phISjd58EprU84',
    appId: '1:142254664391:ios:994e674d6e9159365aece2',
    messagingSenderId: '142254664391',
    projectId: 'idts-67182',
    storageBucket: 'idts-67182.firebasestorage.app',
    iosBundleId: 'com.beeKids.beeKidsMobile',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyAp1D7k_Wy_Zk16ieVc4phISjd58EprU84',
    appId: '1:142254664391:ios:994e674d6e9159365aece2',
    messagingSenderId: '142254664391',
    projectId: 'idts-67182',
    storageBucket: 'idts-67182.firebasestorage.app',
    iosBundleId: 'com.beeKids.beeKidsMobile',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyB2hd0GZmKAoIbyKO8EolIYTveczXMIDIU',
    appId: '1:142254664391:web:5936235af43967825aece2',
    messagingSenderId: '142254664391',
    projectId: 'idts-67182',
    authDomain: 'idts-67182.firebaseapp.com',
    storageBucket: 'idts-67182.firebasestorage.app',
    measurementId: 'G-DDPE30P5K5',
  );

}