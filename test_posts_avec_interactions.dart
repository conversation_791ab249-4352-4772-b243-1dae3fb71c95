import 'dart:convert';
import 'lib/view_model/postService.dart';

void main() {
  // Exemple de réponse de l'API basé sur l'image fournie
  String jsonResponse = '''
  [
    {
      "id": 33,
      "content": "Test pub 2",
      "photoUrl": [
        "https://s3.dpc.com.tn/beekids/983d877-4ba8-4c34-be76-98b0abaeeca1_4257754_1.png"
      ],
      "userId": 1,
      "likeCount": 1,
      "approved": true,
      "authorName": "makramEd ben rhomme",
      "userPhotoUrl": null,
      "createdAt": "2025-07-28 14:46:30",
      "totalLikes": 1,
      "totalCommentaires": 7,
      "totalInteractions": 10
    }
  ]
  ''';

  // Test de parsing avec le nouveau modèle PostAvecInteractions
  try {
    final List<dynamic> jsonData = json.decode(jsonResponse);
    
    final List<PostAvecInteractions> postsAvecInteractionsList = jsonData.map((item) {
      return PostAvecInteractions.fromMap(item);
    }).toList();

    print("✅ Test réussi ! Nombre de posts avec plus d'interactions parsés: ${postsAvecInteractionsList.length}");
    
    // Affichage des détails de tous les posts
    print("\n📋 Liste des posts avec plus d'interactions:");
    for (int i = 0; i < postsAvecInteractionsList.length; i++) {
      final post = postsAvecInteractionsList[i];
      print("   ${i + 1}. Post ID: ${post.id}");
      print("      - Auteur: ${post.authorName}");
      print("      - Contenu: ${post.content}");
      print("      - Date: ${post.createdAt}");
      print("      - Total Likes: ${post.totalLikes}");
      print("      - Total Commentaires: ${post.totalCommentaires}");
      print("      - Total Interactions: ${post.totalInteractions}");
      print("      - Approuvé: ${post.approved}");
      print("      - Nombre de photos: ${post.photoUrl.length}");
      if (post.photoUrl.isNotEmpty) {
        print("      - Première photo: ${post.photoUrl.first}");
      }
      print("      - Photo utilisateur: ${post.userPhotoUrl ?? 'Aucune'}");
      print("");
    }
    
    // Test des propriétés calculées
    print("🔍 Test des propriétés:");
    if (postsAvecInteractionsList.isNotEmpty) {
      final premier = postsAvecInteractionsList.first;
      print("   - toString(): ${premier.toString()}");
      print("   - Post le plus populaire: ${premier.authorName} avec ${premier.totalInteractions} interactions");
    }
    
    // Test de tri par interactions (simulation)
    postsAvecInteractionsList.sort((a, b) => b.totalInteractions.compareTo(a.totalInteractions));
    print("\n📊 Posts triés par interactions (décroissant):");
    for (var post in postsAvecInteractionsList) {
      print("   - ${post.authorName}: ${post.totalInteractions} interactions");
    }
    
  } catch (e) {
    print("❌ Erreur lors du parsing: $e");
  }
}
