import 'dart:convert';
import 'package:bee_kids_mobile/model/Notification.dart';
import 'package:bee_kids_mobile/view_model/NotificationService.dart';
import 'package:bee_kids_mobile/resources/environnement/apiUrl.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

// Mock du client HTTP
class MockClient extends Mock implements http.Client {}

void main() {
  late NotificationService notificationService;
  late MockClient mockClient;

  setUp(() async {
    // Initialisation de FlutterBinding
    TestWidgetsFlutterBinding.ensureInitialized();

    // Initialisation des mocks
    mockClient = MockClient();
    SharedPreferences.setMockInitialValues({'userId': 1});
    notificationService = NotificationService();
  });

  test('should return a list of notifications', () async {
    // Données simulées
    final responseJson = jsonEncode([
      {
        "id": 1,
        "content": "New update available!", // Correction ici (utiliser "content" au lieu de "message" si nécessaire)
        "isRead": false,
        "timeStamp": "2024-03-17T12:00:00Z"
      }
    ]);

    final uri = Uri.parse('${ApiUrl.baseUrl}notifications/mobile/1');

    when(mockClient.get(uri)).thenAnswer(
        (_) async => Future.value(http.Response(responseJson, 200)));

    final result = await notificationService.fetchNotifications();

    expect(result, isA<List<NotificationModel>>());
    expect(result.length, 1);
  });
}
