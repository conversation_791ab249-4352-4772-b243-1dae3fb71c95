import 'package:bee_kids_mobile/model/event.dart';
import 'package:bee_kids_mobile/model/userInteraction.dart';
import 'package:bee_kids_mobile/view_model/evennementService.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:pie_chart/pie_chart.dart';
import 'package:bee_kids_mobile/view/directrice/footer.dart';
import 'package:bee_kids_mobile/view_model/classeService.dart';
import 'package:bee_kids_mobile/view_model/eleveService.dart';
import 'package:bee_kids_mobile/view_model/postService.dart';
import 'package:bee_kids_mobile/view_model/suivieService.dart';
import 'package:bee_kids_mobile/view_model/userService.dart';

class TableauDeBordScreen extends StatefulWidget {
  const TableauDeBordScreen({super.key});

  @override
  State<TableauDeBordScreen> createState() => _TableauDeBordScreenState();
}

class _TableauDeBordScreenState extends State<TableauDeBordScreen> {
  final eleveService _eleveService = eleveService();
  final SuivieService _suivieService = SuivieService();
  final classeService _classeService = classeService();
  final PostService _postService = PostService();
  final UserService _userService = UserService();
  final EventService _eventService = EventService();
  List<MauvaisSuivi> _suivisList = [];
  List<EleveSansSuivie> _elevesSansSuivieList = [];
  List<PostAvecInteractions> _postsAvecPlusInteractionsList = [];

  int _nombreTotaleUtilisateurs = 0;
  int _nombreTotaleParents = 0;
  int _nombreTotaleFormateurs = 0;
  int _nombreTotaleEleves = 0;
  int _nombreSuivieAujourdhui = 0;
  int _nombreTotaleClasses = 0;
  int _nombreTotalePosts = 0;
  Event? _eventPlusProche;
  List<InteractionUser> _topInteractions = [];

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }



  Future<void> _loadDashboardData() async {
    final utilisateurs = await _userService.getNombresUtilisateurs();
    final parents = await _userService.getNombresParents();
    final formateurs = await _userService.getNombresFormateurs();
    final eleves = await _eleveService.getNombresTotalesEleves();
    final suiviesAujourdhui = await _suivieService.getNombreSuivisPourAujourdhui();
    final posts = await _postService.getNombrePostsTotal();
    final classes = await _classeService.getNbTotalesClasse();
    final event = await _eventService.getEventPlusProche();
    final interactions = await _postService.getTopInteractions();
   final allSuivis = await _suivieService.getMauvaisSuivis();
   final elevesSansSuivie = await _suivieService.getElevesSansSuivie();
   final postsAvecPlusInteractions = await _postService.getPostsAvecPlusInteractions();
    setState(() {
      _suivisList = allSuivis;
      _elevesSansSuivieList = elevesSansSuivie;
      _postsAvecPlusInteractionsList = postsAvecPlusInteractions;
      _eventPlusProche = event;
      _topInteractions = interactions;
      _nombreTotaleUtilisateurs = utilisateurs;
      _nombreTotaleParents = parents;
      _nombreTotaleFormateurs = formateurs;
      _nombreTotaleEleves = eleves;
      _nombreSuivieAujourdhui = suiviesAujourdhui;
      _nombreTotalePosts = posts;
      _nombreTotaleClasses = classes;
      _isLoading = false;
    });
  }





Widget _buildSuivisList() {
  if (_suivisList.isEmpty) return const SizedBox();

  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      const SizedBox(height: 20),
      const Text(
        '⚠️ Suivis à Améliorer',
        style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold, color: Colors.redAccent),
      ),
      const SizedBox(height: 10),
      ..._suivisList.map((suivis) => _buildSuivisCard(suivis)).toList(),
    ],
  );
}

Widget _buildSuivisCard(MauvaisSuivi suivis) {
  // Liste des champs avec leur valeur
  final faibles = <String, String>{
    'Repas': suivis.repas,
    'Sommeil': suivis.sommeil,
    'Participation': suivis.participation,
    'Jeux Ext.': suivis.jeuxExterieur,
    'Interaction': suivis.interaction,
    'Humeur': suivis.humeur,
  };

  // On garde uniquement les champs faibles
  final champsFaibles = faibles.entries.where((e) => e.value.toLowerCase() == 'faible').toList();

  return Card(
    elevation: 4,
    margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
    child: Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.person, color: Colors.blue),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '👶 Élève : ${suivis.eleveNom}',
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              const Icon(Icons.school, color: Colors.green),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '👨‍🏫 Formateur : ${suivis.formateurNom}',
                  style: const TextStyle(fontSize: 16),
                ),
              ),
            ],
          ),
          const Divider(height: 20, thickness: 1.2),
          const Text(
            '⚠️ Faiblesses détectées:',
            style: TextStyle(fontSize: 15, fontWeight: FontWeight.bold, color: Colors.red),
          ),
          const SizedBox(height: 6),
          if (champsFaibles.isEmpty)
            const Text('✅ Aucun champ faible', style: TextStyle(color: Colors.green)),
          ...champsFaibles.map((entry) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Text(
                  '🔸 ${entry.key} : ${entry.value}',
                  style: const TextStyle(fontSize: 14),
                ),
              )),
        ],
      ),
    ),
  );
}

Widget _buildElevesSansSuivieList() {
  if (_elevesSansSuivieList.isEmpty) return const SizedBox();

  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      const SizedBox(height: 20),
      const Text(
        '⚠️ Élèves Sans Suivie Aujourd\'hui',
        style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold, color: Colors.orange),
      ),
      const SizedBox(height: 10),
      Card(
        elevation: 4,
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.warning, color: Colors.orange, size: 24),
                  const SizedBox(width: 8),
                  Text(
                    '${_elevesSansSuivieList.length} élève(s) sans suivie',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.orange,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              const Divider(thickness: 1.2),
              const SizedBox(height: 8),
              ..._elevesSansSuivieList.map((eleve) => _buildEleveSansSuivieCard(eleve)).toList(),
            ],
          ),
        ),
      ),
    ],
  );
}

Widget _buildEleveSansSuivieCard(EleveSansSuivie eleve) {
  return Container(
    margin: const EdgeInsets.symmetric(vertical: 4),
    padding: const EdgeInsets.all(12),
    decoration: BoxDecoration(
      color: Colors.orange.shade50,
      borderRadius: BorderRadius.circular(10),
      border: Border.all(color: Colors.orange.shade200),
    ),
    child: Row(
      children: [
        const Icon(Icons.person_outline, color: Colors.orange, size: 20),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                eleve.nomComplet,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  const Icon(Icons.class_, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    '${eleve.classe}',
                    style: const TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                  const SizedBox(width: 16),
                  const Icon(Icons.school, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      ': ${eleve.enseignant}',
                      style: const TextStyle(fontSize: 14, color: Colors.grey),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    ),
  );
}

Widget _buildPostsAvecPlusInteractionsList() {
  if (_postsAvecPlusInteractionsList.isEmpty) return const SizedBox();

  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      const SizedBox(height: 20),
      const Text(
        '🔥 Posts Ayant Plus d\'Interactions',
        style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold, color: Colors.deepPurple),
      ),
      const SizedBox(height: 10),
      Card(
        elevation: 4,
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.trending_up, color: Colors.deepPurple, size: 24),
                  const SizedBox(width: 8),
                  Text(
                    '${_postsAvecPlusInteractionsList.length} post(s) populaire(s)',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.deepPurple,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              const Divider(thickness: 1.2),
              const SizedBox(height: 8),
              ..._postsAvecPlusInteractionsList.take(5).map((post) => _buildPostAvecInteractionsCard(post)).toList(),
            ],
          ),
        ),
      ),
    ],
  );
}

Widget _buildPostAvecInteractionsCard(PostAvecInteractions post) {
  return Container(
    margin: const EdgeInsets.symmetric(vertical: 8),
    padding: const EdgeInsets.all(12),
    decoration: BoxDecoration(
      color: Colors.deepPurple.shade50,
      borderRadius: BorderRadius.circular(10),
      border: Border.all(color: Colors.deepPurple.shade200),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // En-tête avec auteur et date
        Row(
          children: [
            CircleAvatar(
              radius: 16,
              backgroundColor: Colors.deepPurple.shade100,
              backgroundImage: post.userPhotoUrl != null && post.userPhotoUrl!.isNotEmpty
                  ? NetworkImage(post.userPhotoUrl!)
                  : null,
              child: post.userPhotoUrl == null || post.userPhotoUrl!.isEmpty
                  ? const Icon(Icons.person, size: 16, color: Colors.deepPurple)
                  : null,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    post.authorName,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    post.createdAt,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.deepPurple,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${post.totalInteractions} interactions',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),

        // Contenu du post
        if (post.content.isNotEmpty) ...[
          Text(
            post.content,
            style: const TextStyle(fontSize: 14),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
        ],

        // Images du post (si disponibles)
        if (post.photoUrl.isNotEmpty) ...[
          SizedBox(
            height: 60,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: post.photoUrl.length > 3 ? 3 : post.photoUrl.length,
              itemBuilder: (context, index) {
                return Container(
                  margin: const EdgeInsets.only(right: 8),
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    image: DecorationImage(
                      image: NetworkImage(post.photoUrl[index]),
                      fit: BoxFit.cover,
                    ),
                  ),
                  child: post.photoUrl.length > 3 && index == 2
                      ? Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            color: Colors.black54,
                          ),
                          child: Center(
                            child: Text(
                              '+${post.photoUrl.length - 2}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        )
                      : null,
                );
              },
            ),
          ),
          const SizedBox(height: 8),
        ],

        // Statistiques d'interactions
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildInteractionStat(Icons.thumb_up, '${post.totalLikes}', 'Likes', Colors.blue),
            _buildInteractionStat(Icons.comment, '${post.totalCommentaires}', 'Commentaires', Colors.green),
            _buildInteractionStat(Icons.trending_up, '${post.totalInteractions}', 'Total', Colors.deepPurple),
          ],
        ),
      ],
    ),
  );
}

Widget _buildInteractionStat(IconData icon, String count, String label, Color color) {
  return Column(
    children: [
      Icon(icon, color: color, size: 16),
      const SizedBox(height: 2),
      Text(
        count,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: color,
        ),
      ),
      Text(
        label,
        style: const TextStyle(
          fontSize: 10,
          color: Colors.grey,
        ),
      ),
    ],
  );
}


Widget _buildDetailRow(String title, String value) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 2.0),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("$title: ", style: const TextStyle(fontWeight: FontWeight.w600)),
        Expanded(child: Text(value)),
      ],
    ),
  );
}

  Widget _buildEventProcheCard(Event event) {
    final String formattedDate = DateFormat('dd/MM/yyyy').format(event.dateEvent);

    return Card(
      elevation: 6,
      margin: const EdgeInsets.only(top: 20),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '📅 Événement le Plus Proche                 ',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.blueAccent,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              event.titre,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 6),
            Text('🗓️ Date: $formattedDate', style: const TextStyle(color: Colors.grey)),
            if (event.userNom != null && event.userPrenom != null)
              Text(
                '👤 Organisé par: ${event.userPrenom} ${event.userNom}',
                style: const TextStyle(color: Colors.grey),
              ),
            const SizedBox(height: 10),
            Text(event.description, maxLines: 3, overflow: TextOverflow.ellipsis),
            if (event.photoUrl != null && event.photoUrl!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 12),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    event.photoUrl!,
                    height: 120,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => const Icon(Icons.broken_image),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopInteractions(List<InteractionUser> users) {
    return Card(
      elevation: 6,
      margin: const EdgeInsets.only(top: 20),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '🏆 Utilisateurs les Plus Interactifs',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.deepOrange,
              ),
            ),
            const SizedBox(height: 12),
            for (var user in users.take(3))
              ListTile(
                contentPadding: EdgeInsets.zero,
                leading: const Icon(Icons.person, color: Colors.teal),
                title: Text('${user.fullName}'),
                subtitle: Text('Interactions: ${user.totalInteractions}'),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPieChart() {
    final dataMap = <String, double>{
      "Utilisateurs": _nombreTotaleUtilisateurs.toDouble(),
      "Parents": _nombreTotaleParents.toDouble(),
      "Formateurs": _nombreTotaleFormateurs.toDouble(),
      "Élèves": _nombreTotaleEleves.toDouble(),
      "Suivis Aujourd’hui": _nombreSuivieAujourdhui.toDouble(),
      "Posts": _nombreTotalePosts.toDouble(),
      "Classes": _nombreTotaleClasses.toDouble(),
    };

    final colorList = <Color>[
      Colors.green,
      Colors.orange,
      Colors.blueAccent,
      Colors.purple,
      Colors.teal,
      Colors.indigo,
      Colors.brown,
    ];

    return Card(
      elevation: 6,
      margin: const EdgeInsets.only(top: 20),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const Text(
              '📊 Statistiques Globales',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.green),
            ),
            const SizedBox(height: 20),
            PieChart(
              dataMap: dataMap,
              animationDuration: const Duration(milliseconds: 800),
              chartLegendSpacing: 32,
              chartRadius: MediaQuery.of(context).size.width / 2.2,
              colorList: colorList,
              initialAngleInDegree: 0,
              chartType: ChartType.disc,
              ringStrokeWidth: 32,
              legendOptions: const LegendOptions(
                showLegendsInRow: false,
                legendPosition: LegendPosition.bottom,
                showLegends: true,
                legendTextStyle: TextStyle(fontWeight: FontWeight.bold),
              ),
              chartValuesOptions: const ChartValuesOptions(
                showChartValueBackground: true,
                showChartValues: true,
                showChartValuesInPercentage: false,
                showChartValuesOutside: false,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    const primaryGreen = Color(0xFF25872C);
    const secondaryPink = Color(0xFFF18099);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: primaryGreen),
          onPressed: () => Navigator.pushNamed(context, '/directrice'),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        title: Row(
          children: [
            Image.asset('lib/resources/icons/logo.png', height: 40),
            const SizedBox(width: 10),
            RichText(
              text: const TextSpan(
                text: 'BEE-',
                style: TextStyle(
                  color: primaryGreen,
                  fontWeight: FontWeight.bold,
                  fontSize: 24,
                ),
                children: [
                  TextSpan(
                    text: 'KIDS',
                    style: TextStyle(
                      color: secondaryPink,
                      fontWeight: FontWeight.bold,
                      fontSize: 24,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '📊 Tableau de Bord',
                    style: TextStyle(
                      fontSize: 26,
                      fontWeight: FontWeight.bold,
                      color: primaryGreen,
                    ),
                  ),
                  _buildPieChart(),
                  if (_eventPlusProche != null) _buildEventProcheCard(_eventPlusProche!),
                  if (_topInteractions.isNotEmpty) _buildTopInteractions(_topInteractions),
                  _buildSuivisList(),
                  _buildElevesSansSuivieList(),
                  _buildPostsAvecPlusInteractionsList(),

                ],
              ),
            ),
      bottomNavigationBar: const MyFooter(currentRoute: '/directrice/tableau-de-bord'),
    );
  }
}
