class UserDTO {
  int userId;
  int conversationId;
  String fullName;
  String? photoUrl;

  UserDTO({
    required this.userId,
    required this.conversationId,
    required this.fullName,
    this.photoUrl,
  });

  // Convert a UserDTO object to a Map
  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'conversationId': conversationId,
      'fullName': fullName,
      'photoUrl': photoUrl,
    };
  }

  // Create a UserDTO object from a Map
  factory UserDTO.fromMap(Map<String, dynamic> map) {
    return UserDTO(
      userId: map['idUser'] ?? 0,
      conversationId: map['conversationId'] ?? 0,
      fullName: map['fullName'] ?? '',
      photoUrl: map['photoUrl'],
    );
  }
}
