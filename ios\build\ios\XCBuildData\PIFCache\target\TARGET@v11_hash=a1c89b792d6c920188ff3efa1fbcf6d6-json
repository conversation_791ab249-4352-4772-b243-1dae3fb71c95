{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f0db350d85ba0737ef311dcf97d4950c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984056b56ab63187e646b06f3cb56c7908", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f12af9291021c819d4d90952b69ee6f9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a9dd23bcdd71d5a828d50d5f4eca95fe", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f12af9291021c819d4d90952b69ee6f9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987ff122a591fe378bcab6c1be15b8394a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9845a50256e6f15ee6b5878712b5edf16f", "guid": "bfdfe7dc352907fc980b868725387e984543c3063ad52835ceb2ff24817fdae2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccd695de9c6d414146085b3cc3d98a4b", "guid": "bfdfe7dc352907fc980b868725387e980847f5816b9357381ecbe4c04b754afc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834e12c3e8744441cd3c737f28f5b4729", "guid": "bfdfe7dc352907fc980b868725387e98165ee53aa743e4c71b6ad2c8f46e5cc3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ae33d7c57a45fee4d2aafe64fca1547", "guid": "bfdfe7dc352907fc980b868725387e98fba4daef4d87ffada22d55cd581d80a9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894a0e2a18b1ac83bd0f32d0b80232084", "guid": "bfdfe7dc352907fc980b868725387e98fcf978ca3e48ce08ae9ec6a655b2ff7d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899f1b45655dd38d73e15e7e8760ef17a", "guid": "bfdfe7dc352907fc980b868725387e98de13b88dc42213e089cb971aa1061109", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b75f4225022b026237330b3d72b41be3", "guid": "bfdfe7dc352907fc980b868725387e983610e8b3659b4062ecaab09de182637f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f3033d03e54098553df50cb99725272", "guid": "bfdfe7dc352907fc980b868725387e98c9f5222e1485d46fbcf89299fa7d24e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876ee75f2b6b6daf6c6355f60a836a3f0", "guid": "bfdfe7dc352907fc980b868725387e9817af44cd682dd4457b369801f348bf82", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98744bdf65cf50b4b35230dea4065abf70", "guid": "bfdfe7dc352907fc980b868725387e98cad4d40ef398102e1a84157603cf4617", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b9f286e72c7029b53862cbceaefb379f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980f58c87bafac9d62ea9cbccd7f29235e", "guid": "bfdfe7dc352907fc980b868725387e98d85c1e47e19a6b2e344f54a8a976a821"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f9cebf4244c1731cf64f508570b392d", "guid": "bfdfe7dc352907fc980b868725387e9859f671b21e271ba45ffb91e60dfcf3ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4aca0b4ce25d82f7e4f4f6ec8327c15", "guid": "bfdfe7dc352907fc980b868725387e98904d6e47be6a0f2358c1d5dfdf1e1e75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989adf91818751829f77fffe6588769472", "guid": "bfdfe7dc352907fc980b868725387e9885469c8896c5cf1f90f1e8170625066d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb7b73808fe7b13cb31eba99d15d4ed5", "guid": "bfdfe7dc352907fc980b868725387e9858c4439af7ae96ec068721062e1e7217"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829ca8ab5fe7aa057e44b35e49e745496", "guid": "bfdfe7dc352907fc980b868725387e98fa6861c4ed378d34183250c672ea2c58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d88b7f3f92aafee2b796aa4e7becd723", "guid": "bfdfe7dc352907fc980b868725387e9860f0a4e675cfd78b273b17e9688f7bb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98319cb0917e48657d7f9b65d672cf85b3", "guid": "bfdfe7dc352907fc980b868725387e98a8bf092a35fe7df1d61457b739639bcb"}], "guid": "bfdfe7dc352907fc980b868725387e9835890100e1278d4912a0a960bc1e1798", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e989eb0b9349eae5cdf5963230d4af39bac"}], "guid": "bfdfe7dc352907fc980b868725387e98e62b94e67497d0d9d86e7bbf1288e19b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9837f145fa470c584eb77faec656d2013a", "targetReference": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04"}], "guid": "bfdfe7dc352907fc980b868725387e9839f52b65e3be06571da83c9298e2891a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04", "name": "video_player_avfoundation-video_player_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988a0a5b40b007f81bee1472e4d0fb23da", "name": "video_player_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b5f237537920ce49888f6f7f73c80a6c", "name": "video_player_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}