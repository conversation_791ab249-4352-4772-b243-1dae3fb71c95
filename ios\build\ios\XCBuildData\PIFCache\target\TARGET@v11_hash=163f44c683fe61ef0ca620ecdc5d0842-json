{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9828f3755cf0ac64513108d71532929d31", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseABTesting", "PRODUCT_NAME": "FirebaseABTesting", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985916d520f5f56979849aaedff181abb4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987a163d8a23d0cc62a142a1268fa57717", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting.modulemap", "PRODUCT_MODULE_NAME": "FirebaseABTesting", "PRODUCT_NAME": "FirebaseABTesting", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a3436f48e8131242495312ecc50f67c0", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987a163d8a23d0cc62a142a1268fa57717", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting.modulemap", "PRODUCT_MODULE_NAME": "FirebaseABTesting", "PRODUCT_NAME": "FirebaseABTesting", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989b53ecb82070ecfd5d8793768a462522", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98082b80204cfcaf1b0784b799a4ac8a20", "guid": "bfdfe7dc352907fc980b868725387e98e5cbf22b78729d2b4de12bfeedd46062"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879a9231651c234f70f32cb7bb5689060", "guid": "bfdfe7dc352907fc980b868725387e98b6ffc73b4dee0899302d03d9ad294c9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbad9d3948e893b312a10b54dafdba71", "guid": "bfdfe7dc352907fc980b868725387e98c78edec9a93172f73fd3a237e70b6ad9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de85d7527f7281582260275f827511e0", "guid": "bfdfe7dc352907fc980b868725387e98b0d27fa2fccb52e148a065e1976d532c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bf0bc251b023d27b1aa57430a88a6a0", "guid": "bfdfe7dc352907fc980b868725387e981971858eef673d931fe3ca461dba635f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf31da1f1db69f9424faf61178f38ad1", "guid": "bfdfe7dc352907fc980b868725387e9853c300f20530f83f852f0a52056a8935"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847144a602fda4bf7624c55249fbba50e", "guid": "bfdfe7dc352907fc980b868725387e98854676b5bacde9421d945889e3580f23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5012f35d2b286aab5383c47f2c7bd4c", "guid": "bfdfe7dc352907fc980b868725387e9871352bbc57dd8eeab87443c71ee369c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9fd71ce3ded032aad5a1f9fafb09b5e", "guid": "bfdfe7dc352907fc980b868725387e98df809944569e30de05375249f8d09c3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0e48abfdd39fb652223d35a382d5dfa", "guid": "bfdfe7dc352907fc980b868725387e98610d7897788005080d9026a1efa80d8e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d467a36be6af7dbc006a0f9de1136a31", "guid": "bfdfe7dc352907fc980b868725387e98858dba8b842fd61897a3c13a5536d17d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843a4dcfa406f7f9bbd257710505a98e0", "guid": "bfdfe7dc352907fc980b868725387e98cae4c49ba9208d772d0c157dae9c4c79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987df459bbaf9b97f97d2f00c40b9c5f53", "guid": "bfdfe7dc352907fc980b868725387e986de13dc3b888d64d60a0cd9929bd14c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98246d5a34aa5bfc59a6da2045b92e44c9", "guid": "bfdfe7dc352907fc980b868725387e9836f0fd154b51c4706aab834d73f7ae42", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9775f6d8329b53bcfb76de90f1220c2", "guid": "bfdfe7dc352907fc980b868725387e98cba95629492f7554ed5972c75c236b3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988deb70403f2846180cc9651f7c6a3ccf", "guid": "bfdfe7dc352907fc980b868725387e98ce2eb4e97b10de9de0d4f2d8f8745f0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857e5d4f00a140ca1c61d8b4ad0e20fd7", "guid": "bfdfe7dc352907fc980b868725387e9864ac782bdfc7fa73ab292cb8fe4f9f64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800da5d79e93a9ca0439fa3cd6abd3d73", "guid": "bfdfe7dc352907fc980b868725387e98860bcaa093b6e9015bc23b902c3b3702"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee0290b205d8860bd0298c8809c149c7", "guid": "bfdfe7dc352907fc980b868725387e98da94af3e31ba16e016488a5d2ff5f424", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981be85c749d49d55995faa0c6cb0809ec", "guid": "bfdfe7dc352907fc980b868725387e982509e07ced546112fd7a451c14c23228"}], "guid": "bfdfe7dc352907fc980b868725387e98c59971f8d6a5496fda26763731c8f176", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d28562dce0174960dee28cffe589b4d0", "guid": "bfdfe7dc352907fc980b868725387e983e787881cfd26a3bc998b92778309993"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d051e38b80f3eb67cf16969c125c6ec8", "guid": "bfdfe7dc352907fc980b868725387e98f65d57cd607b902acae67a7f1a793f7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884ff470f45aba6a6d353d514bb563fc3", "guid": "bfdfe7dc352907fc980b868725387e98804d08a7837ed0851993ece48c8659af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd207645851ffc7e58934b706cba81c7", "guid": "bfdfe7dc352907fc980b868725387e984bc91eda38f42c093340fc277b794abb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ec2628734c1a7455906efd2a819ad43", "guid": "bfdfe7dc352907fc980b868725387e98e2cb894f5cac1db3460b7a940c59785b"}], "guid": "bfdfe7dc352907fc980b868725387e989cb8d50555df2a0d6b42b28d5a2ae462", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e984f6e5b236acdf206068b7acda48296d6"}], "guid": "bfdfe7dc352907fc980b868725387e98f4d87662f7f4823f5e2642b824f8955c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e3e654df6dec1451db047541ba55dff4", "targetReference": "bfdfe7dc352907fc980b868725387e98b9cce05bc25808e2e3952904a4034443"}], "guid": "bfdfe7dc352907fc980b868725387e980686093db2c750405247b155a7ab77bf", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98b9cce05bc25808e2e3952904a4034443", "name": "FirebaseABTesting-FirebaseABTesting_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}], "guid": "bfdfe7dc352907fc980b868725387e984d1b80eb520d7ec9828b3cb4e14dcb65", "name": "FirebaseABTesting", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98388ecc0b6beee3823c42c78ba6025714", "name": "FirebaseABTesting.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}