import 'dart:convert';
import 'package:bee_kids_mobile/model/eleve.dart';
import 'package:bee_kids_mobile/view_model/eleveService.dart';
import 'package:bee_kids_mobile/view_model/tokenService.dart';
import 'package:bee_kids_mobile/resources/environnement/apiUrl.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:http/http.dart' as http;

// Mocks
class MockTokenService extends Mock implements TokenService {}

class MockClient extends Mock implements http.Client {}

void main() {
  late eleveService eleveServiceInstance;
  late MockTokenService mockTokenService;
  late MockClient mockClient;

  setUp(() {
    mockTokenService = MockTokenService();
    mockClient = MockClient();
    eleveServiceInstance = eleveService();

    // Mock du token
    when(mockTokenService.getToken())
        .thenAnswer((_) async => Future.value('fake_token'));

    // Mock de l'ID
    when(mockTokenService.getId()).thenAnswer((_) async => Future.value(1));
  });

  group('EleveService Tests', () {
    test('should return valid headers with token', () async {});

    test('should return list of Eleves by Parent ID', () async {
      final uri = Uri.parse('${ApiUrl.baseUrl}eleves/parParent/1');
      final responseJson = jsonEncode([
        {"id": 1, "nom": "John Doe", "age": 10},
        {"id": 2, "nom": "Jane Doe", "age": 12}
      ]);

      when(mockClient.get(uri, headers: anyNamed('headers'))).thenAnswer(
          (_) async => Future.value(http.Response(responseJson, 200)));

      final result = await eleveServiceInstance.getAllElevesByParentId();

      expect(result, isA<List<Eleve>>());
      expect(result.length, 2);
      expect(result.first.nom, "John Doe");
    });

    test('should return list of Eleves by Educateur ID', () async {
      final uri = Uri.parse('${ApiUrl.baseUrl}eleves/formateur/1');
      final responseJson = jsonEncode([
        {"id": 1, "nom": "John Doe", "age": 10},
        {"id": 2, "nom": "Jane Doe", "age": 12}
      ]);

      when(mockClient.get(uri, headers: anyNamed('headers'))).thenAnswer(
          (_) async => Future.value(http.Response(responseJson, 200)));

      final result = await eleveServiceInstance.getAllElevesByEducateurId();

      expect(result, isA<List<Eleve>>());
      expect(result.length, 2);
      expect(result.first.nom, "John Doe");
    });

    test('should return list of all Eleves', () async {
      final uri = Uri.parse('${ApiUrl.baseUrl}eleves/all');
      final responseJson = jsonEncode([
        {"id": 1, "nom": "John Doe", "age": 10},
        {"id": 2, "nom": "Jane Doe", "age": 12}
      ]);

      when(mockClient.get(uri, headers: anyNamed('headers'))).thenAnswer(
          (_) async => Future.value(http.Response(responseJson, 200)));

      final result = await eleveServiceInstance.getAllEleves();

      expect(result, isA<List<Eleve>>());
      expect(result.length, 2);
      expect(result.first.nom, "John Doe");
    });

    test('should return a single Eleve by ID', () async {
      final uri = Uri.parse('${ApiUrl.baseUrl}eleves/1');
      final responseJson = jsonEncode({"id": 1, "nom": "John Doe", "age": 10});

      when(mockClient.get(uri, headers: anyNamed('headers'))).thenAnswer(
          (_) async => Future.value(http.Response(responseJson, 200)));

      final result = await eleveServiceInstance.getEleveById(1);

      expect(result, isA<Eleve>());
      expect(result.nom, "John Doe");
    });

    test('should return a photo string by Eleve ID', () async {
      final uri = Uri.parse('${ApiUrl.baseUrl}api/eleves/photos/1');
      final responseString = "base64encodedImageString";

      when(mockClient.get(uri, headers: anyNamed('headers'))).thenAnswer(
          (_) async => Future.value(http.Response(responseString, 200)));

      final result = await eleveServiceInstance.getPhotoByEleveById(1);

      expect(result, isA<String>());
      expect(result, "base64encodedImageString");
    });

    test('should throw an exception when getEleveById fails', () async {
      final uri = Uri.parse('${ApiUrl.baseUrl}eleves/999');

      when(mockClient.get(uri, headers: anyNamed('headers'))).thenAnswer(
          (_) async => Future.value(http.Response('Not Found', 404)));

      expect(() async => await eleveServiceInstance.getEleveById(999),
          throwsException);
    });

    test('should throw an exception when getPhotoByEleveById fails', () async {
      final uri = Uri.parse('${ApiUrl.baseUrl}api/eleves/photos/999');

      when(mockClient.get(uri, headers: anyNamed('headers'))).thenAnswer(
          (_) async => Future.value(http.Response('Not Found', 404)));

      expect(() async => await eleveServiceInstance.getPhotoByEleveById(999),
          throwsException);
    });
  });
}
