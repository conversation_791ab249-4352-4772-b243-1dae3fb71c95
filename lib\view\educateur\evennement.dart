import 'dart:io';
import 'package:bee_kids_mobile/model/user.dart';
import 'package:bee_kids_mobile/view_model/userService.dart';
import 'package:flutter/material.dart';
import 'package:bee_kids_mobile/model/event.dart';
import 'package:bee_kids_mobile/view/educateur/footer.dart';
import 'package:bee_kids_mobile/view_model/evennementService.dart';
import 'package:bee_kids_mobile/view_model/tokenService.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'dart:convert';
import 'package:intl/date_symbol_data_local.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/material.dart' show ScrollDirection;

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Evennements',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: CalendarAppEducateur(),
    );
  }
}

class CalendarAppEducateur extends StatefulWidget {
  final DateTime? initialDate;

  const CalendarAppEducateur({Key? key, this.initialDate}) : super(key: key);

  @override
  _CalendarScreenState createState() => _CalendarScreenState();
}

class _CalendarScreenState extends State<CalendarAppEducateur> {
  final EventService _eventService = EventService();
  late Future<List<Event>> _eventsFuture;
  late DateTime _selectedDay;
  late DateTime _focusedDay;
    final UserService userService = UserService();
  TextEditingController _dateController = TextEditingController();
  ScrollController _scrollController = ScrollController();
  bool _isCalendarExpanded = true;
  String? name;
  Offset _fabPosition = Offset(20, 20);
  Set<DateTime> _eventDays = {}; // Store the days with events
  Map<DateTime, List<Event>> _allEvents =
      {}; // Store events for all dates persistently

  @override
  void initState() {
    super.initState();
    initializeDateFormatting('fr_FR', null);
      

    // Use the initialDate if provided, otherwise use current date
    _selectedDay = widget.initialDate ?? DateTime.now();
    _focusedDay = widget.initialDate ?? DateTime.now();

    _loadEventsForSelectedDate(_selectedDay);

    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  // Function to remove emojis and flags from text
  String _removeEmojisAndFlags(String text) {
    // Regular expression to detect emojis, flags, and special Unicode characters
    final emojiRegex = RegExp(
      r'[\u{1F600}-\u{1F64F}]|' // Emoticons
      r'[\u{1F300}-\u{1F5FF}]|' // Misc Symbols and Pictographs
      r'[\u{1F680}-\u{1F6FF}]|' // Transport and Map
      r'[\u{1F1E0}-\u{1F1FF}]|' // Regional indicator symbols (flags)
      r'[\u{2600}-\u{26FF}]|' // Misc symbols
      r'[\u{2700}-\u{27BF}]|' // Dingbats
      r'[\u{1F900}-\u{1F9FF}]|' // Supplemental Symbols and Pictographs
      r'[\u{1F018}-\u{1F270}]|' // Various symbols
      r'[\u{238C}-\u{2454}]|' // Various symbols
      r'[\u{20D0}-\u{20FF}]|' // Combining Diacritical Marks for Symbols
      r'[\u{FE00}-\u{FE0F}]', // Variation Selectors
      unicode: true,
    );

    return text.replaceAll(emojiRegex, '').trim();
  }

  // Validation function to check for emojis and flags in input
  String? _validateInputOptimized(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName ne peut pas être vide';
    }
    
    // Use a simpler regex for better performance
    if (value.contains(RegExp(r'[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F1E0}-\u{1F1FF}]', unicode: true))) {
      return '$fieldName ne peut pas contenir d\'emojis ou de symboles spéciaux';
    }
    
    return null;
  }

  void _scrollListener() {
    if (_scrollController.position.userScrollDirection ==
        ScrollDirection.reverse) {
      if (_isCalendarExpanded) {
        setState(() {
          _isCalendarExpanded = false;
        });
      }
    } else if (_scrollController.position.userScrollDirection ==
        ScrollDirection.forward) {
      if (!_isCalendarExpanded) {
        setState(() {
          _isCalendarExpanded = true;
        });
      }
    }
  }

  void _loadEventsForSelectedDate(DateTime date) async {
    // Use a more efficient date comparison
    final today = DateTime.now();
    final selectedDateOnly = DateTime(date.year, date.month, date.day);
    final todayOnly = DateTime(today.year, today.month, today.day);
    
    if (selectedDateOnly.isAtSameMomentAs(todayOnly)) {
      setState(() {
        _eventsFuture = _eventService.getEventsAfterToday();
      });
    } else {
      setState(() {
        _eventsFuture = _eventService.getEvenementsByDate(date);
      });
    }

    // Load events in background without blocking UI
    _eventsFuture.then((events) {
      if (mounted) {
        setState(() {
          _allEvents[date] = events;
          _eventDays = _allEvents.values
              .expand((e) => e)
              .map((event) => DateTime.utc(
                  event.dateEvent.year, event.dateEvent.month, event.dateEvent.day))
              .toSet();
        });
      }
    });
  }

  void _goToDate() {
    final inputDate = _dateController.text;
    try {
      DateTime parsedDate = DateFormat('yyyy-MM-dd').parseStrict(inputDate);
      setState(() {
        _focusedDay = parsedDate;
        _selectedDay = parsedDate;
      });
      _loadEventsForSelectedDate(parsedDate);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
        content: Text('Format de date invalide ! Veuillez utiliser la forme AAAA-MM-JJ'),
        backgroundColor: Colors.orange,
        behavior: SnackBarBehavior.floating,
      ));
    }
  }

  void _goToToday() {
    setState(() {
      _focusedDay = DateTime.now();
      _selectedDay = DateTime.now();
    });
    _loadEventsForSelectedDate(DateTime.now());
  }

  Future<void> _deleteEvent(int eventId) async {
    try {
      await _eventService.deleteEvenement(eventId);
      Navigator.pushNamed(context, '/educateur/Evennements');
      _loadEventsForSelectedDate(_selectedDay);
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
        content: Text('Événement supprimé avec succès.'),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ));
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Erreur lors de la suppression: $e'),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ));
    }
  }

  void _showDeleteConfirmationDialog(int eventId) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirmer la suppression'),
          content:
              const Text('Êtes-vous sûr de vouloir supprimer cet événement ?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child:
                  const Text('Annuler', style: TextStyle(color: Colors.blue)),
            ),
            TextButton(
              onPressed: () {
                _deleteEvent(eventId);
                Navigator.pushNamed(context, '/educateur/Evennements');
              },
              child:
                  const Text('Supprimer', style: TextStyle(color: Colors.pink)),
            ),
          ],
        );
      },
    );
  }

  void _showCreateEventDialog(DateTime selectedDate, {Event? existingEvent}) {
    final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
    final TextEditingController titleController = TextEditingController(
        text: existingEvent?.titre != null
            ? utf8.decode(latin1.encode(existingEvent!.titre))
            : '');
    final TextEditingController descriptionController = TextEditingController(
        text: existingEvent?.description != null
            ? utf8.decode(latin1.encode(existingEvent!.description))
            : '');
    File? selectedPhoto;
    bool _isSubmitting = false;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setDialogState) {
            Future<void> pickImage() async {
              if (_isSubmitting) return; // Prevent image picking during submission
            
              final picker = ImagePicker();
              final pickedFile = await picker.pickImage(
                source: ImageSource.gallery,
                maxWidth: 800,
                maxHeight: 600,
                imageQuality: 70,
              );
            
              if (pickedFile != null) {
                setDialogState(() {
                  selectedPhoto = File(pickedFile.path);
                });
              }
            }

            Future<void> _submitForm() async {
              if (_isSubmitting) return; // Prevent multiple submissions
            
              if (!_formKey.currentState!.validate()) {
                return;
              }

              // Set loading state
              setDialogState(() {
                _isSubmitting = true;
              });

              final String titre = titleController.text.trim();
              final String description = descriptionController.text.trim();

              // Double vérification pour les emojis avant l'envoi
              if (_validateInputOptimized(titre, 'Le titre') != null ||
                  _validateInputOptimized(description, 'La description') != null) {
                setDialogState(() {
                  _isSubmitting = false;
                });
                
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
                    content: Text(
                        'Le titre et la description ne peuvent pas contenir d\'emojis ou de symboles spéciaux.'),
                    backgroundColor: Colors.red,
                    behavior: SnackBarBehavior.floating,
                  ));
                }
                return;
              }

              try {
                if (existingEvent == null) {
                  final userId = await TokenService().getId();
                  await _eventService.createEvenement(
                    titre: titre,
                    description: description,
                    dateEvent: selectedDate,
                    userId: userId,
                    photo: selectedPhoto,
                  );
                } else {
                  await _eventService.updateEvenement(
                    id: existingEvent.id,
                    titre: titre,
                    description: description,
                    dateEvent: selectedDate,
                    photo: selectedPhoto,
                  );
                }

                // Close dialog immediately
                if (Navigator.canPop(context)) {
                  Navigator.of(context).pop();
                }
                
                // Show success message
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                    content: Text(
                      existingEvent == null
                          ? 'Événement créé avec succès.'
                          : 'Événement modifié avec succès.',
                    ),
                    backgroundColor: Colors.green,
                    behavior: SnackBarBehavior.floating,
                  ));
                }

                // Reload events after dialog is closed
                _loadEventsForSelectedDate(_selectedDay);

              } catch (e) {
                // Reset loading state on error
                if (mounted) {
                  setDialogState(() {
                    _isSubmitting = false;
                  });
                }
                
                String errorMessage = 'Erreur: $e';
                if (e.toString().contains('timeout') || e.toString().contains('Délai')) {
                  errorMessage = 'Délai d\'attente dépassé. Vérifiez votre connexion et réessayez.';
                } else if (e.toString().contains('Server error') || e.toString().contains('Erreur serveur')) {
                  errorMessage = 'Erreur du serveur. Réessayez plus tard.';
                }
                
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                    content: Text(errorMessage),
                    backgroundColor: Colors.red,
                    behavior: SnackBarBehavior.floating,
                    duration: const Duration(seconds: 5),
                  ));
                }
              }
            }

            return WillPopScope(
  onWillPop: () async => !_isSubmitting,
  child: AlertDialog(
    titlePadding: const EdgeInsets.only(left: 24, top: 24, right: 8),
    title: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          existingEvent == null ? 'Créer un événement' : 'Modifier un événement',
          style: const TextStyle(color: Colors.green),
        ),
        IconButton(
          icon: const Icon(Icons.close, color: Colors.red),
          onPressed: () => Navigator.of(context).pop(),
          tooltip: 'Fermer',
        ),
      ],
    ),
                content: SingleChildScrollView(
                  child: Form(
                    key: _formKey,
                    child: Column(
                      mainAxisSize: MainAxisSize.min, // Important: minimize dialog size
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextFormField(
                          controller: titleController,
                          enabled: !_isSubmitting, // Disable during submission
                          decoration: const InputDecoration(
                            labelText: 'Titre',
                            border: OutlineInputBorder(),
                          ),
                          validator: (value) => _validateInputOptimized(value, 'Le titre'),
                          autovalidateMode: AutovalidateMode.onUserInteraction,
                          onChanged: (value) {
                            // Trigger validation on each change
                            _formKey.currentState?.validate();
                          },
                        ),
                        const SizedBox(height: 10),
                        TextFormField(
                          controller: descriptionController,
                          enabled: !_isSubmitting, // Disable during submission
                          decoration: const InputDecoration(
                            labelText: 'Description',
                            border: OutlineInputBorder(),
                          ),
                          maxLines: 3,
                          validator: (value) => _validateInputOptimized(value, 'La description'),
                          autovalidateMode: AutovalidateMode.onUserInteraction,
                          onChanged: (value) {
                            // Trigger validation on each change
                            _formKey.currentState?.validate();
                          },
                        ),
                        const SizedBox(height: 10),
                        ListTile(
                          contentPadding: EdgeInsets.zero,
                          title: Text(
                            "Date de l'événement : ${DateFormat('yyyy-MM-dd').format(selectedDate)}",
                          ),
                        ),
                        ElevatedButton(
                          onPressed: _isSubmitting ? null : pickImage,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('Sélectionner une photo'),
                        ),
                        const SizedBox(height: 10),
                        if (selectedPhoto != null)
                          Container(
                            height: 150,
                            width: double.infinity,
                            child: Image.file(
                              selectedPhoto!,
                              fit: BoxFit.cover,
                            ),
                          ),
                        // Show loading indicator in content area instead of button
                        if (_isSubmitting)
                          const Padding(
                            padding: EdgeInsets.symmetric(vertical: 16.0),
                            child: Center(
                              child: Column(
                                children: [
                                  CircularProgressIndicator(
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
                                  ),
                                  SizedBox(height: 8),
                                  Text(
                                    'Création en cours...',
                                    style: TextStyle(color: Colors.grey),
                                  ),
                                ],
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
                actions: _isSubmitting 
                    ? [] // Hide action buttons during submission
                    : [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('Annuler'),
                        ),
                        ElevatedButton(
                          onPressed: _submitForm,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                          child: Text(existingEvent == null ? 'Créer' : 'Modifier'),
                        ),
                      ],
              ),
            );
          },
        );
      },
    );
  }

  Widget buildEventCard(Event event) {
    return FutureBuilder<User>(
        future: userService.fetchUsersById(),
        builder: (context, userSnapshot) {
          String encodedTitle =
              utf8.decode(latin1.encode(_removeEmojisAndFlags(event.titre)));
          String encodedDescription =
              utf8.decode(latin1.encode(_removeEmojisAndFlags(event.description)));
          String EncodedUserName =
              utf8.decode(latin1.encode(event.userNom!));
          String EncodedUserPrenom =
              utf8.decode(latin1.encode(event.userPrenom!));
          return FutureBuilder<String?>(
              future: TokenService().getUserName(),
              builder: (context, snapshot) {
                final currentUserName = userSnapshot.data?.nom;
                final bool isOwner = currentUserName == event.userNom;
                if (userSnapshot.hasData) {
                  print ("event data : ${event.toJson()}");
                  print('currentUserName: ${userSnapshot.data?.nom}');
                  print('event.userNom: ${event.userNom}');
                  print('isOwner: $isOwner');
                }
                return Card(
                  shape:
                      RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                  margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
                  child: ListTile(
                    leading: event.photoUrl != null &&
                            event.photoUrl!.isNotEmpty &&
                            Uri.tryParse(event.photoUrl!)?.hasScheme == true
                        ? Image.network(
                            event.photoUrl!,
                            width: 50,
                            height: 50,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return const Icon(Icons.event,
                                  size: 50, color: Colors.green);
                            },
                          )
                        : const Icon(Icons.event, size: 50, color: Colors.green),
                    title: Text(
                      encodedTitle,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Text(
                        '${encodedDescription}\nDate: ${DateFormat('yyyy-MM-dd').format(event.dateEvent)}\n Créé par: ${EncodedUserName} ${EncodedUserPrenom}'),
                    trailing: isOwner
                        ? Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                icon: const Icon(Icons.edit, color: Colors.orange),
                                onPressed: () => _showCreateEventDialog(
                                    event.dateEvent,
                                    existingEvent: event),
                              ),
                              IconButton(
                                icon: const Icon(Icons.delete, color: Colors.red),
                                onPressed: () =>
                                    _showDeleteConfirmationDialog(event.id),
                              ),
                            ],
                          )
                        : null,
                  ),
                );
              });
        });
  }  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    _fabPosition = Offset(screenSize.width - 70, screenSize.height - 230);

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pushNamed(context, '/educateur/menu'),
        ),
        title: const Text(
          'Calendrier des événements',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.green[800],
        actions: [
          IconButton(
            icon: const Icon(Icons.today),
            onPressed: _goToToday,
          ),
          IconButton(
            icon: Icon(_isCalendarExpanded
                ? Icons.keyboard_arrow_up
                : Icons.keyboard_arrow_down),
            onPressed: () {
              setState(() {
                _isCalendarExpanded = !_isCalendarExpanded;
              });
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          setState(() {
            _loadEventsForSelectedDate(_selectedDay);
          });
        },
        child: Stack(
          children: [
            Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: TextField(
                    controller: _dateController,
                    decoration: const InputDecoration(
                      labelText: 'Entrez une date (AAAA-MM-JJ)',
                      border: OutlineInputBorder(),
                    ),
                    onSubmitted: (value) => _goToDate(),
                  ),
                ),

                //  calendarFormat: CalendarFormat.week, // Use week view instead of month view
                AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  height: _isCalendarExpanded ? null : 0,
                  child: _isCalendarExpanded
                      ? TableCalendar(
                          firstDay: DateTime.utc(1000, 1, 1),
                          lastDay: DateTime.utc(9999, 1, 1),
                          focusedDay: _focusedDay,
                          selectedDayPredicate: (day) =>
                              isSameDay(_selectedDay, day),
                          locale: 'fr_FR',
                          headerStyle: HeaderStyle(
                            formatButtonVisible: false,
                            titleCentered: true,
                          ),
                          daysOfWeekStyle: DaysOfWeekStyle(
                            weekdayStyle: TextStyle(color: Colors.black),
                            weekendStyle: TextStyle(color: Colors.red),
                          ),
                          calendarStyle: CalendarStyle(
                            weekendTextStyle: TextStyle(color: Colors.red),
                            selectedDecoration: BoxDecoration(
                              color: Colors.green,
                              shape: BoxShape.circle,
                            ),
                            todayDecoration: BoxDecoration(
                              color: Colors.green.withOpacity(0.5),
                              shape: BoxShape.circle,
                            ),
                          ),
                          onDaySelected: (selectedDay, focusedDay) {
                            setState(() {
                              _selectedDay = selectedDay;
                              _focusedDay = focusedDay;
                            });
                            _loadEventsForSelectedDate(selectedDay);
                          },
                          calendarBuilders: CalendarBuilders(
                            markerBuilder: (context, date, events) {
                              if (_eventDays.contains(date)) {
                                return Positioned(
                                  bottom: 1,
                                  right: 1,
                                  child: Icon(
                                    Icons.circle,
                                    color: Colors.green,
                                    size: 8,
                                  ),
                                );
                              }
                              return SizedBox.shrink();
                            },
                          ),
                        )
                      : SizedBox.shrink(),
                ),

                Expanded(
                  child: FutureBuilder<List<Event>>(
                    future: _eventsFuture,
                    builder: (BuildContext context,
                        AsyncSnapshot<List<Event>> snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return const Center(
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
                          ),
                        );
                      } else if (snapshot.hasError) {
                        return const Center(
                          child: Text('Aucun événement trouvé pour cette date!')
                        );
                      } else {
                        return ListView.builder(
                          controller: _scrollController,
                          itemCount: snapshot.data!.length,
                          itemBuilder: (context, index) =>
                              buildEventCard(snapshot.data![index]),
                        );
                      }
                    },
                  ),
                )
              ],
            ),
            Positioned(
              left: _fabPosition.dx,
              top: _fabPosition.dy,
              child: _selectedDay.isBefore(DateTime(
                DateTime.now().year,
                DateTime.now().month,
                DateTime.now().day,
              ))
                  ? const SizedBox.shrink()
                  : FloatingActionButton(
                      onPressed: () => _showCreateEventDialog(_selectedDay),
                      backgroundColor: Colors.green,
                      child: const Icon(Icons.add),
                    ),
            ),
          ],
        ),
      ),
      bottomNavigationBar:
          const MyFooterEducateur(currentRoute: '/educateur/menu'),
    );
  }
}
