import 'dart:convert';
import 'package:bee_kids_mobile/model/FullConversationDTO.dart';
import 'package:bee_kids_mobile/model/UserConversation.dart';
import 'package:bee_kids_mobile/model/messagerie.dart';
import 'package:bee_kids_mobile/view_model/tokenService.dart';
import 'package:bee_kids_mobile/view_model/WebSocketService.dart';
import 'package:bee_kids_mobile/resources/environnement/apiUrl.dart';
import 'package:bee_kids_mobile/view_model/messagerieService.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:http/http.dart' as http;
import 'dart:async';

// Create mock classes
class MockTokenService extends Mock implements TokenService {
  @override
  Future<String?> getToken() async => 'fake_token';
}

class MockWebSocketService extends Mock implements WebSocketService {
  final StreamController<String> _controller =
      StreamController<String>.broadcast();

  @override
  Stream<String> get conversationUpdates => _controller.stream;

  @override
  void sendMessage(String destination, String message) {
    // Mock implementation
  }
}

class MockHttpClient extends Mock implements http.Client {}

// Mock HTTP response
class MockResponse extends Mock implements http.Response {
  final int statusCode;
  final String body;

  MockResponse(this.statusCode, this.body);
}

@GenerateMocks([http.Client])
void main() {
  late MessagerieService messagerieService;
  late MockTokenService mockTokenService;
  late MockWebSocketService mockWebSocketService;
  late http.Client mockClient;

  setUp(() {
    mockTokenService = MockTokenService();
    mockWebSocketService = MockWebSocketService();
    mockClient = MockHttpClient();

    // Create a custom implementation for testing
    messagerieService = MessagerieService();
    // We'll use mocked responses directly in each test
  });

  group('MessagerieService Tests', () {
    test('should send message successfully via API', () async {
      // Arrange
      final message = Message(
        senderId: "1",
        receiverId: "2",
        content: "Bonjour !",
        status: "sent",
        timestamp: "2024-03-17T12:00:00Z",
        read: false,
      );

      // Act & Assert
      // This is a simplified test that just verifies the method doesn't throw an exception
      // In a real test, you would use dependency injection to replace the HTTP client
      expect(() async => await messagerieService.sendMessage(message),
          returnsNormally);
    });

    test('should parse FullConversationDTO correctly', () {
      // Test the model parsing
      final json = {
        "content": "Hello",
        "status": "sent",
        "timestamp": "2024-03-17T12:00:00Z",
        "isRead": true
      };

      final dto = FullConversationDTO.fromJson(json);

      expect(dto.content, "Hello");
      expect(dto.status, "sent");
      expect(dto.timestamp, "2024-03-17T12:00:00Z");
      expect(dto.isRead, true);
    });

    test('should parse Message correctly', () {
      // Test the model parsing
      final json = {
        "senderId": "1",
        "receiverId": "2",
        "content": "Hello",
        "status": "sent",
        "timestamp": "2024-03-17T12:00:00Z",
        "read": true
      };

      final message = Message.fromJson(json);

      expect(message.senderId, "1");
      expect(message.receiverId, "2");
      expect(message.content, "Hello");
      expect(message.status, "sent");
      expect(message.timestamp, "2024-03-17T12:00:00Z");
      expect(message.read, true);
    });

    test('Message toJson should handle receiverId correctly', () {
      // Test the special case where receiverId is "0"
      final message = Message(
        senderId: "1",
        receiverId: "0",
        content: "Hello",
        status: "sent",
        timestamp: "2024-03-17T12:00:00Z",
        read: false,
      );

      final json = message.toJson();

      expect(json['receiverId'], null); // Should be null when receiverId is "0"
    });

    test('should validate userId format in getUserById', () {
      // Test the validation logic for getUserById
      expect(() => messagerieService.getUserById("abc"), throwsException);
      expect(() => messagerieService.getUserById("123abc"), throwsException);
      expect(() => messagerieService.getUserById(""), throwsException);

      // This would pass validation but might fail on the HTTP call
      // which we're not testing here
      expect(() => messagerieService.getUserById("123"), returnsNormally);
    });
  });
}
