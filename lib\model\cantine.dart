import 'package:json_annotation/json_annotation.dart';

@JsonSerializable()
class cantine {
  final int id;
  final String jour;
  final String entree;
  final String platPrincipale;
  final String dessert;
  final String photoUrl;

  cantine({
    required this.id,
    required this.jour,
    required this.entree,
    required this.platPrincipale,
    required this.dessert,
    required this.photoUrl,
  });

  factory cantine.fromJson(Map<String, dynamic> json) {
    return cantine(
      id: _parseId(json['id']),
      jour: json['jour'] as String? ?? '',
      entree: json['entree'] as String? ?? '',
      platPrincipale: json['platPrincipale'] as String? ?? '',
      dessert: json['dessert'] as String? ?? '',
      photoUrl: json['photoUrl'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'jour': jour,
      'entree': entree,
      'platPrincipale': platPrincipale,
      'dessert': dessert,
      'photoUrl': photoUrl,
    };
  }

  static int _parseId(dynamic id) {
    if (id == null) return 0;
    return (id is num) ? id.toInt() : 0;
  }
}
