import 'dart:convert';
import 'package:bee_kids_mobile/model/suivie.dart';
import 'package:bee_kids_mobile/view/parent/footer.dart';
import 'package:bee_kids_mobile/view_model/suivieService.dart';
import 'package:bee_kids_mobile/view_model/eleveService.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class SuivieConstants {
  static const String repas3 = 'faible';
  static const String repas2 = 'moyen';
  static const String repas1 = 'fort';

  static const String humeur1 = 'fort';
  static const String humeur2 = 'moyen';
  static const String humeur3 = 'faible';

  static const String sommeil1 = 'fort';
  static const String sommeil2 = 'moyen';
  static const String sommeil3 = 'faible';

  static const String jeux2 = 'non';
  static const String jeux1 = 'oui';

  static const String interaction1 = 'fort';
  static const String interaction2 = 'moyen';
  static const String interaction3 = 'faible';

  static const String participation1 = 'fort';
  static const String participation2 = 'moyen';
  static const String participation3 = 'faible';
}

class SuivieEnfantParent extends StatefulWidget {
  final int eleveId;
  final String dateDeNaissance;
  final String nom;
  final String prenom;

  const SuivieEnfantParent({
    Key? key,
    required this.eleveId,
    required this.dateDeNaissance,
    required this.nom,
    required this.prenom,
  }) : super(key: key);

  @override
  _SuivieEnfantState createState() => _SuivieEnfantState();
}

class _SuivieEnfantState extends State<SuivieEnfantParent> {
  String? selectedRepas;
  String? selectedHumeur;
  String? selectedSommeil;
  String? selectedJeux;
  String? selectedInteraction;
  String? selectedParticipation;
  String? selectedNote;

  late Future<List<Suivis>> suivisList;
  final SuivieService _suivieService = SuivieService();
  final eleveService _eleveService = eleveService();
  bool fetched = false;
  final TextEditingController _noteController = TextEditingController();
  final FocusNode _noteFocusNode = FocusNode();

  String selectedDate = DateFormat('yyyy-MM-dd').format(DateTime.now());
  Suivis? fetchedSuivis;

  @override
  void initState() {
    super.initState();
    _noteFocusNode.addListener(() {
      if (!_noteFocusNode.hasFocus) {
        selectedNote = _noteController.text;
      }
    });
    fetchSuivis();
  }

  @override
  void dispose() {
    _noteController.dispose();
    _noteFocusNode.dispose();
    super.dispose();
  }

  void fetchSuivis() {
    suivisList =
        _suivieService.getSuivisByEleveIdAndDate(widget.eleveId, selectedDate);
    suivisList.then((data) {
      if (data.isNotEmpty) {
        fetchedSuivis = data.first;
        setState(() {
          fetched = true;
          selectedRepas = fetchedSuivis!.repas;
          selectedHumeur = fetchedSuivis!.humeur;
          selectedSommeil = fetchedSuivis!.sommeil;
          selectedJeux = fetchedSuivis!.jeuxExterieur;
          selectedInteraction = fetchedSuivis!.interaction;
          selectedParticipation = fetchedSuivis!.participation;
          selectedNote = fetchedSuivis!.note;
          _noteController.text = utf8.decode(latin1.encode(selectedNote!));
        });
      } else {
        fetchedSuivis = null;
        setState(() {
          selectedRepas = null;
          selectedHumeur = null;
          selectedSommeil = null;
          selectedJeux = null;
          selectedInteraction = null;
          selectedParticipation = null;
          selectedNote = null;
          _noteController.clear();
        });
      }
    });
  }

  bool areFieldsUnchanged() {
    return fetchedSuivis != null &&
        selectedRepas == fetchedSuivis!.repas &&
        selectedHumeur == fetchedSuivis!.humeur &&
        selectedSommeil == fetchedSuivis!.sommeil &&
        selectedJeux == fetchedSuivis!.jeuxExterieur &&
        selectedInteraction == fetchedSuivis!.interaction &&
        selectedParticipation == fetchedSuivis!.participation &&
        _noteController.text == fetchedSuivis!.note;
  }

  String changed() {
    if (areFieldsUnchanged()) {
      return 'Aucun champ n\'a été modifié.';
    } else {
      return 'Au moins un champ a été modifié.';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () =>
              Navigator.pushNamed(context, '/parent/suivie_enfants'),
        ),
        backgroundColor: const Color(0xFF4CAF50),
        elevation: 0,
        title: Row(
          children: [
            FutureBuilder<String>(
              future: _eleveService.getPhotoByEleveById(widget.eleveId),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const CircularProgressIndicator(color: Colors.white);
                }
                if (snapshot.hasError || !snapshot.hasData) {
                  return Container(
                    width: 60,
                    height: 60,
                    color: Colors.grey[300],
                    child: const Icon(Icons.person, size: 40),
                  );
                }

                final photoData = jsonDecode(snapshot.data!);
                return ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    photoData['photoUrl'],
                    width: 60,
                    height: 60,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 60,
                        height: 60,
                        color: Colors.grey[300],
                        child: const Icon(Icons.person, size: 40),
                      );
                    },
                  ),
                );
              },
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${widget.nom} ${widget.prenom}',
                    style: const TextStyle(color: Colors.white, fontSize: 18),
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    widget.dateDeNaissance,
                    style: const TextStyle(color: Colors.white, fontSize: 14),
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Text(
                    "Date : $selectedDate",
                    style: const TextStyle(
                        fontSize: 16, fontWeight: FontWeight.bold),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                ElevatedButton(
                  onPressed: () async {
                    DateTime? pickedDate = await showDatePicker(
                        context: context,
                        initialDate: DateTime.now(),
                        firstDate: DateTime(2000),
                        lastDate: DateTime(2100),
                        cancelText: 'Annuler');

                    if (pickedDate != null) {
                      setState(() {
                        selectedDate =
                            DateFormat('yyyy-MM-dd').format(pickedDate);
                      });
                      fetchSuivis();
                    }
                  },
                  style:
                      ElevatedButton.styleFrom(backgroundColor: Colors.green),
                  child: const Text(
                    'Choisir une date',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: FutureBuilder<List<Suivis>>(
              future: suivisList,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (snapshot.hasError) {
                  return Center(child: Text('Error: ${snapshot.error}'));
                }

                // Si aucune donnée n'est trouvée
                if (!snapshot.hasData || snapshot.data!.isEmpty) {
                  return SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          buildCategoryCards(),
                          const SizedBox(height: 16),
                          Row(),
                        ],
                      ),
                    ),
                  );
                }

                // Si les données existent, affiche les suivis
                return SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        buildCategoryCards(),
                        const SizedBox(height: 16),
                        Row(),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
      bottomNavigationBar: const MyFooterParent(currentRoute: '/parent/menu'),
    );
  }

  Widget buildCategoryCards() {
    return Column(
      children: [
        buildCard(
          'Repas',
          [
            'lib/resources/images/suivieEnfant/Repas1.png',
            'lib/resources/images/suivieEnfant/Repas2.png',
            'lib/resources/images/suivieEnfant/Repas3.png',
          ],
          selectedRepas,
          (value) => setState(() => selectedRepas = value),
        ),
        buildCard(
          'Humeur',
          [
            'lib/resources/images/suivieEnfant/Humeur3.png',
            'lib/resources/images/suivieEnfant/Humeur2.png',
            'lib/resources/images/suivieEnfant/Humeur1.png',
          ],
          selectedHumeur,
          (value) => setState(() => selectedHumeur = value),
        ),
        buildCard(
          'Sommeil',
          [
            'lib/resources/images/suivieEnfant/Sommeil3.png',
            'lib/resources/images/suivieEnfant/Sommeil2.png',
            'lib/resources/images/suivieEnfant/Sommeil1.png',
          ],
          selectedSommeil,
          (value) => setState(() => selectedSommeil = value),
        ),
        buildCard(
          'Jeux extérieurs',
          [
            'lib/resources/images/suivieEnfant/jeux1.png',
            'lib/resources/images/suivieEnfant/jeux2.png',
          ],
          selectedJeux,
          (value) => setState(() => selectedJeux = value),
        ),
        buildCard(
          'Interaction avec les amis',
          [
            'lib/resources/images/suivieEnfant/interaction3.png',
            'lib/resources/images/suivieEnfant/interaction2.png',
            'lib/resources/images/suivieEnfant/interaction1.png',
          ],
          selectedInteraction,
          (value) => setState(() => selectedInteraction = value),
        ),
        buildCard(
          'Participation aux activités',
          [
            'lib/resources/images/suivieEnfant/participation3.png',
            'lib/resources/images/suivieEnfant/participation2.png',
            'lib/resources/images/suivieEnfant/participation1.png',
          ],
          selectedParticipation,
          (value) => setState(() => selectedParticipation = value),
        ),
        buildNoteCard(),
      ],
    );
  }

  Widget buildNoteCard() {
    return Card(
      color: Colors.white60,
      elevation: 5,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Note',
              style: TextStyle(
                color: Colors.green,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            TextField(
              controller: _noteController,
              focusNode: _noteFocusNode,
              readOnly: true,
              decoration: InputDecoration(
                hintText: 'Pas encore remplie...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: const BorderSide(color: Colors.green),
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget buildCard(String title, List<String> images, String? selectedValue,
      Function(String?) onChanged) {
    return Card(
      elevation: 5,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      color: Colors.white60,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text(
              title,
              style: const TextStyle(
                  color: Colors.green,
                  fontSize: 14,
                  fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            Row(
              children: List.generate(images.length, (index) {
                String value;
                switch (title) {
                  case 'Repas':
                    value = [
                      SuivieConstants.repas1,
                      SuivieConstants.repas2,
                      SuivieConstants.repas3
                    ][index];
                    break;
                  case 'Humeur':
                    value = [
                      SuivieConstants.humeur1,
                      SuivieConstants.humeur2,
                      SuivieConstants.humeur3
                    ][index];
                    break;
                  case 'Sommeil':
                    value = [
                      SuivieConstants.sommeil1,
                      SuivieConstants.sommeil2,
                      SuivieConstants.sommeil3
                    ][index];
                    break;
                  case 'Jeux extérieurs':
                    value = [
                      SuivieConstants.jeux1,
                      SuivieConstants.jeux2,
                    ][index];
                    break;
                  case 'Interaction avec les amis':
                    value = [
                      SuivieConstants.interaction1,
                      SuivieConstants.interaction2,
                      SuivieConstants.interaction3
                    ][index];
                    break;
                  case 'Participation aux activités':
                    value = [
                      SuivieConstants.participation1,
                      SuivieConstants.participation2,
                      SuivieConstants.participation3
                    ][index];
                    break;
                  default:
                    value = '';
                }
                return Expanded(
                  child: Column(
                    children: [
                      Image.asset(images[index]),
                      Radio<String>(
                        value: value,
                        groupValue: selectedValue,
                        onChanged: null,
                      ),
                    ],
                  ),
                );
              }),
            ),
          ],
        ),
      ),
    );
  }
}
