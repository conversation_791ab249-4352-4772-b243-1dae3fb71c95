#!/usr/bin/env python3
"""
Script de test pour envoyer des notifications WebSocket à l'application BeeKids Mobile
Ce script simule l'envoi de notifications depuis le backend vers l'application mobile
"""

import json
import time
import websocket
import threading
from datetime import datetime

# Configuration WebSocket (ajustez selon votre configuration)
WEBSOCKET_URL = "ws://localhost:8080/ws"  # Remplacez par votre URL WebSocket
USER_ID = 3  # ID de l'utilisateur test

class WebSocketNotificationSender:
    def __init__(self, url, user_id):
        self.url = url
        self.user_id = user_id
        self.ws = None
        self.connected = False
        
    def on_open(self, ws):
        print(f"✅ Connecté au serveur WebSocket: {self.url}")
        self.connected = True
        
        # S'abonner au channel de l'utilisateur
        subscribe_message = {
            "command": "SUBSCRIBE",
            "destination": f"/user/{self.user_id}/queue/mobile-notifications",
            "id": "sub-0"
        }
        self.send_stomp_message(subscribe_message)
        
    def on_message(self, ws, message):
        print(f"📨 Message reçu: {message}")
        
    def on_error(self, ws, error):
        print(f"❌ Erreur WebSocket: {error}")
        
    def on_close(self, ws, close_status_code, close_msg):
        print("🔌 Connexion WebSocket fermée")
        self.connected = False
        
    def send_stomp_message(self, message):
        """Envoie un message STOMP via WebSocket"""
        if self.ws and self.connected:
            stomp_frame = self.build_stomp_frame(message)
            self.ws.send(stomp_frame)
            print(f"📤 Message STOMP envoyé: {message}")
            
    def build_stomp_frame(self, message):
        """Construit une trame STOMP"""
        command = message.get("command", "SEND")
        destination = message.get("destination", "")
        body = json.dumps(message.get("body", {}))
        
        frame = f"{command}\n"
        if destination:
            frame += f"destination:{destination}\n"
        frame += f"content-type:application/json\n"
        frame += f"content-length:{len(body)}\n"
        frame += f"\n{body}\0"
        
        return frame
        
    def connect(self):
        """Se connecte au serveur WebSocket"""
        try:
            self.ws = websocket.WebSocketApp(
                self.url,
                on_open=self.on_open,
                on_message=self.on_message,
                on_error=self.on_error,
                on_close=self.on_close
            )
            
            # Démarrer la connexion dans un thread séparé
            wst = threading.Thread(target=self.ws.run_forever)
            wst.daemon = True
            wst.start()
            
            # Attendre la connexion
            timeout = 10
            while not self.connected and timeout > 0:
                time.sleep(0.5)
                timeout -= 0.5
                
            return self.connected
            
        except Exception as e:
            print(f"❌ Erreur de connexion: {e}")
            return False
            
    def send_test_notification(self, notification_type="test"):
        """Envoie une notification de test"""
        if not self.connected:
            print("❌ Pas de connexion WebSocket active")
            return False
            
        # Créer une notification de test
        test_notification = {
            "notificationId": int(time.time() * 1000),
            "userId": self.user_id,
            "content": f"Notification de test {notification_type}",
            "messageContent": f"Ceci est une notification de test envoyée à {datetime.now().strftime('%H:%M:%S')}",
            "isMenu": True,
            "isRead": False,
            "timeStamp": datetime.now().isoformat(),
            "notifcationCounter": int(time.time()) % 100,
            "is1to1Message": False,
            "isGroupMessage": False,
            "isNewPubReq": notification_type == "publication",
            "isNewPub": notification_type == "publication",
            "isSuivie": notification_type == "suivie",
            "isEvent": notification_type == "event",
            "messageCounter": 0,
            "senderName": "Test Sender"
        }
        
        # Message STOMP pour envoyer la notification
        stomp_message = {
            "command": "SEND",
            "destination": f"/user/{self.user_id}/queue/mobile-notifications",
            "body": test_notification
        }
        
        self.send_stomp_message(stomp_message)
        print(f"🧪 Notification de test envoyée: {notification_type}")
        return True
        
    def disconnect(self):
        """Se déconnecte du serveur WebSocket"""
        if self.ws:
            self.ws.close()

def main():
    """Fonction principale pour tester l'envoi de notifications"""
    print("🧪 === Test d'envoi de notifications WebSocket ===")
    print(f"URL: {WEBSOCKET_URL}")
    print(f"User ID: {USER_ID}")
    print()
    
    # Créer le sender
    sender = WebSocketNotificationSender(WEBSOCKET_URL, USER_ID)
    
    # Se connecter
    print("🔄 Connexion au serveur WebSocket...")
    if not sender.connect():
        print("❌ Impossible de se connecter au serveur WebSocket")
        print("💡 Vérifiez que le serveur backend est démarré et accessible")
        return
        
    print("✅ Connexion établie avec succès!")
    print()
    
    try:
        # Menu interactif
        while True:
            print("\n🎯 === Menu de Test ===")
            print("1. Envoyer notification générale")
            print("2. Envoyer notification de publication")
            print("3. Envoyer notification de suivie")
            print("4. Envoyer notification d'événement")
            print("5. Envoyer 5 notifications rapides")
            print("0. Quitter")
            
            choice = input("\nChoisissez une option (0-5): ").strip()
            
            if choice == "0":
                break
            elif choice == "1":
                sender.send_test_notification("general")
            elif choice == "2":
                sender.send_test_notification("publication")
            elif choice == "3":
                sender.send_test_notification("suivie")
            elif choice == "4":
                sender.send_test_notification("event")
            elif choice == "5":
                print("🚀 Envoi de 5 notifications rapides...")
                for i in range(5):
                    sender.send_test_notification(f"batch_{i+1}")
                    time.sleep(1)
                print("✅ 5 notifications envoyées!")
            else:
                print("❌ Option invalide")
                
    except KeyboardInterrupt:
        print("\n🛑 Interruption par l'utilisateur")
    finally:
        print("🔌 Déconnexion...")
        sender.disconnect()
        print("👋 Test terminé!")

if __name__ == "__main__":
    main()
