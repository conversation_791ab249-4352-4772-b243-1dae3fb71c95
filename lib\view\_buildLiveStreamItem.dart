import 'package:bee_kids_mobile/model/LiveStream.dart';
import 'package:bee_kids_mobile/view/LiveVideoScreen.dart';
import 'package:bee_kids_mobile/view/VideoPlayerScreen.dart';
import 'package:flutter/material.dart';

Widget _buildLiveStreamItem(LiveStream stream) {
  return GestureDetector(
    child: Container(
      width: 80, // Adjust width as needed
      margin: EdgeInsets.symmetric(horizontal: 5),
      child: Column(
        children: [
          Stack(
            alignment: Alignment.bottomRight,
            children: [
              CircleAvatar(
                radius: 30,
                backgroundImage: AssetImage(
                    'assets/images/profile_placeholder.png'), // Add user image later
              ),
              if (stream.isLive)
                Container(
                  padding: EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                  child: Text(
                    'LIVE',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: 5),
          Text(
            stream.userName,
            style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    ),
  );
}
