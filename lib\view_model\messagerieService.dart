import 'dart:async';
import 'dart:convert';
import 'package:bee_kids_mobile/model/FullConversationDTO.dart';
import 'package:bee_kids_mobile/model/UserConversation.dart';
import 'package:bee_kids_mobile/model/messagerie.dart';
import 'package:bee_kids_mobile/resources/environnement/apiUrl.dart';
import 'package:bee_kids_mobile/view_model/tokenService.dart';
import 'package:http/http.dart' as http;
import 'package:bee_kids_mobile/view_model/WebSocketService.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MessagerieService {
  final baseUrl = ApiUrl.baseUrl;
  final TokenService tokenService = TokenService();
  final WebSocketService webSocketService = WebSocketService();
  
  // Add a stream controller for message counter updates
  final _messageCounterController = StreamController<int>.broadcast();
  Stream<int> get messageCounterUpdates => _messageCounterController.stream;
  
  // Track total unread message count
  int _totalMessageCount = 0;
  int get totalMessageCount => _totalMessageCount;

  MessagerieService() {
    // Initialize message counter from shared preferences
    _loadMessageCounter();
    
    // Listen for updates from WebSocket
    webSocketService.conversationUpdates.listen((message) {
      print('Message reçu via WebSocket: $message');
      
      try {
        // Parse the message to check if it contains a message counter
        final data = jsonDecode(message);
        if (data.containsKey('messageCounter')) {
          int counter = data['messageCounter'] ?? 0;
          _updateMessageCounter(counter);
        }
      } catch (e) {
        print('Error parsing WebSocket message: $e');
      }
    });
  }
  
  // Load message counter from shared preferences
  Future<void> _loadMessageCounter() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _totalMessageCount = prefs.getInt('messageCounter') ?? 0;
      _messageCounterController.add(_totalMessageCount);
    } catch (e) {
      print('Error loading message counter: $e');
    }
  }
  
  // Update message counter and save to shared preferences
  Future<void> _updateMessageCounter(int count) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _totalMessageCount = count;
      await prefs.setInt('messageCounter', _totalMessageCount);
      _messageCounterController.add(_totalMessageCount);
    } catch (e) {
      print('Error updating message counter: $e');
    }
  }
  
  // Reset message counter when user views messages
  Future<void> resetMessageCounter() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _totalMessageCount = 0;
      await prefs.setInt('messageCounter', 0);
      _messageCounterController.add(0);
    } catch (e) {
      print('Error resetting message counter: $e');
    }
  }

  // Get headers with Authorization token
  Future<Map<String, String>> _getAuthHeaders() async {
    final token = await tokenService.getToken();
    if (token != null) {
      return {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };
    }
    return {
      'Content-Type': 'application/json',
    };
  }

  // Send a message via WebSocket and API
  Future<void> sendMessage(Message message) async {
    final url = Uri.parse('${baseUrl}messages/send');
    final headers = await _getAuthHeaders();
    final body = jsonEncode(message.toJson());

    // Send message using WebSocket for real-time communication
    webSocketService.sendMessage(
        '/app/messages', body); // Send the message via WebSocket

    // Debugging logs for headers and body
    print('Headers: $headers');
    print('Request Body: $body');

    final response = await http.post(
      url,
      headers: headers,
      body: body,
    );

    // Log the response status and body for debugging
    print('Response Status: ${response.statusCode}');
    print('Response Body: ${response.body}');

    if (response.statusCode != 200 && response.statusCode != 201) {
      throw Exception('Failed to send message: ${response.body}');
    } else {
      print('Message sent successfully!');
    }
  }

  Future<List<FullConversationDTO>> getConversation({
    required int userConnectedId,
    required int conversationId,
  }) async {
    final url = Uri.parse(
        '${baseUrl}messages/$conversationId?userConnectedId=$userConnectedId');
    final headers = await _getAuthHeaders();

    final response = await http.get(url, headers: headers);

    if (response.statusCode == 200) {
      // Décodage explicite à partir des bytes en UTF‑8 (même en l'absence d'en-tête correct)
      final decodedBody = utf8.decode(response.bodyBytes, allowMalformed: true);
      final List<dynamic> data = jsonDecode(decodedBody);

      print('Full Conversation Response: $data');

      return data.map((e) => FullConversationDTO.fromJson(e)).toList();
    } else {
      throw Exception('Failed to fetch messages: ${response.body}');
    }
  }

  // Obtenir les conversations d'un utilisateur
  Future<List<UserConversationDTO>> getUserConversations(String userId) async {
    final url = Uri.parse('${baseUrl}messages/conversations?userId=$userId');
    final headers = await _getAuthHeaders();
    final response = await http.get(url, headers: headers);

    if (response.statusCode == 200) {
      final List<dynamic> data = jsonDecode(response.body);

      // Log pour débogage
      print('Conversations reçues : $data');

      return data.map((e) {
        // Vérifiez si des champs essentiels sont absents
        if (e['conversationId'] == null || e['fullName'] == null) {
          print('Données de conversation invalides : $e');
        }
        return UserConversationDTO.fromJson(e);
      }).toList();
    } else {
      throw Exception(
          'Échec de la récupération des conversations: ${response.body}');
    }
  }

  // Mark a message as read
  Future<void> markAsRead(int conversationId) async {
    final url = Uri.parse('${baseUrl}messages/read/$conversationId');
    final headers = await _getAuthHeaders();

    final response = await http.put(url, headers: headers);
    
    // Reset message counter when marking messages as read
    if (response.statusCode == 200) {
      resetMessageCounter();
    }
  }

  Future<Map<String, dynamic>> getUserById(String userId) async {
    // Validate that userId is numeric or in the expected format
    if (!RegExp(r'^\d+$').hasMatch(userId)) {
      throw Exception('Invalid userId: $userId');
    }

    final url = Uri.parse('${baseUrl}users/$userId');
    final headers = await _getAuthHeaders();
    final response = await http.get(url, headers: headers);

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to fetch user details: ${response.body}');
    }
  }

  // Add this method to fetch paginated messages
  Future<Map<String, dynamic>> getConversationPaginated({
    required int userConnectedId,
    required int conversationId,
    int page = 0,
    int size = 10,
  }) async {
    final url = Uri.parse(
        '${baseUrl}messages/pagination/$conversationId?userConnectedId=$userConnectedId&page=$page&size=$size');
    final headers = await _getAuthHeaders();

    final response = await http.get(url, headers: headers);

    if (response.statusCode == 200) {
      // Decode the response body with UTF-8 encoding
      final decodedBody = utf8.decode(response.bodyBytes, allowMalformed: true);
      final Map<String, dynamic> data = jsonDecode(decodedBody);

      print('Paginated Conversation Response: $data');

      // Extract content (messages) and pagination metadata
      final List<dynamic> content = data['content'] ?? [];
      final bool hasNext = data['last'] != null ? !data['last'] : false;
      final int totalPages = data['totalPages'] ?? 0;
      final int totalElements = data['totalElements'] ?? 0;
    
      return {
        'messages': content.map((e) => FullConversationDTO.fromJson(e)).toList(),
        'hasNext': hasNext,
        'totalPages': totalPages,
        'totalElements': totalElements,
        'currentPage': page,
      };
    } else {
      throw Exception('Failed to fetch paginated messages: ${response.body}');
    }
  }
}
