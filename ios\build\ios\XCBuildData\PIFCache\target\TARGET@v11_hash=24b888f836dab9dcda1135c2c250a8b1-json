{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9897c249cb1dde38d3f425ac9464bbbbd5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e01de03b33854898401e279548d9f67a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982d4703d30030c01ad4edcafc6c04c9b2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989fc7a0edfa87bc1e00789c2377ac7cc7", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982d4703d30030c01ad4edcafc6c04c9b2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98170be42079079b9ec866b06766a3799c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98685af610e1e4cfe26795160098eb7b4d", "guid": "bfdfe7dc352907fc980b868725387e9889f032893e676a3f6bc05065b7e5f2d5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f098da3543f70884925c978cf33ca91", "guid": "bfdfe7dc352907fc980b868725387e989516e5e8a82ea3d9c94d873c01b7c5a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a67071b699d2c9cc2686c36f5b45da2f", "guid": "bfdfe7dc352907fc980b868725387e98472adce4ced5b59b57993a44caf9cfe3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef90c6bdb4c26b681d6f81046bc5da3b", "guid": "bfdfe7dc352907fc980b868725387e98e06d934bf8df98e5e977346992ba54aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc2b475d94da1626e621ff5112c94109", "guid": "bfdfe7dc352907fc980b868725387e989907f4fe17d291d371debb9f1dc53b62", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8efa1d7becb0a86eebf0ebd6e48adce", "guid": "bfdfe7dc352907fc980b868725387e98f56f3592a44ae128468ac15805cee995", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5dfe4469be0c73fecc1657db6d47e5b", "guid": "bfdfe7dc352907fc980b868725387e9872f538b1908fb9f906fab5ca6721945f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98852f1fd77d5ee7f3dc0d76d954718c23", "guid": "bfdfe7dc352907fc980b868725387e98eea39ee77137a187fdf133439ce10a59", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987eb8a669f6602b41a83fbac5c55f27d8", "guid": "bfdfe7dc352907fc980b868725387e982de5cb6e0e56960d594919e1109b8910"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815500d0ff403d2ad9c78f15c6d624f88", "guid": "bfdfe7dc352907fc980b868725387e9805992a8e62c460241d179b86a78a597c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98663615494a3b6c18c32f3d5d8f3e82c3", "guid": "bfdfe7dc352907fc980b868725387e988248acc89e567f7cbc7365d36711be38", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab7bfafa0109a16dcbc10c93d131a8e3", "guid": "bfdfe7dc352907fc980b868725387e9841b0ea7b059a2df6239ff85dda792269", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832ad09f7b85074a73ee74dc756df53f0", "guid": "bfdfe7dc352907fc980b868725387e985159e2d8a7e9b998612bbc620462b137", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987aa05fa40731bedd811361eee912f307", "guid": "bfdfe7dc352907fc980b868725387e9867fb0b53487cded952c1a344408fd42a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98926cc867ad1973eb8dbcc9e51fd3b7ba", "guid": "bfdfe7dc352907fc980b868725387e98d78a2761054c73063bcd9266a4cb1ce4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2dac5ce431e73c940912c18096c655f", "guid": "bfdfe7dc352907fc980b868725387e98766c1e75410815684919225d5ed2808a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878b6620c0f3f3413980b43639ca37805", "guid": "bfdfe7dc352907fc980b868725387e98af8a229bb2594063bd996b18eed1a185", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f80e3a381bba3455e19ec2bd62f6d95", "guid": "bfdfe7dc352907fc980b868725387e98516753765d9a0ca5e8aa8fc7daa914df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2a1cd658852ea16d2daea14b3c522ae", "guid": "bfdfe7dc352907fc980b868725387e98f057d6428607ccaca8778773eee747e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e95d62379ac5e04942e5a345c2b520f", "guid": "bfdfe7dc352907fc980b868725387e988a05a67acb9e3c41dfd04ef954a89c46", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847271428d0ab31e27af35f2234d88a7a", "guid": "bfdfe7dc352907fc980b868725387e987134ec2376f3c29617ce735830e1a90e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2d43c76b9a3281a2e9848f738d8879c", "guid": "bfdfe7dc352907fc980b868725387e9866b7787db6cbc74d2e859d3bc40a313a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839297daa95327737f9a1952e1344c251", "guid": "bfdfe7dc352907fc980b868725387e98b8a4cad22dceb2af430308bc4ee6dfcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bf809d3d63c423a7068b31e32ab7a3d", "guid": "bfdfe7dc352907fc980b868725387e986c4dc018ff2a89c26b4eed3081025079", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef1dd38619dc2120572511c82f7098b8", "guid": "bfdfe7dc352907fc980b868725387e984eee2ad00e167c0db8da8d7c4f065bea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98703e1a280c29497ace376af8ed4b2441", "guid": "bfdfe7dc352907fc980b868725387e980d956329462a3bde6977ea0c9525408d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e059f04c17fd50a9f00e9ee905106470", "guid": "bfdfe7dc352907fc980b868725387e98279ee4f1490e57acbad6832f9d7de7bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf2123d7a5efb1b02d961fb13b647655", "guid": "bfdfe7dc352907fc980b868725387e980ce8f1f1d77dd9693ab9b661a392abc8"}], "guid": "bfdfe7dc352907fc980b868725387e989f8022edb0ce02b1dd722b51fafc5036", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9843603c5ffacdf2b72fb8eaab8294371a", "guid": "bfdfe7dc352907fc980b868725387e98137b26aef58692502732d72d772dc661"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa057ac1c3dece8f140ce04f9a86807e", "guid": "bfdfe7dc352907fc980b868725387e98c2977e6c87a5dfbc279b38916ef572af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba3cf65b76e959e02891312db72409d7", "guid": "bfdfe7dc352907fc980b868725387e983035a25b24237eb41f88d073d2fe9d05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982790e13468f61aead21356ec0eeb1d56", "guid": "bfdfe7dc352907fc980b868725387e9894e10253d5113e7a66d69ef57cd5db3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98113368f1c263a098aaf04c5a6a62f0af", "guid": "bfdfe7dc352907fc980b868725387e98007fdce9ad71dfa97c2be4d92012ba3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cf648ee6ec11e468e53b71cf9639e07", "guid": "bfdfe7dc352907fc980b868725387e98d208f9b04464579dcaf6d7a70885f29e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3a2dc1af8d3c0737c2231c9fa07c793", "guid": "bfdfe7dc352907fc980b868725387e982edc8123e74c779a074fdc543a964c62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5a9ffeface41505531961e0b9848a6c", "guid": "bfdfe7dc352907fc980b868725387e982a7db6f8138957335c8b40cb6a1dbfdf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98277ee92e7c4b4a3c41f62233006fd6a3", "guid": "bfdfe7dc352907fc980b868725387e98da185738f1ebb78ae6481c25a63c2ded"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812f7269b6ed170977a5898164519191b", "guid": "bfdfe7dc352907fc980b868725387e98aa2128b2036697680ae636412289f0f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dbcec7d3bd655c17178c041233ae5b2", "guid": "bfdfe7dc352907fc980b868725387e986c30a84fc35cf0ba7b315972d770d325"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ce9c0ee496bdc0c74ca9eb36d8ed0ef", "guid": "bfdfe7dc352907fc980b868725387e982ec10db348df06aff49f9cb3f11e453a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842e7cf4a67e0af127d01e22138351b57", "guid": "bfdfe7dc352907fc980b868725387e98ede4410001331679544f0828796347f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef81b17a0744774a470d7cc53b9d0f99", "guid": "bfdfe7dc352907fc980b868725387e98e53ed9436bca8e516d6fa7785f5a0f01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983910a1668702b35c12ce65b0013fc0e3", "guid": "bfdfe7dc352907fc980b868725387e98ac928769a96a203003c552ef01cf5b81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bfaa139f90c58261958f515161e395c", "guid": "bfdfe7dc352907fc980b868725387e982828aeee6f41f42c321e39f718e8d5bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf79ee8067d4b43ea6b68b29e90e7e61", "guid": "bfdfe7dc352907fc980b868725387e986f2486b8619b770302a203b639b4bd6d"}], "guid": "bfdfe7dc352907fc980b868725387e98c9bf4203b763060e68f97960749577ed", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e9876d346e7b2f23d053297da24184f4284"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c66b5324530b90fee7ec191645484220", "guid": "bfdfe7dc352907fc980b868725387e980dee2d18f3679cbcf18936cff5e03b48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e56187f5d1ece87c9a12e28d3c1a5427", "guid": "bfdfe7dc352907fc980b868725387e989b02dec7a4b9edd333695466c97a5710"}], "guid": "bfdfe7dc352907fc980b868725387e9856cee66f1ec52ce328e22b4b57ef2c90", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98bc8be3db575b52bef459c671b8d1a96a", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98b4983de1fcefb9ff4b5a2c9d775740de", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}