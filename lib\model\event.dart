class Event {
  final int id;
  final DateTime? createdAt;
  final String description;
  final String photoType;
  final String? photoUrl;
  final String titre;
  final DateTime updatedAt;
  final int? userId;
  final DateTime dateEvent;
  final String? userNom;
  final String? userPrenom;

  Event({
    required this.id,
    required this.createdAt,
    required this.description,
    required this.photoType,
    required this.photoUrl,
    required this.titre,
    required this.updatedAt,
    this.userId,
    required this.dateEvent,
    required this.userNom,
    required this.userPrenom,
  });

  // Factory constructor to create an Event object from a JSON map
  factory Event.fromJson(Map<String, dynamic> json) {
    return Event(
      id: json['id'] as int,
      createdAt: _parseDate(json['created_at']),
      description: json['description'] ?? '',
      photoType: json['photo_type'] ?? '',
      photoUrl: json['photoUrl'] ?? '',
      titre: json['titre'] ?? '',
      updatedAt: _parseDate(json['updated_at'] ?? ''),
      userId: json['userId'] as int?,
      dateEvent: _parseDate(json['dateEvent']),
      userNom: json['userNom'] ?? '',
      userPrenom: json['userPrenom'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'created_at': createdAt?.toIso8601String().split('T').first,
      'description': description,
      'photo_type': photoType,
      'photoUrl': photoUrl,
      'titre': titre,
      'updated_at': updatedAt.toIso8601String().split('T').first,
      'userId': userId,
      'dateEvent': dateEvent.toIso8601String().split('T').first,
      'userNom': userNom,
      'userPrenom': userPrenom,
    };
  }

  // Helper function to safely parse date strings, ignoring the time part
  static DateTime _parseDate(String? dateStr) {
    if (dateStr == null || dateStr.isEmpty) {
      return DateTime.now().toLocal().copyWith(
          hour: 0, minute: 0, second: 0, millisecond: 0, microsecond: 0);
    }
    try {
      // Try parsing directly with DateTime.parse
      DateTime date = DateTime.parse(dateStr);
      return DateTime(
          date.year, date.month, date.day); // Ensure time part is discarded
    } catch (e) {
      // Handle unexpected formats, specifically when time zone or fractional seconds are missing
      try {
        // Attempt to add a default time zone if not present
        DateTime date = DateTime.parse('$dateStr+00:00');
        return DateTime(
            date.year, date.month, date.day); // Ensure time part is discarded
      } catch (e) {
        // Print the error and fallback to the current date
        print('Error parsing date: $dateStr');
        return DateTime.now().toLocal().copyWith(
            hour: 0, minute: 0, second: 0, millisecond: 0, microsecond: 0);
      }
    }
  }
}
