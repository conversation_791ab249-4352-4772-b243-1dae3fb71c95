/* import 'dart:convert';
import 'dart:io';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:bee_kids_mobile/view_model/tokenService.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Keep track of recently processed message IDs to avoid duplicates
// Use a more robust deduplication mechanism with timestamps
final Map<String, int> _processedMessageIds = <String, int>{};
// Use a more aggressive deduplication window
const int _deduplicationWindow = 600000; // 1 hour instead of 10 minutes

// Add a global cache to prevent duplicates even before SharedPreferences is loaded
final Set<String> _recentlyProcessedIds = <String>{};

// This must be a top-level function
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  try {
    // Initialize Firebase if needed
    try {
      Firebase.app();
      debugPrint("🔄 Firebase was already initialized for background handler");
    } catch (e) {
      await Firebase.initializeApp();
      debugPrint("🔄 Firebase initialized for background handler");
    }
    
    // Log message receipt
    debugPrint("📱 BACKGROUND NOTIFICATION RECEIVED ==================");
    debugPrint("📱 Message ID: ${message.messageId}");
    debugPrint("📱 Notification ID: ${message.data['notificationId']}");
    debugPrint("📱 Title: ${message.notification?.title ?? message.data['title']}");
    debugPrint("📱 Body: ${message.notification?.body ?? message.data['body']}");
    debugPrint("📱 Data: ${message.data}");
    debugPrint("📱 ================================================");
    
    // Skip empty messages
    if (message.data.isEmpty && message.notification == null) {
      debugPrint("⚠️ Received empty FCM message in background, ignoring");
      return;
    }
    
    // Extract notification ID for deduplication
    final String notificationId = message.data['notificationId'] ?? '';
    
    // Create a unique key for this notification
    final String dedupeKey;
    if (notificationId.isNotEmpty) {
      dedupeKey = notificationId;
    } else if (message.messageId != null && message.messageId!.isNotEmpty) {
      dedupeKey = message.messageId!;
    } else {
      // Create a stable key from the content
      final String content = message.data['content'] ?? '';
      final String title = message.notification?.title ?? message.data['title'] ?? '';
      final String body = message.notification?.body ?? message.data['body'] ?? '';
      final String combinedData = "$title-$body-$content";
      dedupeKey = combinedData.hashCode.toString();
    }
    
    // IMMEDIATE MEMORY CHECK - Before even accessing SharedPreferences
    if (_recentlyProcessedIds.contains(dedupeKey)) {
      debugPrint("🔄 Skipping duplicate notification (from memory cache): $dedupeKey");
      return;
    }

    // Add to memory cache immediately
    _recentlyProcessedIds.add(dedupeKey);
    // Limit the size of the memory cache
    if (_recentlyProcessedIds.length > 100) {
      _recentlyProcessedIds.remove(_recentlyProcessedIds.first);
    }
    
    // Get current time
    final int now = DateTime.now().millisecondsSinceEpoch;
    
    // Check SharedPreferences for persistent deduplication
    final prefs = await SharedPreferences.getInstance();
    final lastProcessedTime = prefs.getInt('notification_$dedupeKey') ?? 0;
    
    // Check if we've seen this notification recently (from shared prefs)
    if (lastProcessedTime > 0 && now - lastProcessedTime < _deduplicationWindow) {
      debugPrint("🔄 Skipping duplicate notification (from persistent storage): $dedupeKey (received ${now - lastProcessedTime}ms after previous)");
      return;
    }
    
    // Also check in-memory cache
    if (_processedMessageIds.containsKey(dedupeKey)) {
      final int lastProcessed = _processedMessageIds[dedupeKey]!;
      if (now - lastProcessed < _deduplicationWindow) {
        debugPrint("🔄 Skipping duplicate notification (from memory): $dedupeKey (received ${now - lastProcessed}ms after previous)");
        return;
      }
    }
    
    // Update both the in-memory cache and persistent storage
    _processedMessageIds[dedupeKey] = now;
    await prefs.setInt('notification_$dedupeKey', now);
    
    // Clean up old entries from the deduplication map
    _processedMessageIds.removeWhere((key, timestamp) => now - timestamp > _deduplicationWindow);
    
    // Extract data - check if it's nested or direct
    Map<String, dynamic> notificationData = Map<String, dynamic>.from(message.data);
    
    // Try to handle nested data structure
    if (notificationData.containsKey('data')) {
      var nestedData = notificationData['data'];
      if (nestedData is String) {
        try {
          // Try to parse if it's a JSON string
          var parsedData = jsonDecode(nestedData);
          if (parsedData is Map) {
            notificationData = Map<String, dynamic>.from(parsedData);
            debugPrint("📱 Parsed nested data from JSON string");
          }
        } catch (e) {
          debugPrint("📱 Failed to parse nested data as JSON: $e");
        }
      } else if (nestedData is Map) {
        notificationData = Map<String, dynamic>.from(nestedData);
        debugPrint("📱 Using nested data map");
      }
    }
    
    // For Android, we need to manually create a notification in the background
    final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = 
        FlutterLocalNotificationsPlugin();
    
    // Create the Android notification channel if needed
    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      'high_importance_channel',
      'High Importance Notifications',
      description: 'This channel is used for important notifications.',
      importance: Importance.max,
    );
    
    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);
    
    // Extract notification title and body
    String title = message.notification?.title ?? 
                  message.data['title'] ?? 
                  'BeeKids';
                  
    String body = message.notification?.body ?? 
                  message.data['body'] ?? 
                  message.data['content'] ?? 
                  message.data['messageContent'] ?? 
                  'Nouvelle notification';
    
    // If both title and body are default values and we have no data,
    // this is likely a malformed message - skip it
    if (title == 'BeeKids' && body == 'Nouvelle notification' && message.data.isEmpty) {
      debugPrint("⚠️ Skipping notification with default values and no data");
      return;
    }
    
    // Use a consistent notification ID based on the notification ID from the server
    int notificationIdInt;
    try {
      notificationIdInt = int.parse(notificationId);
    } catch (e) {
      // If parsing fails, use a hash of the dedupeKey
      notificationIdInt = dedupeKey.hashCode;
    }
    
    // Add more detailed logging before showing the notification
    debugPrint("🔔 PREPARING TO DISPLAY BACKGROUND NOTIFICATION:");
    debugPrint("🔔 Notification ID: $notificationIdInt");
    debugPrint("🔔 Title: $title");
    debugPrint("🔔 Body: $body");
    
    // CRITICAL FIX: Cancel ALL existing notifications first
    // This is more aggressive but ensures no duplicates
    if (Platform.isAndroid) {
      try {
        // Cancel the specific notification first
        await flutterLocalNotificationsPlugin.cancel(notificationIdInt);
        
        // NEW: On Android, also try to cancel any notification with the same tag
        // This requires the Android-specific implementation
        final androidPlugin = flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();
        if (androidPlugin != null) {
          await androidPlugin.cancel(notificationIdInt);
        }
      } catch (e) {
        debugPrint("Error cancelling previous notification: $e");
      }
    } else {
      // For iOS, just cancel the specific notification
      await flutterLocalNotificationsPlugin.cancel(notificationIdInt);
    }
    
    // NEW: Add a small delay to ensure cancellation completes
    await Future.delayed(Duration(milliseconds: 100));
    
    // Show the notification with grouping for Android
    await flutterLocalNotificationsPlugin.show(
      notificationIdInt,  // Use consistent ID
      title,
      body,
      NotificationDetails(
        android: AndroidNotificationDetails(
          'high_importance_channel',
          'High Importance Notifications',
          channelDescription: 'This channel is used for important notifications.',
          importance: Importance.max,
          priority: Priority.high,
          showWhen: true,
          enableVibration: true,
          playSound: true,
          icon: '@mipmap/ic_launcher',
          // NEW: Add grouping to prevent multiple similar notifications
          groupKey: 'com.beekids.app.NOTIFICATIONS',
          setAsGroupSummary: false,
          // NEW: Add a tag to help with cancellation
          tag: 'notification_$notificationIdInt',
        ),
        iOS: const DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        ),
      ),
      payload: jsonEncode(notificationData),
    );
    
    // Add a very clear log message confirming the notification was displayed
    debugPrint("✅ BACKGROUND NOTIFICATION DISPLAYED ✅");
    debugPrint("✅ ID: $notificationIdInt");
    debugPrint("✅ Title: $title");
    debugPrint("✅ Body: $body");
    debugPrint("✅ Time: ${DateTime.now().toString()}");
    debugPrint("✅ ================================================");
    
  } catch (e, stackTrace) {
    // Log any errors that occur
    debugPrint("❌ ERROR IN BACKGROUND HANDLER: $e");
    debugPrint("❌ STACK TRACE: $stackTrace");
  }
}

class BackgroundServiceHandler {
  static Future<void> initializeService() async {
    // Ensure Firebase is initialized for background handlers
    await Firebase.initializeApp();
  }

  @pragma('vm:entry-point')
  static Future<bool> onIosBackground(ServiceInstance service) async {
    // Initialize Firebase in the background
    try {
      await Firebase.initializeApp();
      debugPrint("Firebase initialized in iOS background");
       
      final tokenService = TokenService();
      final userId = await tokenService.getId();
      
      return true;
    } catch (e) {
      debugPrint("Error in iOS background handler: $e");
      return false;
    }
  }

  @pragma('vm:entry-point')
  static void onStart(ServiceInstance service) async {
    final tokenService = TokenService();
    final userId = await tokenService.getId();
  }
}
 */

import 'dart:convert';
import 'dart:io';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:bee_kids_mobile/view_model/tokenService.dart';
import 'package:shared_preferences/shared_preferences.dart';

final Map<String, int> _processedMessageIds = <String, int>{};
const int _deduplicationWindow = 600000; // 10 minutes
final Set<String> _recentlyProcessedIds = <String>{};

@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  try {
    // Initialize Firebase if needed
    try {
      Firebase.app();
      debugPrint("🔄 Firebase was already initialized for background handler");
    } catch (e) {
      await Firebase.initializeApp();
      debugPrint("🔄 Firebase initialized for background handler");
    }

    // ✅ Skip auto-shown notifications on Android
    if (Platform.isAndroid && message.notification != null) {
      debugPrint("⚠️ Skipping auto-shown notification from Android system.");
      return;
    }

    // Skip empty messages
    if (message.data.isEmpty && message.notification == null) {
      debugPrint("⚠️ Received empty FCM message in background, ignoring");
      return;
    }

    final String notificationId = message.data['notificationId'] ?? '';
    final String dedupeKey;

    if (notificationId.isNotEmpty) {
      dedupeKey = notificationId;
    } else if (message.messageId != null && message.messageId!.isNotEmpty) {
      dedupeKey = message.messageId!;
    } else {
      final String content = message.data['content'] ?? '';
      final String title = message.data['title'] ?? '';
      final String body = message.data['body'] ?? '';
      final String combinedData = "$title-$body-$content";
      dedupeKey = combinedData.hashCode.toString();
    }

    if (_recentlyProcessedIds.contains(dedupeKey)) {
      debugPrint("🔄 Skipping duplicate notification (memory): $dedupeKey");
      return;
    }
    _recentlyProcessedIds.add(dedupeKey);
    if (_recentlyProcessedIds.length > 100) {
      _recentlyProcessedIds.remove(_recentlyProcessedIds.first);
    }

    final int now = DateTime.now().millisecondsSinceEpoch;
    final prefs = await SharedPreferences.getInstance();
    final lastProcessedTime = prefs.getInt('notification_$dedupeKey') ?? 0;

    if (lastProcessedTime > 0 &&
        now - lastProcessedTime < _deduplicationWindow) {
      debugPrint("🔄 Skipping duplicate (persistent): $dedupeKey");
      return;
    }

    if (_processedMessageIds.containsKey(dedupeKey)) {
      final int lastProcessed = _processedMessageIds[dedupeKey]!;
      if (now - lastProcessed < _deduplicationWindow) {
        debugPrint("🔄 Skipping duplicate (memory time): $dedupeKey");
        return;
      }
    }

    _processedMessageIds[dedupeKey] = now;
    await prefs.setInt('notification_$dedupeKey', now);
    _processedMessageIds
        .removeWhere((key, ts) => now - ts > _deduplicationWindow);

    // Flatten nested data if needed
    Map<String, dynamic> notificationData =
        Map<String, dynamic>.from(message.data);
    if (notificationData.containsKey('data')) {
      var nestedData = notificationData['data'];
      try {
        if (nestedData is String) {
          notificationData = Map<String, dynamic>.from(jsonDecode(nestedData));
        } else if (nestedData is Map) {
          notificationData = Map<String, dynamic>.from(nestedData);
        }
      } catch (_) {
        debugPrint("⚠️ Failed to parse nested data");
      }
    }

    final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
        FlutterLocalNotificationsPlugin();

    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      'high_importance_channel',
      'High Importance Notifications',
      description: 'This channel is used for important notifications.',
      importance: Importance.max,
    );

    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);

    final String title = message.data['title'] ?? 'BeeKids';
    final String body = message.data['body'] ??
        message.data['content'] ??
        message.data['messageContent'] ??
        'Nouvelle notification';

    if (title == 'BeeKids' &&
        body == 'Nouvelle notification' &&
        message.data.isEmpty) {
      debugPrint("⚠️ Skipping notification with default values and no data");
      return;
    }

    int notificationIdInt;
    try {
      notificationIdInt = int.parse(notificationId);
    } catch (_) {
      notificationIdInt = dedupeKey.hashCode;
    }

    debugPrint(
        "🔔 Showing notification: ID=$notificationIdInt | Title=$title | Body=$body");

    if (Platform.isAndroid) {
      try {
        await flutterLocalNotificationsPlugin.cancel(notificationIdInt);
        final androidPlugin = flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin>();
        await androidPlugin?.cancel(notificationIdInt);
      } catch (e) {
        debugPrint("❌ Error cancelling existing Android notification: $e");
      }
    } else {
      await flutterLocalNotificationsPlugin.cancel(notificationIdInt);
    }

    await Future.delayed(const Duration(milliseconds: 100));

    await flutterLocalNotificationsPlugin.show(
      notificationIdInt,
      title,
      body,
      NotificationDetails(
        android: AndroidNotificationDetails(
          'high_importance_channel',
          'High Importance Notifications',
          channelDescription:
              'This channel is used for important notifications.',
          importance: Importance.max,
          priority: Priority.high,
          showWhen: true,
          enableVibration: true,
          playSound: true,
          icon: '@mipmap/ic_launcher',
          groupKey: 'com.beekids.app.NOTIFICATIONS',
          tag: 'notification_$notificationIdInt',
        ),
        iOS: const DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        ),
      ),
      payload: jsonEncode(notificationData),
    );

    debugPrint("✅ Notification displayed ✅");
  } catch (e, stack) {
    debugPrint("❌ ERROR in background handler: $e");
    debugPrint("❌ STACK TRACE: $stack");
  }
}

class BackgroundServiceHandler {
  static Future<void> initializeService() async {
    await Firebase.initializeApp();
  }

  @pragma('vm:entry-point')
  static Future<bool> onIosBackground(ServiceInstance service) async {
    try {
      await Firebase.initializeApp();
      debugPrint("📱 Firebase initialized in iOS background");

      final tokenService = TokenService();
      final userId = await tokenService.getId();

      return true;
    } catch (e) {
      debugPrint("❌ Error in iOS background: $e");
      return false;
    }
  }

  @pragma('vm:entry-point')
  static void onStart(ServiceInstance service) async {
    final tokenService = TokenService();
    final userId = await tokenService.getId();
    debugPrint("📱 Service started for user $userId");
  }
}
