name: bee_kids_mobile
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.1.1+39

environment:
  sdk: ^3.5.4

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  image_picker: ^1.1.2
  file_picker: ^8.1.7
  chewie: ^1.3.0 # Pour afficher et lire des vidéos
  video_player: ^2.5.1 # Requis par Chewie
  flutter_pdfview: ^1.3.4 # Pour visualiser les PDF
  dio: ^5.8.0+1 # Pour télécharger des fichiers

  json_annotation: ^4.7.0
  shared_preferences: ^2.5.3
  get_it: ^7.2.0
  mime: ^1.0.0
  path_provider: ^2.1.5
  cached_network_image: ^3.2.3
  stomp_dart_client: ^1.0.0
  web_socket_channel: ^2.4.0
  carousel_slider: ^5.1.1
  url_launcher: ^6.1.7
  permission_handler: ^12.0.0+1
  expansion_tile_card: ^3.0.0
  #awesome_notifications: ^0.10.0
  flutter_inapp_notifications: ^1.0.0
  flutter_vlc_player: ^7.4.0
  image_gallery_saver:
    path: ./packages/image_gallery_saver-1.7.1
  pie_chart: ^5.3.2

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  http: any
  get: any

  http_parser: any
  table_calendar: ^3.1.3
  flutter_calendar_carousel: ^2.4.4
  provider: ^6.1.1
  overlay_support: ^2.1.0
  workmanager: ^0.5.2
  flutter_background_service: ^5.1.0
  flutter_local_notifications: ^18.0.1
  firebase_core: ^3.13.0
  firebase_messaging: ^15.2.5
  firebase_analytics: ^11.4.5
  firebase_remote_config: ^5.0.2
  open_file: ^3.5.10
  # camera: ^0.11.1
  image: ^4.0.17
  flutter_image_compress: ^2.4.0
dev_dependencies:
  flutter_test:
    sdk: flutter
  test: any
  mockito: ^5.0.16
  mocktail: ^1.0.4
  http: any
  build_runner: ^2.3.3
  logger: ^2.0.1

  generator_test: ^0.3.1
  json_serializable: ^6.6.0
  flutter_launcher_icons: ^0.14.2
flutter_icons:
  image_path: "lib/resources/images/logo.png"
  android: true
  ios: true
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - lib/resources/images/login_bg.png
    - lib/resources/icons/Right_Arrow_Icon.png
    - lib/resources/images/kids/image1.png
    - lib/resources/images/kids/image2.png
    - lib/resources/images/kids/image3.png
    - lib/resources/images/kids/image4.png
    - lib/resources/images/kids/image5.png
    - lib/resources/images/kids/image6.png
    - lib/resources/images/kids/image7.png
    - lib/resources/images/kids/image8.png
    - lib/resources/images/kids/image9.png
    - lib/resources/images/kids/image10.png
    - lib/resources/images/kids/image11.png
    - lib/resources/images/kids/image12.png
    - lib/resources/images/suivieEnfant/Humeur1.png
    - lib/resources/images/suivieEnfant/Humeur2.png
    - lib/resources/images/suivieEnfant/Humeur3.png
    - lib/resources/images/suivieEnfant/interaction1.png
    - lib/resources/images/suivieEnfant/interaction2.png
    - lib/resources/images/suivieEnfant/interaction3.png
    - lib/resources/images/suivieEnfant/jeux1.png
    - lib/resources/images/suivieEnfant/jeux2.png
    - lib/resources/images/suivieEnfant/participation1.png
    - lib/resources/images/suivieEnfant/participation2.png
    - lib/resources/images/suivieEnfant/participation3.png
    - lib/resources/images/suivieEnfant/Repas1.png
    - lib/resources/images/suivieEnfant/Repas2.png
    - lib/resources/images/suivieEnfant/Repas3.png
    - lib/resources/images/suivieEnfant/Sommeil1.png
    - lib/resources/images/suivieEnfant/Sommeil2.png
    - lib/resources/images/suivieEnfant/Sommeil3.png
    - lib/resources/images/cantine/image1.png
    - lib/resources/images/cantine/image2.png
    - lib/resources/images/cantine/image3.png
    - lib/resources/images/cantine/image4.png
    - lib/resources/images/cantine/image5.png
    - lib/resources/images/cantine/image6.png
    - lib/resources/images/avatar_placeholder.png
      # logo beekids
    - lib/resources/images/logo.png
    # avatar girl
    - lib/resources/images/avatar_girl.png
    # post image
    - lib/resources/images/post 1.png
    - lib/resources/images/post 2.png
    - lib/resources/images/post 3.png

    - lib/resources/icons/calendrier.png
    - lib/resources/icons/fille.png
    - lib/resources/icons/cantine.png
    - lib/resources/icons/activites.png
    - lib/resources/icons/message.png
    - lib/resources/images/groupes/gp1.png
    - lib/resources/images/groupes/gp2.png
    - lib/resources/images/groupes/gp3.png
    - lib/resources/images/groupes/gp4.png
    - lib/resources/icons/logo.png
    - lib/resources/images/directrice.png
    - lib/resources/images/educatrice.png
    - lib/resources/images/parente.jpg
    - lib/resources/images/cantine/placeholder.png
    - lib/resources/images/placeholder-image.jpg
    - lib/resources/icons/evennement.png
    - lib/resources/images/dish-placeholder-hd-png.png
    - lib/resources/fonts/NotoColorEmoji.ttf

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  fonts:
    - family: NotoColorEmoji
      fonts:
        - asset: lib/resources/fonts/NotoColorEmoji.ttf
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
