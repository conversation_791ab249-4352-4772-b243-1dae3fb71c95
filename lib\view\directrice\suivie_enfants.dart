import 'dart:async';
import 'dart:convert';

import 'package:bee_kids_mobile/model/classe.dart';
import 'package:bee_kids_mobile/model/eleve.dart';
import 'package:bee_kids_mobile/view/directrice/footer.dart';
import 'package:bee_kids_mobile/view_model/classeService.dart';
import 'package:bee_kids_mobile/view_model/eleveService.dart';
import 'package:flutter/material.dart';

class SuivieEnfants extends StatefulWidget {
  const SuivieEnfants({super.key});

  @override
  _SuivieEnfantsState createState() => _SuivieEnfantsState();
}

class _SuivieEnfantsState extends State<SuivieEnfants> {
  final eleveService _eleveService = eleveService();
  final classeService _classeService = classeService();
  
  List<Classe> classes = [];
  List<Eleve> eleves = [];
  List<Eleve> filteredEleves = [];
  TextEditingController searchController = TextEditingController();
  
  bool isLoading = false;
  bool isLoadingMore = false;
  
  // Lazy loading variables
  final ScrollController _scrollController = ScrollController();
  final int _batchSize = 16; // Load 20 items at a time
  int _currentlyLoaded = 0;
  List<Eleve> _displayedEleves = [];

  // Add a key to force rebuild of the image widget
  GlobalKey _imageWidgetKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _fetchData();
    _setupScrollListener();

    searchController.addListener(() {
      _filterEleves();
    });
  }

  @override
  void dispose() {
    searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >= 
          _scrollController.position.maxScrollExtent - 200) {
        _loadMoreItems();
      }
    });
  }

  void _loadMoreItems() {
    if (isLoadingMore || _currentlyLoaded >= filteredEleves.length) return;

    setState(() {
      isLoadingMore = true;
    });

    // Simulate network delay for smooth loading
    Timer(const Duration(milliseconds: 300), () {
      setState(() {
        final nextBatch = _currentlyLoaded + _batchSize;
        final endIndex = nextBatch > filteredEleves.length 
            ? filteredEleves.length 
            : nextBatch;
        
        _displayedEleves.addAll(
          filteredEleves.sublist(_currentlyLoaded, endIndex)
        );
        _currentlyLoaded = endIndex;
        isLoadingMore = false;
      });
    });
  }

  void _resetLazyLoading() {
    _currentlyLoaded = 0;
    _displayedEleves.clear();
    _loadMoreItems();
  }

  // Add the missing _clearImageCache method
  void _clearImageCache() {
    setState(() {
      // Force rebuild of the image widget by changing its key
      _imageWidgetKey = GlobalKey();
    });
    
    // Clear Flutter's image cache
    imageCache.clear();
    imageCache.clearLiveImages();
    
    print('Image cache cleared');
  }

  Future<void> _fetchData() async {
    setState(() {
      isLoading = true;
    });

    try {
      // Fetch both classes and eleves concurrently
      final results = await Future.wait([
        _classeService.getAllClasses(),
        _eleveService.getAllEleves(),
      ]);

      final fetchedClasses = results[0] as List<Classe>;
      final fetchedEleves = results[1] as List<Eleve>;

      setState(() {
        classes = fetchedClasses.isNotEmpty ? fetchedClasses : [];
        eleves = fetchedEleves.isNotEmpty ? fetchedEleves : [];
        filteredEleves = eleves;
        isLoading = false;
      });
      
      _resetLazyLoading();
      
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      print('Error fetching data: $e');
    }
  }

  void _fetchEleves() async {
    await _fetchData();
  }

  void _fetchElevesByClasseId(id) async {
    setState(() {
      isLoading = true;
    });
    
    try {
      final response = await _classeService.getElevesByClasseId(id);
      setState(() {
        eleves = response.isNotEmpty ? response : [];
        filteredEleves = eleves;
        isLoading = false;
      });
      _resetLazyLoading();
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      print('Error fetching eleves by classe ID: $e');
    }
  }

  // Method to reset to all students
  void _showAllStudents() async {
    setState(() {
      isLoading = true;
    });
    
    try {
      final fetchedEleves = await _eleveService.getAllEleves();
      setState(() {
        eleves = fetchedEleves.isNotEmpty ? fetchedEleves : [];
        filteredEleves = eleves;
        isLoading = false;
      });
      _resetLazyLoading();
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      print('Error fetching all eleves: $e');
    }
  }

  void _filterEleves() {
    setState(() {
      filteredEleves = eleves.where((eleve) {
        return eleve.nom
            .toLowerCase()
            .contains(searchController.text.toLowerCase());
      }).toList();
    });
    _resetLazyLoading();
  }

  Widget buildDefaultAvatar(double screenWidth, {bool isLoading = false}) {
    return Container(
      width: screenWidth * 0.2,
      height: screenWidth * 0.2,
      decoration: BoxDecoration(
        color: isLoading ? Colors.grey[200] : Colors.grey[300],
        shape: BoxShape.circle,
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Icon(
            Icons.person,
            size: screenWidth * 0.1,
            color: isLoading ? Colors.grey[400] : Colors.grey[600],
          ),
          if (isLoading)
            Positioned(
              bottom: screenWidth * 0.02,
              right: screenWidth * 0.02,
              child: Container(
                width: screenWidth * 0.04,
                height: screenWidth * 0.04,
                child: CircularProgressIndicator(
                  strokeWidth: 1.5,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[500]!),
                ),
              ),
            ),
        ],
      ),
    );
  }
  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pushNamed(context, '/directrice/menu'),
        ),
        title: const Text(
          "Suivi des enfants",
          style: TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.green,
        elevation: 0,
/*         actions: [
          // Simple refresh button without cache clearing
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _fetchData,
            tooltip: 'Actualiser les données',
          ),
        ], */
      ),
      body: Container(
        color: Colors.green,
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.all(screenWidth * 0.04),
              child: TextField(
                controller: searchController,
                decoration: InputDecoration(
                  hintText: 'Rechercher par nom',
                  prefixIcon: const Icon(Icons.search),
                  filled: true,
                  fillColor: Colors.white,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(screenWidth * 0.1),
                    borderSide: BorderSide.none,
                  ),
                ),
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
              height: screenHeight * 0.06,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: classes.length + 1, // +1 for "Tous" button
                itemBuilder: (context, index) {
                  if (index == 0) {
                    // "Tous" button to show all students
                    return Padding(
                      padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.02),
                      child: ElevatedButton(
                        onPressed: _showAllStudents,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: Colors.green,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(screenWidth * 0.06),
                          ),
                        ),
                        child: Text(
                          'Tous',
                          style: TextStyle(fontSize: screenWidth * 0.035),
                        ),
                      ),
                    );
                  }
                  
                  final classe = classes[index - 1];
                  return Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: screenWidth * 0.02),
                    child: ElevatedButton(
                      onPressed: () async {
                        _fetchElevesByClasseId(classe.id);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: Colors.green,
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(screenWidth * 0.06),
                        ),
                      ),
                      child: Text(classe.nomClasse,
                          style: TextStyle(fontSize: screenWidth * 0.035)),
                    ),
                  );
                },
              ),
            ),
            SizedBox(height: screenHeight * 0.02),
            
            // Info text showing loaded items
           /*  if (filteredEleves.isNotEmpty)
              Padding(
                padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
                child: Text(
                  'Affichage de ${_displayedEleves.length} sur ${filteredEleves.length} élèves',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: screenWidth * 0.035,
                  ),
                  textAlign: TextAlign.center,
                ),
              ), */
            SizedBox(height: screenHeight * 0.01),
            
            // Main content
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(screenWidth * 0.02),
                child: isLoading
                    ? const Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : CenteredCardWithImages(
                        key: _imageWidgetKey,
                        eleves: _displayedEleves,
                        fetchEleves: _fetchEleves,
                        screenWidth: screenWidth,
                        screenHeight: screenHeight,
                        scrollController: _scrollController,
                        isLoadingMore: isLoadingMore,
                        hasMoreItems: _currentlyLoaded < filteredEleves.length,
                      ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: const MyFooter(currentRoute: '/directrice/menu'),
    );
  }
}

class CenteredCardWithImages extends StatefulWidget {
  final List<Eleve> eleves;
  final VoidCallback fetchEleves;
  final double screenWidth;
  final double screenHeight;
  final ScrollController scrollController;
  final bool isLoadingMore;
  final bool hasMoreItems;

  const CenteredCardWithImages({
    super.key,
    required this.eleves,
    required this.fetchEleves,
    required this.screenWidth,
    required this.screenHeight,
    required this.scrollController,
    required this.isLoadingMore,
    required this.hasMoreItems,
  });

  @override
  State<CenteredCardWithImages> createState() => _CenteredCardWithImagesState();
}

class _CenteredCardWithImagesState extends State<CenteredCardWithImages> {
  final Map<int, String?> _imageCache = {};
  final Map<int, Future<String?>> _loadingFutures = {};

  // Add cache size limit
  static const int _maxCacheSize = 100;

  void _cleanupCache() {
    if (_imageCache.length > _maxCacheSize) {
      final keysToRemove = _imageCache.keys.take(_imageCache.length - _maxCacheSize);
      for (final key in keysToRemove) {
        _imageCache.remove(key);
      }
    }
  }

  @override
  void dispose() {
    _imageCache.clear();
    _loadingFutures.clear();
    super.dispose();
  }

  Future<String?> _loadImage(int eleveId) {
    if (_imageCache.containsKey(eleveId)) {
      return Future.value(_imageCache[eleveId]);
    }

    if (_loadingFutures.containsKey(eleveId)) {
      return _loadingFutures[eleveId]!;
    }

    final future = eleveService().getPhotoByEleveById(eleveId).then((data) {
      try {
        if (data.isNotEmpty) {
          final photoData = jsonDecode(data);
          final photoUrl = photoData['photoUrl'] as String?;
          _imageCache[eleveId] = photoUrl;
          return photoUrl;
        }
      } catch (e) {
        print('Error parsing photo data for eleve $eleveId: $e');
      }
      _imageCache[eleveId] = null;
      return null;
    }).catchError((error) {
      print('Error loading image for eleve $eleveId: $error');
      _imageCache[eleveId] = null;
      return null;
    }).whenComplete(() {
      _loadingFutures.remove(eleveId);
    });

    _loadingFutures[eleveId] = future;
    return future;
  }

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.topCenter,
      child: Card(
        elevation: 5,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(widget.screenWidth * 0.04),
        ),
        child: Padding(
          padding: EdgeInsets.all(widget.screenWidth * 0.03),
          child: Column(
            children: [
              Expanded(
                child: widget.eleves.isEmpty
                    ? const Center(
                        child: Text(
                          'Aucun élève n est présent dans ce groupe.',
                          style: TextStyle(fontSize: 16),
                        ),
                      )
                    : CustomScrollView(
                        controller: widget.scrollController,
                        slivers: [
                          SliverGrid(
                            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 3,
                              crossAxisSpacing: widget.screenWidth * 0.03,
                              mainAxisSpacing: widget.screenWidth * 0.03,
                            ),
                            delegate: SliverChildBuilderDelegate(
                              (context, index) {
                                final eleve = widget.eleves[index];
                                return _buildEleveCard(eleve);
                              },
                              childCount: widget.eleves.length,
                            ),
                          ),
                                                    // Loading indicator at the bottom
                          if (widget.isLoadingMore || widget.hasMoreItems)
                            SliverToBoxAdapter(
                              child: Container(
                                padding: EdgeInsets.all(widget.screenWidth * 0.04),
                                child: widget.isLoadingMore
                                    ? const Center(
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                        ),
                                      )
                                    : widget.hasMoreItems
                                        ? const Center(
                                            child: Text(
                                              'Faites défiler pour charger plus...',
                                              style: TextStyle(
                                                color: Colors.grey,
                                                fontSize: 14,
                                              ),
                                            ),
                                          )
                                        : const SizedBox.shrink(),
                              ),
                            ),
                        ],
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEleveCard(Eleve eleve) {
    return GestureDetector(
      onTap: () {
        final args = {
          'id': eleve.id,
          'dateDeNaissance': eleve.dateDeNaissance,
          'nom': eleve.nom,
          'prenom': eleve.prenom,
        };

        if (args['id'] != null &&
            args['dateDeNaissance'] != null &&
            args['nom'] != null &&
            args['prenom'] != null) {
          Navigator.pushNamed(
            context,
            '/directrice/suivie_enfant',
            arguments: args,
          );
        }
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildEleveImage(eleve),
          SizedBox(height: widget.screenHeight * 0.01),
          Flexible(
            child: Text(
              eleve.nom,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: widget.screenWidth * 0.03,
                fontWeight: FontWeight.bold,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEleveImage(Eleve eleve) {
    return FutureBuilder<String?>(
      future: _loadImage(eleve.id),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return SizedBox(
            width: widget.screenWidth * 0.2,
            height: widget.screenWidth * 0.2,
            child: const Center(
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          );
        }

        final photoUrl = snapshot.data;
        if (photoUrl != null && photoUrl.isNotEmpty) {
          return ClipOval(
            child: Image.network(
              photoUrl,
              width: widget.screenWidth * 0.2,
              height: widget.screenWidth * 0.2,
              fit: BoxFit.cover,
              cacheWidth: (widget.screenWidth * 0.2 * 2).round(),
              cacheHeight: (widget.screenWidth * 0.2 * 2).round(),
              errorBuilder: (context, error, stackTrace) {
                return buildDefaultAvatar(widget.screenWidth);
              },
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return SizedBox(
                  width: widget.screenWidth * 0.2,
                  height: widget.screenWidth * 0.2,
                  child: const Center(
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                );
              },
            ),
          );
        }

        return buildDefaultAvatar(widget.screenWidth);
      },
    );
  }

  Widget buildDefaultAvatar(double screenWidth) {
    return Container(
      width: screenWidth * 0.2,
      height: screenWidth * 0.2,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        shape: BoxShape.circle,
      ),
      child: Icon(
        Icons.person,
        size: screenWidth * 0.1,
        color: Colors.grey[600],
      ),
    );
  }
}

void main() {
  runApp(const MaterialApp(
    debugShowCheckedModeBanner: false,
    home: SuivieEnfants(),
  ));
}
