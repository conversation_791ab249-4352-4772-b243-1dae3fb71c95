import 'package:bee_kids_mobile/model/Post.dart';
import 'package:bee_kids_mobile/view/InlineVideoPlayer.dart';
import 'package:bee_kids_mobile/view/VideoPlayerScreen.dart';
import 'package:bee_kids_mobile/view_model/postService.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:bee_kids_mobile/view/pdfViewerScreen.dart';
import 'package:bee_kids_mobile/view/videoPlayerScreen.dart';

class UpdatePostScreen extends StatefulWidget {
  final Post post;

  const UpdatePostScreen({super.key, required this.post});

  @override
  _UpdatePostScreenState createState() => _UpdatePostScreenState();
}

class _UpdatePostScreenState extends State<UpdatePostScreen> {
  final TextEditingController _contentController = TextEditingController();
  List<String> mediaUrls = [];

  @override
  void initState() {
    super.initState();
    _contentController.text = widget.post.content ?? ''; // Pre-fill the content

    // Collect media URLs for images, videos, and PDFs
// Check if photoUrl, videoUrl, or pdfUrl are lists or single values, and add them accordingly
    if (widget.post.photoUrl != null) {
      if (widget.post.photoUrl is List<String>) {
        mediaUrls.addAll(widget.post.photoUrl as List<String>);
      } else {
        mediaUrls.add(widget.post.photoUrl as String);
      }
    }
    if (widget.post.videoUrl != null) {
      if (widget.post.videoUrl is List<String>) {
        mediaUrls.addAll(widget.post.videoUrl as List<String>);
      } else {
        mediaUrls.add(widget.post.videoUrl as String);
      }
    }
    if (widget.post.pdfUrl != null) {
      if (widget.post.pdfUrl is List<String>) {
        mediaUrls.addAll(widget.post.pdfUrl as List<String>);
      } else {
        mediaUrls.add(widget.post.pdfUrl as String);
      }
    }
  }

  Future<void> _updatePost() async {
    try {
      // Update the post
      await PostService().updatePost(
        widget.post.id,
        _contentController.text,
        [],
      );
      Navigator.pop(context);
    } catch (e) {
      print('Error updating post: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Modifier Publication"),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextField(
              controller: _contentController,
              decoration: const InputDecoration(
                labelText: 'Publication contenue',
              ),
              maxLines: null,
            ),
            const SizedBox(height: 16),

            // Media carousel display
            mediaUrls.isNotEmpty
                ? CarouselSlider.builder(
                    itemCount: mediaUrls.length,
                    itemBuilder: (context, index, realIndex) {
                      String mediaUrl = mediaUrls[index];

                      // Handle different media types
                      if (mediaUrl.endsWith('.jpg') ||
                          mediaUrl.endsWith('.png')) {
                        return Image.network(mediaUrl);
                      } else if (mediaUrl.endsWith('.mp4')) {
                        return InlineVideoPlayer(videoUrl: mediaUrl);
                      } else if (mediaUrl.endsWith('.pdf')) {
                        return PDFViewerScreen(pdfUrl: mediaUrl);
                      }
                      return SizedBox(); // Fallback
                    },
                    options: CarouselOptions(
                      height: 250.0,
                      autoPlay: true,
                      enlargeCenterPage: true,
                      viewportFraction: 1.0,
                    ),
                  )
                : const Text("No media available"),

            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () async {
                await _updatePost(); // Update the post
                Navigator.pushNamed(
                    context, '/directrice/acceuil'); // Navigate after update
              },
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    Colors.green, // Set the background color to green
              ),
              child: const Text('Modifier Publication'),
            )
          ],
        ),
      ),
    );
  }
}
