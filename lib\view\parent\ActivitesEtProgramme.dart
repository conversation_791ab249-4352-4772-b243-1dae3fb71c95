import 'dart:convert';
import 'dart:io';
import 'package:bee_kids_mobile/resources/environnement/apiUrl.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:bee_kids_mobile/model/classe.dart';
import 'package:bee_kids_mobile/model/eleve.dart';
import 'package:bee_kids_mobile/view_model/classeService.dart';
import 'package:bee_kids_mobile/view_model/EmploiService.dart';
import 'package:bee_kids_mobile/view_model/EleveService.dart';
import 'package:bee_kids_mobile/view/parent/footer.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:path_provider/path_provider.dart';

typedef EmploiData = Map<String, dynamic>;

class ActivitesEtProgrammeParent extends StatefulWidget {
  @override
  _ActivitesEtProgrammeState createState() => _ActivitesEtProgrammeState();
}

class PDFViewerScreen extends StatelessWidget {
  final String filePath;

  const PDFViewerScreen({Key? key, required this.filePath}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Visualisation du fichier PDF'),
        backgroundColor: Colors.green,
      ),
      body: PDFView(
        filePath: filePath,
        enableSwipe: true,
        swipeHorizontal: false,
        autoSpacing: true,
        pageFling: true,
        onError: (error) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Erreur : Impossible d\'ouvrir le fichier PDF.'),
              backgroundColor: Colors.red,
            ),
          );
        },
      ),
    );
  }
}

class _ActivitesEtProgrammeState extends State<ActivitesEtProgrammeParent> {
  final eleveService _eleveService = eleveService();
  final classeService _classeService = classeService();
  final EmploiService _emploiService = EmploiService();
  List<Classe> classes = [];
  List<Eleve> eleves = [];
  List<dynamic> data = [];
  List<EmploiData> activites = [];
  List<Eleve> filteredEleves = [];
  EmploiData? dernierEmploi;
  bool isLoading = true;
  bool hasError = false;
  String errorMessage = '';
  TextEditingController searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _fetchEleves();
    _filterEleves();

    searchController.addListener(() {
      _filterEleves();
    });
  }

  void _fetchEleves() async {
    try {
      final response = await _eleveService.getAllElevesByParentId();
      setState(() {
        eleves = response.isNotEmpty ? response : [];
        filteredEleves = eleves;
      });
    } catch (e) {
      print('Error fetching eleves: $e');
    }
  }

  void _filterEleves() {
    setState(() {
      filteredEleves = eleves.where((eleve) {
        return eleve.nom
            .toLowerCase()
            .contains(searchController.text.toLowerCase());
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pushNamed(context, '/parent/menu'),
        ),
        title: const Text(
          'Emplois du temps',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.green,
        elevation: 0,
      ),
      body: Container(
        color: Colors.green,
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.all(screenWidth * 0.04),
              child: TextField(
                controller: searchController,
                decoration: InputDecoration(
                  hintText: 'Rechercher par nom',
                  prefixIcon: const Icon(Icons.search),
                  filled: true,
                  fillColor: Colors.white,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(screenWidth * 0.1),
                    borderSide: BorderSide.none,
                  ),
                ),
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
              height: screenHeight * 0.06,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: classes.length,
                itemBuilder: (context, index) {
                  final classe = classes[index];
                  return Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: screenWidth * 0.02),
                    child: ElevatedButton(
                      onPressed: () async {},
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: Colors.green,
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(screenWidth * 0.06),
                        ),
                      ),
                      child: Text(classe.nomClasse,
                          style: TextStyle(fontSize: screenWidth * 0.035)),
                    ),
                  );
                },
              ),
            ),
            // SizedBox(height: screenHeight * 0.03),
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(screenWidth * 0.02),
                child: CenteredCardWithImages(
                  eleves: filteredEleves,
                  fetchEleves: _fetchEleves,
                  screenWidth: screenWidth,
                  screenHeight: screenHeight,
                  showNoEmploiPopup: _showNoEmploiPopup,
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: const MyFooterParent(currentRoute: '/parent/menu'),
    );
  }
}

void _showNoEmploiPopup(BuildContext context) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        content: Text('Aucun emploi du temps associé à cet enfant.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: Text('OK'),
          ),
        ],
      );
    },
  );
}

class CenteredCardWithImages extends StatelessWidget {
  final List<Eleve> eleves;
  final VoidCallback fetchEleves;
  final double screenWidth;
  final double screenHeight;
  final Function(BuildContext) showNoEmploiPopup;

  const CenteredCardWithImages({
    super.key,
    required this.eleves,
    required this.fetchEleves,
    required this.screenWidth,
    required this.screenHeight,
    required this.showNoEmploiPopup,
  });

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.topCenter,
      child: Card(
        elevation: 5,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(screenWidth * 0.04),
        ),
        child: Padding(
          padding: EdgeInsets.all(screenWidth * 0.03),
          child: Column(
            children: [
              Expanded(
                child: eleves.isEmpty
                    ? const Center(
                        child: Text(
                          'Aucun élève.',
                          style: TextStyle(fontSize: 16),
                        ),
                      )
                    : SingleChildScrollView(
                        child: GridView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            crossAxisSpacing: screenWidth * 0.03,
                            mainAxisSpacing: screenWidth * 0.03,
                          ),
                          itemCount: eleves.length,
                          itemBuilder: (context, index) {
                            final eleve = eleves[index];

                            return GestureDetector(
                              onTap: () {
                                if (eleve.classeId == null) {
                                  showNoEmploiPopup(
                                      context); // Afficher la pop-up
                                } else {
                                  final args = {
                                    'id': eleve.id,
                                    'nom': eleve.nom,
                                    'prenom': eleve.prenom,
                                    'classeId': eleve.classeId,
                                  };

                                  Navigator.pushNamed(
                                    context,
                                    '/parent/EmploiEnfant',
                                    arguments: args,
                                  );
                                }
                              },
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  FutureBuilder<String>(
                                    future: eleveService()
                                        .getPhotoByEleveById(eleve.id),
                                    builder: (context, snapshot) {
                                      if (snapshot.connectionState ==
                                          ConnectionState.waiting) {
                                        return SizedBox(
                                          width: screenWidth * 0.2,
                                          height: screenWidth * 0.2,
                                          child: const Center(
                                              child:
                                                  CircularProgressIndicator()),
                                        );
                                      } else if (snapshot.hasData &&
                                          snapshot.data != null) {
                                        final photoData =
                                            jsonDecode(snapshot.data!);
                                        final photoUrl = photoData['photoUrl'];

                                        if (photoUrl != null &&
                                            photoUrl.isNotEmpty) {
                                          return Image.network(
                                            photoUrl,
                                            width: screenWidth * 0.2,
                                            height: screenWidth * 0.2,
                                            fit: BoxFit.cover,
                                          );
                                        }
                                      }
                                      return SizedBox(
                                        width: screenWidth * 0.2,
                                        height: screenWidth * 0.2,
                                        child: const CircleAvatar(
                                          child: Icon(Icons.person, size: 40),
                                        ),
                                      );
                                    },
                                  ),
                                  SizedBox(height: screenHeight * 0.01),
                                  Flexible(
                                    child: Text(
                                      eleve.nom,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        fontSize: screenWidth * 0.03,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

void main() {
  runApp(MaterialApp(
    debugShowCheckedModeBanner: false,
    home: ActivitesEtProgrammeParent(),
  ));
}
