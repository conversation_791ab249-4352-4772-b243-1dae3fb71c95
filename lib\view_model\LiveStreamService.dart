import 'dart:convert';
import 'package:bee_kids_mobile/model/LiveStream.dart';
import 'package:bee_kids_mobile/view_model/tokenService.dart';
import 'package:http/http.dart' as http;
import '../resources/environnement/apiUrl.dart';

class LiveStreamService {
  Future<Map<String, String>> _getAuthHeaders() async {
    final token = await tokenService.getToken();
    return token != null
        ? {'Authorization': 'Bearer $token', 'Content-Type': 'application/json'}
        : {'Content-Type': 'application/json'};
  }

  final baseUrl = ApiUrl.baseUrl;
  final TokenService tokenService = TokenService();

  Future<List<LiveStream>> fetchLiveStreams(
      {int page = 0, int size = 6}) async {
    final headers = await _getAuthHeaders();
    final response = await http.get(
      Uri.parse('${baseUrl}api/livestreams/list?page=$page&size=$size'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      print('fetch lives works !!!');
      final Map<String, dynamic> data = json.decode(response.body);
      print("Live Streams Response: $data"); // Debugging
      List<dynamic> liveStreamsData = data['liveStreams'];

      return liveStreamsData.map((json) => LiveStream.fromJson(json)).toList();
    } else {
      throw Exception('Failed to load live streams');
    }
  }

  // Start a live stream
  Future<Map<String, dynamic>> startLiveStream(int userId) async {
    final headers = await _getAuthHeaders();
    final response = await http.post(
      Uri.parse('${baseUrl}api/livestreams/start/$userId'),
      headers: headers,
    );

    if (response.statusCode == 201) {
      print('start live works !!!');
      // Parse the response if the request is successful
      final Map<String, dynamic> data = json.decode(response.body);
      return data; // Returning the response body with stream details
    } else {
      throw Exception('Failed to start live stream');
    }
  }

  Future<Map<String, dynamic>> stopLiveStream(int streamId) async {
    final headers = await _getAuthHeaders();
    final response = await http.post(
      Uri.parse('${baseUrl}api/livestreams/stop/$streamId'),
      headers: headers,
    );

    print('Raw stop live response: ${response.body.substring(0, 500)}...');
    // Debugging

    if (response.statusCode == 200) {
      try {
        print("stop livestream works!");
        final Map<String, dynamic> data = json.decode(response.body);
        return data;
      } catch (e) {
        throw Exception('JSON Parsing Error: $e\nResponse: ${response.body}');
      }
    } else {
      throw Exception(
          'Failed to stop live stream. Status code: ${response.statusCode}');
    }
  }
}
