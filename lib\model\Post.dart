import 'package:bee_kids_mobile/model/Comment.dart';
import 'package:bee_kids_mobile/model/Like.dart';

class Post {
  final int id;
  final String? content;
  final List<String>? photoUrl;
  final List<String>? videoUrl;
  final List<String>? pdfUrl;
  final DateTime? createdAt;
  final String? authorName;
  final String? authorPhotoUrl;
  final String? role;
  final int? authorId; // Nouvelle propriété ajoutée pour l'ID de l'auteur
  
  // Propriétés pour les likes
  int likeCount;
  bool liked;
  List<LikeDTO> likedByUsers;
  List<UserLikeDTO> detailedLikedByUsers; // Nouvelle propriété ajoutée
  
  // Propriétés pour les commentaires
  int commentCount;
  List<Comment> comments;
  bool showComments;

  Post({
    required this.id,
    this.content,
    this.photoUrl,
    this.videoUrl,
    this.pdfUrl,
    this.createdAt,
    this.authorName,
    this.authorPhotoUrl,
    this.role,
    this.authorId, // Ajout du paramètre
    this.likeCount = 0,
    this.liked = false,
    this.likedByUsers = const [],
    this.detailedLikedByUsers = const [], // Initialisation de la nouvelle propriété
    this.commentCount = 0,
    this.comments = const [],
    this.showComments = false,
  });

  // Factory method for creating Post from JSON
  factory Post.fromJson(Map<String, dynamic> json) {
    return Post(
      id: json['id'] ?? 0,
      content: json['content'],
      photoUrl: json['photoUrl'] != null 
          ? List<String>.from(json['photoUrl']) 
          : null,
      videoUrl: json['videoUrl'] != null 
          ? List<String>.from(json['videoUrl']) 
          : null,
      pdfUrl: json['pdfUrl'] != null 
          ? List<String>.from(json['pdfUrl']) 
          : null,
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : null,
      authorName: json['authorName'],
      authorPhotoUrl: json['authorPhotoUrl'],
      role: json['role'],
      authorId: json['authorId'] ?? json['userId'], // Ajout de la récupération de l'ID auteur
      likeCount: json['likeCount'] ?? 0,
      liked: json['liked'] ?? false,
      commentCount: json['commentCount'] ?? 0,
      // Les listes seront initialisées vides et remplies plus tard
      likedByUsers: [],
      detailedLikedByUsers: [],
      comments: [],
      showComments: false,
    );
  }

  // Method to convert Post to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'photoUrl': photoUrl,
      'videoUrl': videoUrl,
      'pdfUrl': pdfUrl,
      'createdAt': createdAt?.toIso8601String(),
      'authorName': authorName,
      'authorPhotoUrl': authorPhotoUrl,
      'role': role,
      'authorId': authorId,
      'likeCount': likeCount,
      'liked': liked,
      'commentCount': commentCount,
    };
  }

  String get timeAgo {
    if (createdAt == null) return 'Date inconnue';
    final now = DateTime.now();
    final difference = now.difference(createdAt!);

    if (difference.inMinutes < 1) {
      return 'À l’instant';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} min';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} h';
    } else {
      return '${difference.inDays} j';
    }
  }

  // Getter pour la compatibilité avec l'ancien code
  int? get userId => authorId;
}
