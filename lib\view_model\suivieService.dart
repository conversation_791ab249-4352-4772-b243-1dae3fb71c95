import 'dart:convert';
import 'package:http/http.dart' as http;
import '../resources/environnement/apiUrl.dart';
import '../view_model/tokenService.dart';
import '../model/suivie.dart';

// Modèle simple pour les mauvais suivis
class MauvaisSuivi {
  final int id;
  final String note;
  final String repas;
  final String sommeil;
  final String humeur;
  final String jeuxExterieur;
  final String interaction;
  final String participation;
  final String jour;
  final String eleveNom;
  final String formateurNom;

  MauvaisSuivi({
    required this.id,
    required this.note,
    required this.repas,
    required this.sommeil,
    required this.humeur,
    required this.jeuxExterieur,
    required this.interaction,
    required this.participation,
    required this.jour,
    required this.eleveNom,
    required this.formateurNom,
  });

  factory MauvaisSuivi.fromMap(Map<String, dynamic> map) {
    return MauvaisSuivi(
      id: map['id'],
      note: map['note'] ?? '',
      repas: map['repas'] ?? '',
      sommeil: map['sommeil'] ?? '',
      humeur: map['humeur'] ?? '',
      jeuxExterieur: map['jeuxExterieur'] ?? '',
      interaction: map['interaction'] ?? '',
      participation: map['participation'] ?? '',
      jour: map['jour'] ?? '',
      eleveNom: map['eleveNom'] ?? '',
      formateurNom: map['formateurNom'] ?? '',
    );
  }

  @override
  String toString() {
    return 'MauvaisSuivi{id: $id, eleveNom: $eleveNom, formateurNom: $formateurNom, jour: $jour}';
  }
}

// Modèle pour les élèves sans suivie
class EleveSansSuivie {
  final String nom;
  final String prenom;
  final String classe;
  final String enseignant;

  EleveSansSuivie({
    required this.nom,
    required this.prenom,
    required this.classe,
    required this.enseignant,
  });

  factory EleveSansSuivie.fromMap(Map<String, dynamic> map) {
    return EleveSansSuivie(
      nom: map['nom'] ?? '',
      prenom: map['prenom'] ?? '',
      classe: map['classe'] ?? '',
      enseignant: map['enseignant'] ?? '',
    );
  }

  String get nomComplet => '$prenom $nom';

  @override
  String toString() {
    return 'EleveSansSuivie{nom: $nom, prenom: $prenom, classe: $classe, enseignant: $enseignant}';
  }
}

class SuivieService {
  final baseUrl = ApiUrl.baseUrl;
  final TokenService tokenService = TokenService();

  // Get headers with Authorization token
  Future<Map<String, String>> _getAuthHeaders() async {
    final token = await tokenService.getToken();
    if (token != null) {
      return {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };
    }
    return {
      'Content-Type': 'application/json',
    };
  }

  Future<List<Suivis>> getSuivisByEleveIdAndDate(
      int eleveId, String date) async {
    final headers = await _getAuthHeaders();
    try {
      final response = await http.get(
        Uri.parse(
            '${baseUrl}api/suivis/suiviByEleveAndDate?date=$date&eleveId=$eleveId'),
        headers: headers,
      );
      print(
          "${baseUrl}api/suivis/suiviByEleveAndDate?date=$date&eleveId=$eleveId");
      print('Response body ===========> ${response.body}'); // Debugging line

      if (response.statusCode == 200) {
        final decoded = json.decode(response.body);

        // Check if the response is a single object or a list
        if (decoded is Map<String, dynamic>) {
          // Wrap the single object in a list
          return [Suivis.fromMap(decoded)];
        } else if (decoded is List) {
          // Process the list of objects
          return decoded
              .map((json) => Suivis.fromMap(json as Map<String, dynamic>))
              .toList();
        } else {
          print('Unexpected response format: $decoded');
          return [];
        }
      } else {
        print('Failed to load suivis: ${response.statusCode} ${response.body}');
        return [];
      }
    } catch (e) {
      print('Error fetching suivis data: $e');
      return [];
    }
  }

  // Fetch Suivis by eleve_id
  Future<List<Suivis>> getSuivisByEleveId(int eleveId) async {
    final headers = await _getAuthHeaders();
    try {
      final response = await http.get(
        Uri.parse('${baseUrl}api/suivis/eleve/$eleveId'),
        headers: headers,
      );
      print('Response body ===========> ${response.body}'); // Debugging line

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body) as List<dynamic>;
        print('json decoder ===========> $data');
        return data
            .map((json) => Suivis.fromMap(json as Map<String, dynamic>))
            .toList();
      } else {
        print('Failed to load suivis: ${response.statusCode} ${response.body}');
        return [];
      }
    } catch (e) {
      print('Error fetching suivis data: $e');
      return [];
    }
  }

  Future<bool> postSuivis(Suivis suivis) async {
    var id = await tokenService.getId();
    final headers = await _getAuthHeaders();
    int eleveId = suivis.id;
    // Now safe since we checked for null
    print('EleveID =>  ${suivis.eleveId}');
    print('suivisId =>  ${suivis.id}');
    print('Suivi date =>${suivis.jour}');
    final url = Uri.parse('${baseUrl}api/suivis/$eleveId/$id');

    try {
      final response = await http.post(
        url,
        headers: headers,
        body: json.encode(suivis.toMap()),
      );

      if (response.statusCode == 201) {
        print('Suivis posted successfully: ${response.body}');

        return true;
      } else {
        print('Failed to post Suivis: ${response.statusCode} ${response.body}');
        return false;
      }
    } catch (e) {
      print('Error posting Suivis data: $e');
      return false;
    }
  }

  Future<bool> deleteSuivis(int id) async {
    final headers = await _getAuthHeaders();
    final url = Uri.parse('${baseUrl}api/suivis/$id');
    try {
      final response = await http.delete(
        url,
        headers: headers,
      );

      if (response.statusCode == 204) {
        print('Suivis deleted successfully: ${response.body}');

        return true;
      } else {
        print(
            'Failed to delet Suivis: ${response.statusCode} ${response.body}');
        return false;
      }
    } catch (e) {
      print('Error deleting Suivis data: $e');
      return false;
    }
  }

  Future<bool> updateSuivis(Suivis suivis) async {
    final headers = await _getAuthHeaders();
    int? formateurId = await tokenService.getId();

    if (formateurId == null) {
      print('Error: formateurId is null');
      return false;
    }

    suivis.formateurId = formateurId; // Now safe since we checked for null
    print('FormateurID =>  $formateurId');
    print('EleveID =>  ${suivis.eleveId}');
    print('suivisId =>  ${suivis.id}');
    print('Suivi date =>${suivis.jour}');

    final url = Uri.parse('${baseUrl}api/suivis/${suivis.id}');
    try {
      final response = await http.put(
        url,
        headers: headers,
        body: json.encode(suivis.toMap()),
      );
      if (response.statusCode == 200) {
        print('Suivis updated successfully: ${response.body}');
        return true;
      } else {
        print(
            'Failed to update Suivis: ${response.statusCode} ${response.body}');
        return false;
      }
    } catch (e) {
      print('Error updating Suivis data: $e');
      return false;
    }
  }

  // CRASH-SAFE method - sequential processing with minimal memory usage
  Future<int> countStudentsWithSuivieForDate(List<int> eleveIds, String date) async {
    print('🔒 SAFE suivie check for ${eleveIds.length} students on $date (Sequential)');

    final startTime = DateTime.now();
    int count = 0;

    // Process students one by one to avoid memory crashes
    for (int i = 0; i < eleveIds.length; i++) {
      final eleveId = eleveIds[i];

      try {
        final suivis = await getSuivisByEleveIdAndDate(eleveId, date);
        if (suivis.isNotEmpty) {
          count++;
        }

        // Progress update every 10 students
        if ((i + 1) % 10 == 0 || i == eleveIds.length - 1) {
          print('📊 Progress: ${i + 1}/${eleveIds.length} students checked (${count} with suivie)');
        }

      } catch (e) {
        // Continue processing even if one student fails
        print('⚠️ Error checking student $eleveId: $e');
      }

      // Small delay to prevent overwhelming the server
      if (i < eleveIds.length - 1) {
        await Future.delayed(const Duration(milliseconds: 50));
      }
    }

    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);

    print('🎯 FINAL RESULT: $count/${eleveIds.length} students with suivie');
    print('⚡ COMPLETED: ${duration.inMilliseconds}ms (${duration.inSeconds}s)');

    return count;
  }




  ////////////// Tableau de bord ////////////
  
/*     Future<List<dynamic>> getMauvaisSuivis() async {
       final url = Uri.parse(
        '${baseUrl}api/suivis/mauvais-suivis');
    final headers = await _getAuthHeaders();
    final response = await http.get(url, headers: headers);

    if (response.statusCode == 200) {
      print('Mauvais suivis fetched successfully: ${response.body}'); 
      return jsonDecode(response.body);

    } else {
      throw Exception('Erreur lors de la récupération des mauvais suivis');
    }
  } */

 Future<List<MauvaisSuivi>> getMauvaisSuivis() async {
  final url = Uri.parse('${baseUrl}api/suivis/mauvais-suivis');
  final headers = await _getAuthHeaders();

  final response = await http.get(url, headers: headers);

  if (response.statusCode == 200) {
    final List<dynamic> jsonData = json.decode(response.body);

    final List<MauvaisSuivi> mauvaisSuivisList = jsonData.map((item) {
      return MauvaisSuivi.fromMap(item);
    }).toList();

    print("✅ Nombre de mauvais suivis récupérés: ${mauvaisSuivisList.length}");
    print('Mauvais suivis fetched successfully: ${response.body}');
    return mauvaisSuivisList;
  } else {
    print("❌ Erreur récupération mauvais suivis: ${response.statusCode}");
    print(response.body);
    return [];
  }
}


Future<int> getNombreSuivisPourAujourdhui() async {
  final url = Uri.parse('${baseUrl}api/suivis/NombresSuivis/aujourdhui');
  final headers = await _getAuthHeaders();
  final response = await http.get(url, headers: headers);

  if (response.statusCode == 200) {
    final responseBody = response.body;
    print('get Nombre Suivis Pour Aujourdhui: $responseBody');

    // Convertir la chaîne en int si c'est un nombre brut
    final nombre = int.tryParse(responseBody);
    if (nombre != null) {
      return nombre;
    } else {
      throw Exception("Réponse inattendue : $responseBody");
    }
  } else {
    print('get Nombre Suivis Pour Aujourdhui Error: ${response.statusCode}');
    print('get Nombre Suivis Pour Aujourdhui Error: ${response.body}');
    throw Exception('Erreur HTTP: ${response.statusCode}');
  }
}

// Méthode pour récupérer les élèves sans suivie
Future<List<EleveSansSuivie>> getElevesSansSuivie() async {
  final url = Uri.parse('${baseUrl}api/suivis/eleves-sans-suivi');
  final headers = await _getAuthHeaders();

  try {
    final response = await http.get(url, headers: headers);

    if (response.statusCode == 200) {
      final List<dynamic> jsonData = json.decode(response.body);

      final List<EleveSansSuivie> elevesSansSuivieList = jsonData.map((item) {
        return EleveSansSuivie.fromMap(item);
      }).toList();

      print("✅ Nombre d'élèves sans suivie récupérés: ${elevesSansSuivieList.length}");
      print('Élèves sans suivie fetched successfully: ${response.body}');
      return elevesSansSuivieList;
    } else {
      print("❌ Erreur récupération élèves sans suivie: ${response.statusCode}");
      print(response.body);
      return [];
    }
  } catch (e) {
    print('Error fetching élèves sans suivie: $e');
    return [];
  }
}



}
