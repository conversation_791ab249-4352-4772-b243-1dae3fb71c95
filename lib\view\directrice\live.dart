/* import 'package:bee_kids_mobile/view_model/LiveStreamService.dart';
import 'package:bee_kids_mobile/view_model/tokenService.dart';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';

class LiveVideo extends StatefulWidget {
  @override
  _LiveVideoState createState() => _LiveVideoState();
}

class _LiveVideoState extends State<LiveVideo> {
  CameraController? _cameraController;
  LiveStreamService liveStreamService = LiveStreamService();
  bool isStreaming = false;
  int? liveStreamId;

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  Future<void> _initializeCamera() async {
    final cameras = await availableCameras();
    _cameraController = CameraController(cameras.first, ResolutionPreset.high);
    await _cameraController?.initialize();
    if (mounted) setState(() {});
  }

  Future<void> _startLive() async {
    int? userId = await TokenService().getId();
    try {
      final response = await liveStreamService.startLiveStream(userId!);
      setState(() {
        isStreaming = true;
        liveStreamId = response['streamId'];
      });
    } catch (e) {
      print('Error starting live: $e');
    }
  }

  Future<void> _stopLive() async {
    if (liveStreamId == null) return;
    try {
      await liveStreamService.stopLiveStream(liveStreamId!);
      setState(() {
        isStreaming = false;
        liveStreamId = null;
      });
    } catch (e) {
      print('Error stopping live: $e');
    }
  }

  @override
  void dispose() {
    _cameraController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Live Stream')),
      body: Column(
        children: [
          Expanded(
            child: _cameraController == null ||
                    !_cameraController!.value.isInitialized
                ? Center(child: CircularProgressIndicator())
                : CameraPreview(_cameraController!),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton(
                onPressed: isStreaming ? null : _startLive,
                child: Text('Start Live'),
              ),
              SizedBox(width: 20),
              ElevatedButton(
                onPressed: isStreaming ? _stopLive : null,
                child: Text('Stop Live'),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
 */
import 'package:bee_kids_mobile/view/directrice/footer.dart';
import 'package:flutter/material.dart';

class LiveVideo extends StatelessWidget {
  const LiveVideo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: const Color.fromARGB(255, 36, 132, 43),
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pushNamed(context, '/directrice/acceuil'),
        ),
        title: const Text(
          "Live stream",
          style: TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: Center(
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(25),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.3),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: const Text(
            "Cette fonctionnalité est en cours de développement",
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.grey,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
      bottomNavigationBar: const MyFooter(currentRoute: '/directrice/acceuil'),
    );
  }
}
