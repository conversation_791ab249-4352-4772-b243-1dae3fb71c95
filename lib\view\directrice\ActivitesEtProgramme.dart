import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:bee_kids_mobile/resources/environnement/apiUrl.dart';
import 'package:bee_kids_mobile/view/educateur/ActivitesEtProgramme.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:bee_kids_mobile/model/classe.dart';
import 'package:bee_kids_mobile/view_model/classeService.dart';
import 'package:bee_kids_mobile/view_model/EmploiService.dart';
import 'package:bee_kids_mobile/view/directrice/footer.dart';
import 'package:open_file/open_file.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:path_provider/path_provider.dart';
import 'package:dio/dio.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_pdfview/flutter_pdfview.dart';

// Définition d'un type pour la structure des emplois
typedef EmploiData = Map<String, dynamic>;

class ActivitesEtProgramme extends StatefulWidget {
  @override
  _ActivitesEtProgrammeState createState() => _ActivitesEtProgrammeState();
}

class PDFViewerScreen extends StatefulWidget {
  final String filePath;

  const PDFViewerScreen({Key? key, required this.filePath}) : super(key: key);

  @override
  _PDFViewerScreenState createState() => _PDFViewerScreenState();
}

class _PDFViewerScreenState extends State<PDFViewerScreen> {
  bool _isLoading = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Emploi du temps',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.green,
        elevation: 5,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Stack(
        children: [
          PDFView(
            filePath: widget.filePath,
            enableSwipe: true,
            swipeHorizontal: false,
            autoSpacing: true,
            pageFling: true,
            onRender: (pages) {
              setState(() {
                _isLoading = false;
              });
              debugPrint('PDF rendu avec $pages pages');
            },
            onError: (error) {
              debugPrint('Erreur lors de l\'ouverture du PDF : $error');
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content:
                      Text('Erreur : Impossible d\'ouvrir le fichier PDF.'),
                  backgroundColor: Colors.red,
                ),
              );
              setState(() {
                _isLoading = false;
              });
            },
            onPageChanged: (page, total) {
              debugPrint('Page actuelle : $page / $total');
            },
          ),
          if (_isLoading)
            const Center(
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }
}

class _ActivitesEtProgrammeState extends State<ActivitesEtProgramme> {
  final classeService _classeService = classeService();
  final EmploiService _emploiService = EmploiService();
  List<Classe> classes = [];
  List<EmploiData> activites = [];
  bool isLoading = true;
  bool _isLoading = false;
  bool hasError = false;
  bool _isConsulting = false;
  DateTime? _lastConsultTap;
  String errorMessage = '';
  bool isLoadingDelete = false;
  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    // Affiche le loader avant de récupérer les données
    setState(() {
      isLoading = true;
    });

    try {
      await _fetchClasses();
      await _loadEmplois();
    } catch (e) {
      _handleError('Erreur d\'initialisation', e);
    } finally {
      // Assurez-vous de masquer le loader après la récupération
      setState(() {
        isLoading = false;
      });
    }
  }

  void _handleError(String message, dynamic error) {
    debugPrint('$message: $error');
    if (mounted) {
      setState(() {
        hasError = true;
        errorMessage = message;
        isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _fetchClasses() async {
    try {
      final fetchedClasses = await _classeService.getAllClasses();
      if (mounted) {
        setState(() {
          classes = fetchedClasses;
          isLoading = false;
        });
      }
    } catch (e) {
      _handleError('Erreur lors de la récupération des classes', e);
    }
  }

  Future<void> _loadEmplois() async {
    if (!mounted) return;

    try {
      setState(() {
        isLoading = true;
        hasError = false;
        errorMessage = '';
      });

      debugPrint('Début du chargement des emplois');

      final classes = await _classeService.getAllClasses();
      debugPrint('Classes reçues : ${classes.length}');

      if (classes.isEmpty) {
        throw Exception('Aucune classe disponible');
      }

      final classesMap = {
        for (var classe in classes) classe.id: classe.nomClasse
      };

      final emplois = await _emploiService.getAllEmplois();
      debugPrint('Emplois reçus : ${emplois.length}');

      if (emplois.isEmpty) {
        // Si aucun emploi n'est trouvé, mettez à jour l'état
        setState(() {
          activites = [];
          isLoading = false;
        });
        return;
      }

      if (mounted) {
        setState(() {
          activites = emplois.map((emploi) {
            final classeId = emploi['classeId'] as int?;
            final classeName = emploi['classeName'] as String?;

            return {
              'id': emploi['id']?.toString() ?? '-1',
              'name': emploi['name'] as String? ?? 'Sans nom',
              'classeName': classeName,
              'classeId': classeId?.toString() ?? '-1',
              'filePath': emploi['filePath'] as String? ?? '',
            };
          }).toList();

          debugPrint('Liste des activités après le mapping : $activites');
          isLoading = false; // Masquer le loader après le chargement
        });
      }
    } catch (e) {
      debugPrint('Erreur lors du chargement des emplois : $e');
      if (mounted) {
        setState(() {
          hasError = true;
          errorMessage =
              'Erreur lors du chargement des emplois : ${e.toString()}';
          isLoading = false; // Masquer le loader en cas d'erreur
        });
      }
    }
  }

  Future<void> _createAndAddEmploi(
      int classId, String NomClasse, PlatformFile file) async {
    if (!mounted) return;

    try {
      debugPrint(
          'Début de création emploi - ClassId: $classId, Name: $NomClasse, File: ${file.name}');

      if (NomClasse.trim().isEmpty) {
        throw Exception('Le nom ne peut pas être vide');
      }
      if (file.path == null) {
        throw Exception('Aucun fichier sélectionné');
      }
      if (!classes.any((c) => c.id == classId)) {
        throw Exception('Classe invalide');
      }

      setState(() {
        isLoading = true;
      });

      final fileToUpload = File(file.path!);
      debugPrint('Chemin du fichier: ${fileToUpload.path}');

      if (!fileToUpload.existsSync()) {
        throw Exception('Le fichier sélectionné est introuvable');
      }

      debugPrint('Appel du service createEmploi');
      await _emploiService.createEmploi(NomClasse, fileToUpload, classId);
      debugPrint('Emploi crée avec succès dans le service');

      debugPrint('Début du rechargement des emplois');
      await _loadEmplois();
      debugPrint('Emplois rechargés avec succès');

      if (mounted) {
        setState(() {
          isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Emploi crée avec succès'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Erreur détaillée: $e');
      _handleError('Erreur lors de la création de l\'emploi', e);
    }
  }

  Future<void> _deleteEmploi(BuildContext context, EmploiData emploi) async {
    try {
      setState(() {
        isLoading = true;
      });

      final emploiId = int.parse(emploi['id']);
      await _emploiService.deleteEmploi(emploiId);

      setState(() {
        activites.removeWhere((item) => item['id'] == emploi['id']);
        isLoading = false;
      });

      if (mounted) {
        // Fermer la popup de détails si elle est ouverte
        Navigator.of(context, rootNavigator: true).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Emploi supprimé avec succès'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la suppression'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  void _showDeleteConfirmationDialog(EmploiData emploi) {
    showDialog(
      context: context,
      builder: (BuildContext contextDialog) {
        return StatefulBuilder(
          builder: (context, setStateDialog) {
            return AlertDialog(
              title: Text(
                'Confirmation de suppression',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              content: Text(
                'Êtes-vous sûr de vouloir supprimer cet emploi?',
                style: TextStyle(fontSize: 16),
              ),
              actions: [
                ElevatedButton(
                  onPressed: isLoadingDelete
                      ? null
                      : () {
                          Navigator.pop(contextDialog);
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                  child: Text('Annuler'),
                ),
                ElevatedButton(
                  onPressed: isLoadingDelete
                      ? null
                      : () async {
                          setStateDialog(() {
                            isLoadingDelete = true;
                          });

                          // Supprime l'emploi et ferme la popup de détails
                          await _deleteEmploi(context, emploi);

                          if (contextDialog.mounted) {
                            Navigator.pop(
                                contextDialog); // Ferme la popup de confirmation
                          }
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.pink,
                    foregroundColor: Colors.white,
                  ),
                  child: isLoadingDelete
                      ? SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : Text('Oui'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<String> getExternalDirectoryPath() async {
    final directory = await getExternalStorageDirectory();
    final downloadPath = "${directory?.parent.parent.parent.parent}/Download";
    return downloadPath;
  }

  Future<void> _downloadFile(
      BuildContext context, String url, String fileName) async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) => AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: const [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Téléchargement en cours...'),
            ],
          ),
        ),
      );

      String? path;

      if (Platform.isAndroid) {
        path = '/storage/emulated/0/Download/$fileName';
      } else if (Platform.isIOS) {
        Directory appDocDir = await getApplicationDocumentsDirectory();
        path = '${appDocDir.path}/$fileName';
      }

      Dio dio = Dio();

      await dio.download(
        url,
        path!,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            debugPrint(
                'Progress: ${(received / total * 100).toStringAsFixed(0)}%');
          }
        },
      );

      if (context.mounted) Navigator.pop(context);

      _showNotificationDialog(
          context, 'Fichier téléchargé avec succès', Colors.green);

      // Optional: Open or share the file
      if (Platform.isIOS) {
        OpenFile.open(path); // Use share_plus or open_file to preview or share
      }
    } catch (e) {
      debugPrint('Erreur lors du téléchargement: $e');

      if (context.mounted) Navigator.pop(context);

      _showNotificationDialog(
          context, 'Erreur lors du téléchargement', Colors.red);
    }
  }

  void _showAddActivityPopup() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AddActivityPopup(
          onAdd: _createAndAddEmploi,
          classes: classes,
        );
      },
    );
  }

  void _showNotificationDialog(
      BuildContext context, String message, Color color) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Row(
            children: [
              Icon(
                color == Colors.green ? Icons.check_circle : Icons.error,
                color: color,
              ),
              SizedBox(width: 10),
              Expanded(child: Text(message)),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: Text('OK'),
            ),
          ],
        );
      },
    );
  }

  void _showDetailsPopup(EmploiData emploi) {
    if (emploi['name'] == null || emploi['filePath'] == null) {
      debugPrint('Données manquantes pour l\'emploi : $emploi');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Détails incomplets pour cet emploi.')),
      );
      return;
    }
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Détails de l\'emploi',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: SingleChildScrollView(
            child: Stack(
              // Use Stack to overlay the loader
              children: [
                Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      utf8.decode(latin1.encode('Nom : ${emploi['name']}')),
                      style:
                          TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 10),
                    Text(
                      utf8.decode(
                          latin1.encode('Classe : ${emploi['classeName']}')),
                      style:
                          TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 10),
                    if (emploi['filePath'] != null &&
                        emploi['filePath'].isNotEmpty)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Fichier associé :',
                            style: TextStyle(
                                fontSize: 16, fontWeight: FontWeight.bold),
                          ),
                          SizedBox(height: 14),
                          ElevatedButton.icon(
                            icon: Icon(Icons.visibility),
                            label: Text('Consulter'),
                            onPressed: () async {
                              print(
                                  '[CONSULTER] Bouton pressé - Début du traitement');
                              final now = DateTime.now();

                              if (_isConsulting) {
                                print(
                                    '[CONSULTER] Bloqué: _isConsulting = true');
                                return;
                              }

                              if (_lastConsultTap != null &&
                                  now.difference(_lastConsultTap!) <
                                      Duration(seconds: 1)) {
                                print(
                                    '[CONSULTER] Bloqué: Double-clic détecté');
                                return;
                              }

                              print(
                                  '[CONSULTER] Démarrage de la consultation...');
                              _lastConsultTap = now;
                              setState(() {
                                _isConsulting = true;
                                print('[CONSULTER] _isConsulting = true');
                              });

                              try {
                                print('[CONSULTER] Préparation du fichier...');
                                final filePath = emploi['filePath'] ?? '';
                                print(
                                    '[CONSULTER] Chemin du fichier: $filePath');

                                final url = filePath.startsWith('https')
                                    ? filePath
                                    : '${ApiUrl.baseUrl}${filePath.startsWith('//') ? '' : '/'}$filePath';
                                print('[CONSULTER] URL complète: $url');

                                final directory =
                                    await getApplicationDocumentsDirectory();
                                final localFile = File(
                                    '${directory.path}/${url.split('/').last}');
                                print(
                                    '[CONSULTER] Fichier local: ${localFile.path}');

                                if (!localFile.existsSync()) {
                                  print(
                                      '[CONSULTER] Téléchargement du fichier...');
                                  Dio dio = Dio();
                                  await dio.download(url, localFile.path);
                                  print('[CONSULTER] Téléchargement terminé');
                                } else {
                                  print(
                                      '[CONSULTER] Fichier déjà existant en local');
                                }

                                if (context.mounted) {
                                  print(
                                      '[CONSULTER] Navigation vers PDFViewerScreen...');
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => PDFViewerScreen(
                                          filePath: localFile.path),
                                    ),
                                  );
                                  print('[CONSULTER] Navigation effectuée');
                                }
                              } catch (e) {
                                print('[CONSULTER] ERREUR: $e');
                                debugPrint(
                                    'Erreur lors de l\'ouverture du fichier : $e');
                                if (context.mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                          'Erreur : Impossible de consulter le fichier.'),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                }
                              } finally {
                                if (mounted) {
                                  print(
                                      '[CONSULTER] Nettoyage final - _isConsulting = false');
                                  setState(() => _isConsulting = false);
                                }
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ],
                      )
                    else
                      Text(
                        'Aucun fichier associé.',
                        style: TextStyle(
                            fontSize: 16, fontStyle: FontStyle.italic),
                      ),
                  ],
                ),
                if (_isLoading) // Display the loader when isLoading is true
                  Center(
                    child: CircularProgressIndicator(),
                  ),
              ],
            ),
          ),
          actions: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                ElevatedButton.icon(
                  icon: Icon(Icons.download),
                  label: Text('Télécharger'),
                  onPressed: () async {
                    try {
                      final filePath = emploi['filePath'] ?? '';
                      final url = filePath.startsWith('http')
                          ? filePath
                          : '${ApiUrl.baseUrl}${filePath.startsWith('/') ? '' : '/'}$filePath';

                      debugPrint('URL générée pour le téléchargement : $url');

                      final fileName = url.split('/').last;

                      await _downloadFile(context, url, fileName);
                    } catch (e) {
                      debugPrint('Erreur lors du téléchargement : $e');
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                                'Erreur : Impossible de télécharger le fichier.'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
                SizedBox(height: 8),
                ElevatedButton.icon(
                  icon: Icon(Icons.delete),
                  label: Text('Supprimer'),
                  onPressed: () {
                    _showDeleteConfirmationDialog(emploi);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.pink,
                    foregroundColor: Colors.white,
                  ),
                ),
                SizedBox(height: 8),
                ElevatedButton(
                  child: Text('Fermer'),
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Emplois du temps',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 22,
          ),
        ),
        backgroundColor: Colors.green,
        iconTheme: IconThemeData(color: Colors.white),
      ),
      body: isLoading
          ? Center(child: CircularProgressIndicator()) // Affiche le loader ici
          : Container(
              color: Colors.green,
              child: Center(
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.9,
                  height: MediaQuery.of(context).size.height * 0.7,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.shade400,
                        blurRadius: 8,
                        offset: Offset(0, 4),
                      ),
                    ],
                  ),
                  padding: EdgeInsets.all(16),
                  child: activites.isEmpty
                      ? Center(
                          child: Text(
                            'Aucun emploi crée.',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        )
                      : GridView.builder(
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            crossAxisSpacing: 16,
                            mainAxisSpacing: 16,
                            childAspectRatio: 1,
                          ),
                          itemCount: activites.length,
                          itemBuilder: (context, index) {
                            return Container(
                              decoration: BoxDecoration(
                                color: const Color.fromARGB(255, 241, 128, 153),
                                borderRadius: BorderRadius.circular(20),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.grey.shade400,
                                    blurRadius: 6,
                                    offset: Offset(0, 3),
                                  ),
                                ],
                              ),
                              child: InkWell(
                                onTap: () {
                                  _showDetailsPopup(activites[index]);
                                },
                                child: Padding(
                                  padding: EdgeInsets.all(8.0),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Text(
                                        utf8.decode(latin1
                                            .encode(activites[index]['name']!)),
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 18,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                      SizedBox(height: 8),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                ),
              ),
            ),
      floatingActionButton: Padding(
        padding: const EdgeInsets.only(bottom: 20.0, right: 10.0),
        child: FloatingActionButton(
          onPressed: _showAddActivityPopup,
          child: Icon(Icons.add, size: 30),
          foregroundColor: Colors.white,
          backgroundColor: Colors.green,
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      bottomNavigationBar: const MyFooter(currentRoute: '/directrice/menu'),
    );
  }
}

class AddActivityPopup extends StatefulWidget {
  final Function(int classId, String name, PlatformFile file) onAdd;
  final List<Classe> classes;

  const AddActivityPopup({
    Key? key,
    required this.onAdd,
    required this.classes,
  }) : super(key: key);

  @override
  _AddActivityPopupState createState() => _AddActivityPopupState();
}

class _AddActivityPopupState extends State<AddActivityPopup> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  int? _selectedClassId;
  PlatformFile? _selectedFile;
  bool _isSubmitting = false;

  String? _fileError;

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  Future<void> _pickFile() async {
    try {
      // Ouvrir le sélecteur de fichiers avec une restriction sur les fichiers PDF uniquement
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'], // N'accepte que les fichiers PDF
      );

      if (result != null) {
        final file = result.files.first;
        const int maxFileSize = 100 * 1024 * 1024; // Limite de 10 Mo

        // Vérification de la taille du fichier
        if (file.size > maxFileSize) {
          setState(() {
            _fileError = 'Le fichier est trop volumineux (max 10 MB)';
            _selectedFile = null;
          });
          return;
        }

        // Vérification supplémentaire du format du fichier
        if (!file.extension!.toLowerCase().contains('pdf')) {
          setState(() {
            _fileError = 'Seuls les fichiers PDF sont acceptés';
            _selectedFile = null;
          });
          return;
        }

        // Si tout est valide, on sélectionne le fichier
        setState(() {
          _selectedFile = file;
          _fileError = null;
        });
      } else {
        // Aucun fichier sélectionné
        setState(() {
          _fileError = 'Aucun fichier sélectionné';
          _selectedFile = null;
        });
      }
    } catch (e) {
      // Gestion des erreurs
      setState(() {
        _fileError = 'Erreur lors de la sélection du fichier';
        _selectedFile = null;
      });
      debugPrint('Erreur lors de la sélection du fichier: $e');
    }
  }

  Future<void> _submitForm() async {
    if (_formKey.currentState == null ||
        !_formKey.currentState!.validate() ||
        _selectedFile == null) {
      if (_selectedFile == null) {
        setState(() {
          _fileError = 'Veuillez sélectionner un fichier';
        });
      }
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      await widget.onAdd(
        _selectedClassId!,
        _nameController.text.trim(),
        _selectedFile!,
      );
      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'ajout: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildHeader(),
                const SizedBox(height: 24),
                _buildNameField(),
                const SizedBox(height: 16),
                _buildClassDropdown(),
                const SizedBox(height: 16),
                _buildFileSelection(),
                const SizedBox(height: 24),
                _buildActions(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Ajouter un emploi',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        IconButton(
          icon: Icon(Icons.close),
          onPressed: _isSubmitting ? null : () => Navigator.pop(context),
        ),
      ],
    );
  }

  Widget _buildNameField() {
    return TextFormField(
      controller: _nameController,
      decoration: InputDecoration(
        labelText: 'Nom',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.assignment),
      ),
      enabled: !_isSubmitting,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Veuillez entrer un nom';
        }
        return null;
      },
    );
  }

  Widget _buildClassDropdown() {
    return DropdownButtonFormField<int>(
      value: _selectedClassId,
      decoration: InputDecoration(
        labelText: 'Classe',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.class_),
      ),
      items: widget.classes.map((classe) {
        return DropdownMenuItem(
          value: classe.id,
          child: Text(classe.nomClasse),
        );
      }).toList(),
      onChanged: _isSubmitting
          ? null
          : (value) {
              setState(() {
                _selectedClassId = value;
              });
            },
      validator: (value) {
        if (value == null) {
          return 'Veuillez sélectionner une classe';
        }
        return null;
      },
    );
  }

  Widget _buildFileSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ElevatedButton.icon(
          icon: Icon(Icons.upload_file),
          label: Text(_selectedFile == null
              ? 'Sélectionner un fichier'
              : 'Fichier séléctionné'),
          onPressed: _isSubmitting ? null : _pickFile,
          style: ElevatedButton.styleFrom(
            padding: EdgeInsets.symmetric(vertical: 12),
          ),
        ),
        if (_fileError != null)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              _fileError!,
              style: TextStyle(
                color: Colors.red,
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildActions() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: _isSubmitting ? null : () => Navigator.pop(context),
          child: Text('Annuler'),
        ),
        SizedBox(width: 16),
        ElevatedButton(
          onPressed: _isSubmitting ? null : _submitForm,
          child: _isSubmitting
              ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : Text('Ajouter'),
          style: ElevatedButton.styleFrom(
            padding: EdgeInsets.symmetric(
              horizontal: 24,
              vertical: 12,
            ),
          ),
        ),
      ],
    );
  }
}
