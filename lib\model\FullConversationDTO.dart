class FullConversationDTO {
  String content;
  String status; // "sent" ou "received"
  String timestamp;
  bool isRead;
  String fullName;
  String photoUrl;
  int messageCounter; // Added message counter field

  FullConversationDTO({
    required this.content,
    required this.status,
    required this.timestamp,
    required this.isRead,
    required this.fullName,
    required this.photoUrl,
    this.messageCounter = 0, // Default to 0
  });

  factory FullConversationDTO.fromJson(Map<String, dynamic> json) {
    return FullConversationDTO(
      content: json['content'] ?? '',
      status: json['status'] ?? '',
      timestamp: json['timestamp'] ?? '0',
      isRead: json['isRead'] ?? false,
      fullName: json['fullName'] ?? '',
      photoUrl: json['photoUrl'] ?? '',
      messageCounter: json['messageCounter'] ?? 0, // Parse message counter
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'content': content,
      'status': status,
      'timestamp': timestamp,
      'isRead': isRead,
      'FullName': fullName,
      'PhotoUrl': photoUrl,
      'messageCounter': messageCounter,
    };
  }

  FullConversationDTO copyWith({
    String? content,
    String? status,
    String? timestamp,
    bool? isRead,
    String? fullName,
    String? photoUrl,
    int? messageCounter,
  }) {
    return FullConversationDTO(
      content: content ?? this.content,
      status: status ?? this.status,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      fullName: fullName ?? this.fullName,
      photoUrl: photoUrl ?? this.photoUrl,
      messageCounter: messageCounter ?? this.messageCounter,
    );
  }
}
