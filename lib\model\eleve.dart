class Eleve {
  final int id;
  final int? parent_id ;
  final int? classeId ;
  final String nom;
  final String prenom;
  final String? dateDeNaissance;
  final String sexe;



  Eleve({
    required this.id,
    required this.parent_id,
    required this.classeId,
    required this.nom,
    required this.prenom,
    required this.dateDeNaissance,
    required this.sexe,
  });

  // Convert an Eleve object to a Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'parent_id':parent_id,
      'classeId':classeId,
      'nom': nom,
      'prenom': prenom,
      'dateDeNaissance': dateDeNaissance,
      'sexe': sexe,
    };
  }

  // Create an Eleve object from a Map
  factory Eleve.fromMap(Map<String, dynamic> map) {
    return Eleve(
      id: map['id'] ?? 0,
      parent_id:map['parent_id'],
      classeId:map['classeId'],
      nom: map['nom'],
      prenom: map['prenom'],
      dateDeNaissance : map['dateDeNaissance'],
      sexe: map['sexe'],
    );
  }
}





