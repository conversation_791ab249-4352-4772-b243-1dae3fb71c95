import 'package:bee_kids_mobile/view/directrice/footer.dart';
import 'package:bee_kids_mobile/view/educateur/footer.dart';
import 'package:flutter/material.dart';

class LiveVideoEducateur extends StatelessWidget {
  const LiveVideoEducateur({super.key});

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Row(
          children: [
            CircleAvatar(
              backgroundImage: AssetImage('lib/resources/images/logo.png'),
              radius: 15,
            ),
            SizedBox(width: 8),
            Expanded(
              child: Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: 'BEE',
                      style: TextStyle(
                        color: Color(0xFF478B49),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextSpan(
                      text: '-KIDS',
                      style: TextStyle(
                        color: Colors.pink,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(10),
            color: const Color(0xFF478B49),
            child: Text(
              'live Video',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: screenWidth * 0.045,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            child: Center(
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(25),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.3),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: const Text(
                  "Cette fonctionnalité est en cours de développement",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
// In the Scaffold of EducateurHome class
bottomNavigationBar: const MyFooterEducateur(currentRoute: '/educateur/accueil'),
    );
  }
}
