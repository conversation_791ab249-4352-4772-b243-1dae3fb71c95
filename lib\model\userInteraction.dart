class InteractionUser {
  final int userId;
  final String fullName;
  final String role;
  final int totalLikes;
  final int totalCommentaires;
  final int totalInteractions;

  InteractionUser({
    required this.userId,
    required this.fullName,
    required this.role,
    required this.totalLikes,
    required this.totalCommentaires,
    required this.totalInteractions,
  });

  factory InteractionUser.fromJson(Map<String, dynamic> json) {
    return InteractionUser(
      userId: json['userId'],
      fullName: json['fullName'],
      role: json['role'],
      totalLikes: json['totalLikes'],
      totalCommentaires: json['totalCommentaires'],
      totalInteractions: json['totalInteractions'],
    );
  }
}
