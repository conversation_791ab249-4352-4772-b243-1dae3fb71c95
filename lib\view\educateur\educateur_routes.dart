
import 'package:bee_kids_mobile/view/educateur/AccueilPoster.dart';
import 'package:bee_kids_mobile/view/educateur/ActivitesEtProgramme.dart';
import 'package:bee_kids_mobile/view/educateur/EmploiEducateur.dart';
import 'package:bee_kids_mobile/view/educateur/Messagerie/Discussions.dart';
import 'package:bee_kids_mobile/view/educateur/NotificationEducateur.dart';
import 'package:bee_kids_mobile/view/educateur/cantineUI.dart';
import 'package:bee_kids_mobile/view/educateur/evennement.dart';
import 'package:bee_kids_mobile/view/educateur/live.dart';
import 'package:bee_kids_mobile/view/educateur/menu.dart';
import 'package:bee_kids_mobile/view/educateur/profile.dart';
import 'package:bee_kids_mobile/view/educateur/suivie_enfant.dart';
import 'package:bee_kids_mobile/view/educateur/suivie_enfants.dart';
import 'package:flutter/material.dart';
import 'package:bee_kids_mobile/view/educateur/AccueilEducateur.dart';

class EducateurRoutes {
  static Route<dynamic>? onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case '/educateur':
      // Check if arguments contain a postId
      if (settings.arguments != null) {
        final args = settings.arguments as Map<String, dynamic>?;
        final postId = args?['postId'] as int?;
        return MaterialPageRoute(builder: (_) => EducateurHome(postId: postId));
      }
        return MaterialPageRoute(
            builder: (_) => const EducateurHome()); // Main screen for educateur
      case '/educateur/accueil':
      // Check if arguments contain a postId
      if (settings.arguments != null) {
        final args = settings.arguments as Map<String, dynamic>?;
        final postId = args?['postId'] as int?;
        return MaterialPageRoute(builder: (_) => EducateurHome(postId: postId));
      }
        return MaterialPageRoute(builder: (_) => const EducateurHome());
      case '/educateur/menu':
        return MaterialPageRoute(builder: (_) => const MenuScreenEducateur());
      case '/educateur/suivie_enfants':
        return MaterialPageRoute(
            builder: (_) => const SuivieEnfantsEducateur());
      case '/educateur/suivie_enfant':
        final args = settings.arguments;
        if (args is Map<String, dynamic>) {
          return MaterialPageRoute(
            builder: (_) => SuivieEnfantEducateur(
              eleveId: args['id'] as int,
              dateDeNaissance: args['dateDeNaissance'] as String,
              nom: args['nom'] as String,
              prenom: args['prenom'] as String,
            ),
          );
        } else {
          print('Invalid or missing arguments: $args');
          return MaterialPageRoute(
            builder: (_) => Scaffold(
              body: Center(
                child: Text(
                  'Invalid arguments for route: ${settings.name}',
                  style: const TextStyle(fontSize: 18, color: Colors.red),
                ),
              ),
            ),
          );
        }
      case '/educateur/cantine':
        return MaterialPageRoute(
            builder: (_) => const CantineScreenEducateur());
      case '/educateur/Discussions':
        return MaterialPageRoute(builder: (_) => DiscussionsScreenEducateur());
      case '/educateur/Evennements':
  if (settings.arguments != null) {
    final args = settings.arguments as Map<String, dynamic>?;
    final selectedDate = args?['selectedDate'];
    if (selectedDate != null) {
      // Convert to DateTime if it's a string
      DateTime parsedDate;
      if (selectedDate is String) {
        try {
          parsedDate = DateTime.parse(selectedDate);
        } catch (e) {
          parsedDate = DateTime.now();
        }
      } else if (selectedDate is DateTime) {
        parsedDate = selectedDate;
      } else {
        parsedDate = DateTime.now();
      }
      return MaterialPageRoute(builder: (_) => CalendarAppEducateur(initialDate: parsedDate));
    }
  }
  return MaterialPageRoute(builder: (_) => CalendarAppEducateur());

      case '/educateur/profile':
        return MaterialPageRoute(builder: (_) => UserProfilePageEducateur());
      case '/educateur/poster':
        return MaterialPageRoute(
            builder: (_) => AcceuilPosterScreenEducateur());
      case '/educateur/enAttente':
        return MaterialPageRoute(builder: (_) => NotificationEducateur());
     case '/educateur/ActivitesEtProgrammeEducateur':
        return MaterialPageRoute(builder: (_) => ActivitesEtProgrammeEducateur());
         case '/educateur/EmploiEducateur':
         final args = settings.arguments;
        if (args is Map<String, dynamic>) {
          return MaterialPageRoute(
            builder: (_) => EmploiEducateur(
              nomClasse: args['nomClasse'] as String,
              id: args['id'] as int,
            ),
          );
        } else {
          print('Invalid or missing arguments: $args');
          return MaterialPageRoute(
            builder: (_) => Scaffold(
              body: Center(
                child: Text(
                  'Invalid arguments for route: ${settings.name}',
                  style: const TextStyle(fontSize: 18, color: Colors.red),
                ),
              ),
            ),
          );
        }
            case '/educateur/live':
        return MaterialPageRoute(builder: (_) => LiveVideoEducateur());

      default:
        return MaterialPageRoute(
          builder: (_) => Scaffold(
            body: Center(
              child: Text('Route non trouvée: ${settings.name}'),
            ),
          ),
        );
    }
  }
}
