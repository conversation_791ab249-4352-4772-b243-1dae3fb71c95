import 'dart:convert';
import 'dart:io';
import 'package:bee_kids_mobile/resources/environnement/apiUrl.dart';
import 'package:bee_kids_mobile/view/educateur/EmploiEducateur.dart';
import 'package:bee_kids_mobile/view/educateur/footer.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:bee_kids_mobile/model/classe.dart';
import 'package:bee_kids_mobile/view_model/classeService.dart';
import 'package:bee_kids_mobile/view_model/EmploiService.dart';

typedef EmploiData = Map<String, dynamic>;

class ActivitesEtProgrammeEducateur extends StatefulWidget {
  @override
  _ActivitesEtProgrammeState createState() => _ActivitesEtProgrammeState();
}

class _ActivitesEtProgrammeState extends State<ActivitesEtProgrammeEducateur> {
  final classeService _classeService = classeService();
  final EmploiService _emploiService = EmploiService();
  List<Classe> classes = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    print("Initialisation de l'écran ActivitesEtProgrammeEducateur");
    _configureStatusBar();
    _fetchClasses();
  }

  void _configureStatusBar() {
    print("Configuration de la barre de statut");
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      systemNavigationBarColor:
          Colors.transparent, // Barre de navigation transparente
      systemNavigationBarIconBrightness: Brightness.light,
      statusBarColor: Colors.transparent, // Barre de statut transparente
      statusBarIconBrightness: Brightness.light,
    ));
  }

  void _fetchClasses() async {
    try {
      print("Récupération des classes...");
      final response = await _classeService.getClassesByEducateurId();
      setState(() {
        classes = response.isNotEmpty ? response : [];
      });
      print("Classes récupérées : ${classes.length}");
    } catch (e) {
      print('Erreur lors de la récupération des classes: $e');
    }
  }

  Future<void> _checkEmploiAndNavigate(
      BuildContext context, int classeId, String nomClasse) async {
    if (_isLoading) return;
    setState(() {
      _isLoading = true;
    });
    print(
        "Vérification de l'emploi du temps pour la classe $nomClasse (ID: $classeId)");

    try {
      final dernieremploi =
          await _emploiService.getLatestEmploiByClasseId(classeId);

      if (dernieremploi == null) {
        print("Aucun emploi trouvé pour la classe $nomClasse");
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text('Aucun emploi trouvé'),
              content: Text(
                  'Pas d\'emploi pour le moment pour la classe $nomClasse.'),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: Text('OK', style: TextStyle(color: Colors.green)),
                ),
              ],
            );
          },
        );
      } else {
        print(
            "Emploi du temps trouvé pour la classe $nomClasse, navigation vers l'écran d'emploi...");
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (_) => EmploiEducateur(
              nomClasse: nomClasse,
              id: classeId,
            ),
          ),
        ).then((_) {
          setState(() {
            _isLoading = false;
          });
        });
      }
    } catch (e) {
      print('Erreur lors de la récupération de l\'emploi: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Aucun emploi crée pour cette classe.'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return Scaffold(
      extendBody: true, // Étendre le corps sous la barre de navigation
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pushNamed(context, '/educateur/menu'),
        ),
        title: const Text(
          'Emplois du temps',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.green,
        elevation: 5,
      ),
      body: SafeArea(
        child: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.white, Colors.white],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          child: Column(
            children: [
              Expanded(
                child: ListView.builder(
                  padding: EdgeInsets.symmetric(vertical: screenHeight * 0.02),
                  itemCount: classes.length,
                  itemBuilder: (context, index) {
                    final classe = classes[index];
                    print("Affichage de la classe : ${classe.nomClasse}");
                    return Center(
                      child: Card(
                        margin: EdgeInsets.symmetric(
                          vertical: screenHeight * 0.01,
                          horizontal: screenWidth * 0.1,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(screenWidth * 0.06),
                        ),
                        color: const Color.fromARGB(255, 241, 128, 153),
                        elevation: 4,
                        child: ListTile(
                          contentPadding: EdgeInsets.all(screenWidth * 0.04),
                          leading: Container(
                            padding: EdgeInsets.all(screenWidth * 0.02),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.3),
                              shape: BoxShape.circle,
                            ),
                            child:
                                const Icon(Icons.class_, color: Colors.white),
                          ),
                          title: Text(
                            classe.nomClasse,
                            style: TextStyle(
                              fontSize: screenWidth * 0.045,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                          trailing: const Icon(Icons.arrow_forward_ios,
                              color: Colors.white),
                          onTap: _isLoading
                              ? null
                              : () {
                                  _checkEmploiAndNavigate(
                                      context, classe.id, classe.nomClasse);
                                },
                        ),
                      ),
                    );
                  },
                ),
              ),
              SizedBox(height: screenHeight * 0.03),
            ],
          ),
        ),
      ),
      bottomNavigationBar:
          const MyFooterEducateur(currentRoute: '/educateur/menu'),
    );
  }
}
