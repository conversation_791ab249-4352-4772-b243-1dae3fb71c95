class Suivis {
  final int id;
  final DateTime? jour;
  final String repas;
  final String sommeil;
  final String participation;
  final String jeuxExterieur;
  final String interaction;
  final String humeur;
  final String note;
   String? eleveNom;
   String? formateurNom;
   int? formateurId;
   int? eleveId;

  // Constructor
  Suivis({
    required this.id,
    required this.jour,
    required this.repas,
    required this.sommeil,
    required this.participation,
    required this.jeuxExterieur,
    required this.interaction,
    required this.humeur,
    required this.note, 
     this.eleveId,
     this.formateurId,
    
/*     required this.eleveNom,
    required this.formateurNom, */
  });

  // Method to convert Suivis object to a map (useful for database or API)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'jour': jour?.toIso8601String(),
      'repas': repas,
      'sommeil': sommeil,
      'participation': participation,
      'jeuxExterieur': jeuxExterieur,
      'interaction': interaction,
      'humeur': humeur,
      'note': note,
     // 'eleveNom': eleveNom,
     // 'formateurNom': formateurNom,
     'eleveId': eleveId,
     'formateurId': formateurId,
    };
  }

  // Method to create a Suivis object from a map (useful for database or API)
  factory Suivis.fromMap(Map<String, dynamic> map) {
    return Suivis(
      id: map['id'],
      jour: DateTime.parse(map['jour']),
      repas: map['repas'],
      sommeil: map['sommeil'],
      participation: map['participation'],
      jeuxExterieur: map['jeuxExterieur'],
      interaction: map['interaction'],
      humeur: map['humeur'],
      note: map['note'],
     /*  eleveNom: map['eleveNom'],
      formateurNom: map['formateurNom'], */
     eleveId: map['eleveId'],
     formateurId: map['formateurId'],
    );
  }

    // Define a toString method for better debugging and printing
  @override
  String toString() {
    return 'Suivis{id: $id, jour: $jour, repas: $repas, sommeil: $sommeil, '
           'participation: $participation, jeuxExterieur: $jeuxExterieur, '
           'interaction: $interaction, humeur: $humeur, note: $note, '
           'eleveNom: $eleveNom, formateurNom: $formateurNom}'
           'eleveId: $eleveId, formateurId: $formateurId}'
           ;
  }
  
}

