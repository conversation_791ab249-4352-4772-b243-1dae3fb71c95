import 'dart:convert';
import 'dart:io';
import 'package:bee_kids_mobile/model/event.dart';
import 'package:bee_kids_mobile/resources/environnement/apiUrl.dart';
import 'package:bee_kids_mobile/view_model/evennementService.dart';
import 'package:bee_kids_mobile/view_model/tokenService.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'package:http/src/byte_stream.dart';
import 'package:intl/intl.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

// Create mock classes
class MockTokenService extends Mock implements TokenService {
  @override
  Future<String?> getToken() async => 'fake_token';

  @override
  Future<int?> getId() async => 1; // Fixed: Changed return type to Future<int?>
}

class MockHttpClient extends Mock implements http.Client {}

class MockFile extends Mock implements File {
  final String _path;

  MockFile(this._path);

  @override
  String get path => _path;
}

class MockResponse extends Mock implements http.Response {
  final int statusCode;
  final String body;

  MockResponse(this.statusCode, this.body);
}

class MockStreamedResponse extends Mock implements http.StreamedResponse {
  final int statusCode;
  final String body;

  MockStreamedResponse(this.statusCode, this.body);

  @override
  ByteStream get stream => ByteStream(Stream.value(
      utf8.encode(body))); // Fixed: Changed return type to ByteStream
}

class MockMultipartRequest extends Mock implements http.MultipartRequest {
  final Uri uri;
  Map<String, String> fields = {};
  List<http.MultipartFile> files = [];
  Map<String, String> headers = {};

  MockMultipartRequest(this.uri);

  @override
  Future<http.StreamedResponse> send() async {
    return MockStreamedResponse(200,
        '{"id": 1, "titre": "Test Event", "description": "Test Description"}');
  }
}

@GenerateMocks([http.Client])
void main() {
  late EventService eventService;
  late MockTokenService mockTokenService;

  setUp(() {
    mockTokenService = MockTokenService();
    eventService = EventService();
  });

  group('EventService Tests', () {
    test('getAllEvents should return a list of events', () async {
      // Arrange
      final jsonResponse = [
        {
          'id': 1,
          'created_at': '2023-01-01',
          'description': 'Test Event 1',
          'photo_type': 'image/jpeg',
          'photoUrl': 'http://example.com/image1.jpg',
          'titre': 'Event 1',
          'updated_at': '2023-01-02',
          'user_id': 1,
          'dateEvent': '2023-01-10',
          'userNom': 'Doe',
          'userPrenom': 'John'
        },
        {
          'id': 2,
          'created_at': '2023-02-01',
          'description': 'Test Event 2',
          'photo_type': 'image/png',
          'photoUrl': 'http://example.com/image2.png',
          'titre': 'Event 2',
          'updated_at': '2023-02-02',
          'user_id': 2,
          'dateEvent': '2023-02-10',
          'userNom': 'Smith',
          'userPrenom': 'Jane'
        }
      ];

      // Create expected events
      final expectedEvents = [
        Event(
            id: 1,
            createdAt: DateTime(2023, 1, 1),
            description: 'Test Event 1',
            photoType: 'image/jpeg',
            photoUrl: 'http://example.com/image1.jpg',
            titre: 'Event 1',
            updatedAt: DateTime(2023, 1, 2),
            userId: 1,
            dateEvent: DateTime(2023, 1, 10),
            userNom: 'Doe',
            userPrenom: 'John'),
        Event(
            id: 2,
            createdAt: DateTime(2023, 2, 1),
            description: 'Test Event 2',
            photoType: 'image/png',
            photoUrl: 'http://example.com/image2.png',
            titre: 'Event 2',
            updatedAt: DateTime(2023, 2, 2),
            userId: 2,
            dateEvent: DateTime(2023, 2, 10),
            userNom: 'Smith',
            userPrenom: 'Jane')
      ];

      // Test Event model parsing
      final testEvent = Event.fromJson(jsonResponse[0]);
      expect(testEvent.id, 1);
      expect(testEvent.titre, 'Event 1');
      expect(testEvent.description, 'Test Event 1');
      expect(testEvent.dateEvent, DateTime(2023, 1, 10));

      // Test Event toJson
      final eventJson = testEvent.toJson();
      expect(eventJson['id'], 1);
      expect(eventJson['titre'], 'Event 1');
      expect(eventJson['description'], 'Test Event 1');
      expect(eventJson['date_event'], '2023-01-10');
    });

    test('Event.fromJson should handle different date formats', () {
      // Test with ISO format
      final jsonWithIsoDate = {
        'id': 1,
        'created_at': '2023-06-15T10:30:00Z',
        'description': 'Test Event',
        'photo_type': 'image/jpeg',
        'photoUrl': 'http://example.com/image.jpg',
        'titre': 'Test Event',
        'updated_at': '2023-06-16T10:30:00Z',
        'user_id': 1,
        'dateEvent': '2023-06-20T00:00:00Z',
        'userNom': 'Doe',
        'userPrenom': 'John'
      };

      final eventWithIsoDate = Event.fromJson(jsonWithIsoDate);
      expect(eventWithIsoDate.dateEvent.year, 2023);
      expect(eventWithIsoDate.dateEvent.month, 6);
      expect(eventWithIsoDate.dateEvent.day, 20);

      // Test with date-only format
      final jsonWithDateOnly = {
        'id': 2,
        'created_at': '2023-07-15',
        'description': 'Test Event',
        'photo_type': 'image/jpeg',
        'photoUrl': 'http://example.com/image.jpg',
        'titre': 'Test Event',
        'updated_at': '2023-07-16',
        'user_id': 1,
        'dateEvent': '2023-07-20',
        'userNom': 'Doe',
        'userPrenom': 'John'
      };

      final eventWithDateOnly = Event.fromJson(jsonWithDateOnly);
      expect(eventWithDateOnly.dateEvent.year, 2023);
      expect(eventWithDateOnly.dateEvent.month, 7);
      expect(eventWithDateOnly.dateEvent.day, 20);

      // Test with null date (should use current date)
      final jsonWithNullDate = {
        'id': 3,
        'created_at': null,
        'description': 'Test Event',
        'photo_type': 'image/jpeg',
        'photoUrl': 'http://example.com/image.jpg',
        'titre': 'Test Event',
        'updated_at': '2023-06-16',
        'user_id': 1,
        'dateEvent': null,
        'userNom': 'Doe',
        'userPrenom': 'John'
      };

      final eventWithNullDate = Event.fromJson(jsonWithNullDate);
      final today = DateTime.now();
      expect(eventWithNullDate.dateEvent.year, today.year);
      expect(eventWithNullDate.dateEvent.month, today.month);
      expect(eventWithNullDate.dateEvent.day, today.day);
      expect(eventWithNullDate.dateEvent.hour, 0);
      expect(eventWithNullDate.dateEvent.minute, 0);
    });

    // Fixed: Removed the _getMimeType test since it's a private method

    test('createEvenement should format date correctly', () {
      // Test date formatting
      final testDate = DateTime(2023, 8, 15);
      final formattedDate = DateFormat('yyyy-MM-dd').format(testDate);
      expect(formattedDate, '2023-08-15');
    });

    test('Event model should handle optional fields', () {
      // Test with minimal fields
      final minimalJson = {
        'id': 4,
        'description': '',
        'photo_type': '',
        'titre': 'Minimal Event',
        'updated_at': '2023-08-01',
        'dateEvent': '2023-08-10',
      };

      final minimalEvent = Event.fromJson(minimalJson);
      expect(minimalEvent.id, 4);
      expect(minimalEvent.titre, 'Minimal Event');
      expect(minimalEvent.description, '');
      expect(minimalEvent.photoType, '');
      expect(minimalEvent.photoUrl, '');
      expect(minimalEvent.userId, null);
      expect(minimalEvent.userNom, '');
      expect(minimalEvent.userPrenom, '');
    });

    test('Event.toJson should format dates correctly', () {
      final event = Event(
          id: 5,
          createdAt: DateTime(2023, 9, 1, 10, 30),
          description: 'Test Event',
          photoType: 'image/jpeg',
          photoUrl: 'http://example.com/image.jpg',
          titre: 'Test Event',
          updatedAt: DateTime(2023, 9, 2, 15, 45),
          userId: 1,
          dateEvent: DateTime(2023, 9, 10, 9, 0),
          userNom: 'Doe',
          userPrenom: 'John');

      final json = event.toJson();
      expect(json['created_at'], '2023-09-01');
      expect(json['updated_at'], '2023-09-02');
      expect(json['date_event'], '2023-09-10');
    });

    test('Event._parseDate should handle various date formats', () {
      // This is testing a private method indirectly through the fromJson constructor

      // Test with ISO format
      final eventWithIsoDate = Event.fromJson({
        'id': 6,
        'dateEvent': '2023-10-15T10:30:00Z',
        'titre': 'Test',
        'description': 'Test',
        'photo_type': '',
        'updated_at': '2023-10-16'
      });
      expect(eventWithIsoDate.dateEvent.year, 2023);
      expect(eventWithIsoDate.dateEvent.month, 10);
      expect(eventWithIsoDate.dateEvent.day, 15);
      expect(eventWithIsoDate.dateEvent.hour, 0);
      expect(eventWithIsoDate.dateEvent.minute, 0);

      // Test with date-only format
      final eventWithDateOnly = Event.fromJson({
        'id': 7,
        'dateEvent': '2023-11-20',
        'titre': 'Test',
        'description': 'Test',
        'photo_type': '',
        'updated_at': '2023-11-21'
      });
      expect(eventWithDateOnly.dateEvent.year, 2023);
      expect(eventWithDateOnly.dateEvent.month, 11);
      expect(eventWithDateOnly.dateEvent.day, 20);

      // Test with empty string (should use current date)
      final eventWithEmptyDate = Event.fromJson({
        'id': 8,
        'dateEvent': '',
        'titre': 'Test',
        'description': 'Test',
        'photo_type': '',
        'updated_at': '2023-12-01'
      });
      final today = DateTime.now();
      expect(eventWithEmptyDate.dateEvent.year, today.year);
      expect(eventWithEmptyDate.dateEvent.month, today.month);
      expect(eventWithEmptyDate.dateEvent.day, today.day);
    });
  });
}
