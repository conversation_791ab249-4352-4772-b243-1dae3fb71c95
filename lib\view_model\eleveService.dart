// ignore_for_file: file_names

import 'dart:convert';

import 'package:bee_kids_mobile/model/eleve.dart';
import 'package:http/http.dart' as http;
import '../resources/environnement/apiUrl.dart';
import '../view_model/tokenService.dart';
class eleveService {
  final baseUrl = ApiUrl.baseUrl;
  final TokenService tokenService = TokenService();

  // Get headers with Authorization token
  Future<Map<String, String>> _getAuthHeaders() async {
    final token = await tokenService.getToken();
    if (token != null) {
      return {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };
    }
    return {
      'Content-Type': 'application/json',
    };
  }


    Future<List<Eleve>> getAllElevesByParentId() async {
     final headers = await _getAuthHeaders();
     int? parentId = await tokenService.getId();
    // print ('parentId == > $parentId');
     try {
      final response = await http.get(Uri.parse('${baseUrl}eleves/parParent/$parentId'),headers: headers,);

      if (response.statusCode == 200) {
        // Parse the JSON response into a list of Eleve objects
        List<dynamic> data = json.decode(response.body);

        List<Eleve> eleves = data.map((item) => Eleve.fromMap(item)).toList();
        return eleves;
    } else {
      throw Exception('Failed to load students by parent Id');
    }
  } catch (e) {
    throw Exception('Error fetching students by parent Id: $e');
  }
}

     // Function to fetch all students by educateur id (élèves)
  Future<List<Eleve>> getAllElevesByEducateurId() async {
     final headers = await _getAuthHeaders();
     int? educateurId = await tokenService.getId();
     try {
      final response = await http.get(Uri.parse('${baseUrl}eleves/formateur/$educateurId'),headers: headers,);

      if (response.statusCode == 200) {
        // Parse the JSON response into a list of Eleve objects
        List<dynamic> data = json.decode(response.body);

        List<Eleve> eleves = data.map((item) => Eleve.fromMap(item)).toList();
        return eleves;
    } else {
      throw Exception('Failed to load students by educateurId');
    }
  } catch (e) {
    throw Exception('Error fetching students by educateurId: $e');
  }
}


   // Function to fetch all students (élèves)
  Future<List<Eleve>> getAllEleves() async {
     final headers = await _getAuthHeaders();
     try {
      final response = await http.get(Uri.parse('${baseUrl}eleves/all'),headers: headers,);

      if (response.statusCode == 200) {
        // Parse the JSON response into a list of Eleve objects
        List<dynamic> data = json.decode(response.body);

        List<Eleve> eleves = data.map((item) => Eleve.fromMap(item)).toList();
        return eleves;
    } else {
      throw Exception('Failed to load students');
    }
  } catch (e) {
    throw Exception('Error fetching students: $e');
  }
}

  Future<Eleve> getEleveById(int id) async {
     final headers = await _getAuthHeaders();
    try {
      final response = await http.get(Uri.parse('${baseUrl}eleves/$id'),headers: headers,);

      if (response.statusCode == 200) {
        // Parse the JSON response into an Eleve object
        var data = json.decode(response.body);
        return Eleve.fromMap(data);
      } else {
        throw Exception('Failed to load student with id: $id');
      }
    } catch (e) {
            throw Exception('Error fetching student: $e');
    }
  }


    Future<String> getPhotoByEleveById(int id) async {
     final headers = await _getAuthHeaders();
    try {
      final response = await http.get(Uri.parse('${baseUrl}api/eleves/photos/$id'),headers: headers,);

      if (response.statusCode == 200) {
        if (response.body.isNotEmpty) {
          return response.body;
        } else {
          return '';
        }
        return response.body;
      } else {
        throw Exception('Failed to load student photo with id: $id');
      }
    } catch (e) {
            throw Exception('Error fetching student photo: $e');
    }
  }


////////////// Tableau de bord ////////////

Future<int> getNombresTotalesEleves() async {
  final url = Uri.parse('${baseUrl}eleves/NombresTotalesEleves');
  final headers = await _getAuthHeaders();

  final response = await http.get(url, headers: headers);
  print('🔁getNombresTotalesEleves Response: ${response.body}');

  if (response.statusCode == 200) {
    final Map<String, dynamic> data = jsonDecode(response.body);
    final total = data['totalEleves'];
    if (total is int) {
      return total;
    } else {
      throw Exception("Champ 'totalEleves' absent ou invalide : ${response.body}");
    }
  } else {
    throw Exception('getNombresTotalesEleves Erreur API: ${response.statusCode} - ${response.body}');
  }
}



}
