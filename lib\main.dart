import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:bee_kids_mobile/routes/app_routes.dart';
import 'package:bee_kids_mobile/view_model/Notification_Background_handler.dart';
import 'package:bee_kids_mobile/view_model/firebase_service.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

// Static minimum version defined by developer (include build number if needed)
const String MINIMUM_REQUIRED_VERSION = "1.1.1+39";

// Static store URLs defined by developer
const String ANDROID_STORE_URL =
    "https://apps.apple.com/tn/app/beekids/id6739808095";
const String IOS_STORE_URL =
    "https://play.google.com/store/apps/details?id=com.beeKids.bee_kids";

Future<bool> shouldForceUpdate() async {
  try {
    final packageInfo = await PackageInfo.fromPlatform();
    final currentVersion = packageInfo.version;
    final currentBuildNumber = packageInfo.buildNumber;

    // Combine version and build number to match pubspec.yaml format
    final fullCurrentVersion = "$currentVersion+$currentBuildNumber";

    print(
        "Current version: $fullCurrentVersion — Min required: $MINIMUM_REQUIRED_VERSION");

    return _isVersionLower(fullCurrentVersion, MINIMUM_REQUIRED_VERSION);
  } catch (e) {
    print('Error in shouldForceUpdate: $e');
    return false;
  }
}

bool _isVersionLower(String current, String min) {
  try {
    // Split version and build number
    List<String> currentParts = current.split('+');
    List<String> minParts = min.split('+');

    String currentVersionPart = currentParts[0];
    String minVersionPart = minParts[0];

    // Compare version numbers first (e.g., 1.1.0 vs 2.0.0)
    List<int> currentVersionNumbers =
        currentVersionPart.split('.').map(int.parse).toList();
    List<int> minVersionNumbers =
        minVersionPart.split('.').map(int.parse).toList();

    // Ensure both lists have the same length by padding with zeros
    while (currentVersionNumbers.length < minVersionNumbers.length) {
      currentVersionNumbers.add(0);
    }
    while (minVersionNumbers.length < currentVersionNumbers.length) {
      minVersionNumbers.add(0);
    }

    // Compare version numbers
    for (int i = 0; i < minVersionNumbers.length; i++) {
      if (currentVersionNumbers[i] < minVersionNumbers[i]) {
        return true; // Current version is lower
      } else if (currentVersionNumbers[i] > minVersionNumbers[i]) {
        return false; // Current version is higher
      }
    }

    // If version numbers are equal, compare build numbers
    if (currentParts.length > 1 && minParts.length > 1) {
      int currentBuildNumber = int.parse(currentParts[1]);
      int minBuildNumber = int.parse(minParts[1]);

      return currentBuildNumber < minBuildNumber;
    } else if (minParts.length > 1) {
      // Min version has build number but current doesn't, consider current as lower
      return true;
    }

    // Versions are equal
    return false;
  } catch (e) {
    print('Error comparing versions: $e');
    return false;
  }
}

// Add this global navigator key
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

// Add this global variable to track Firebase initialization
bool _firebaseInitialized = false;

Future<void> initializeFirebase() async {
  if (_firebaseInitialized) return;

  await Firebase.initializeApp();
  _firebaseInitialized = true;
  debugPrint("Firebase initialized globally");
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Check for force update before initializing anything else
  bool forceUpdate = await shouldForceUpdate();
  if (forceUpdate) {
    runApp(MaterialApp(
      debugShowCheckedModeBanner: false,
      home: ForceUpdateScreen(),
    ));
    return;
  }

  // Initialize Firebase first and only once
  await initializeFirebase();

  // Initialize notification channels and settings
  await initializeNotifications();

  // IMPORTANT: Register background handler ONLY ONCE
  FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

  // Disable default notification presentation
  await disableDefaultNotifications();

  // Initialize Firebase service LAST - but don't let it crash the app
  try {
    await FirebaseService().initialize();
  } catch (e) {
    debugPrint('⚠️ Firebase service initialization failed: $e');
    debugPrint('📱 App will continue without full Firebase functionality');
  }

  runApp(MyApp());
}

Future<void> initializeNotifications() async {
  // Android initialization
  const AndroidInitializationSettings initializationSettingsAndroid =
      AndroidInitializationSettings('@mipmap/ic_launcher');

  // iOS initialization
  final DarwinInitializationSettings initializationSettingsIOS =
      DarwinInitializationSettings(
    requestAlertPermission: true,
    requestBadgePermission: true,
    requestSoundPermission: true,
  );

  final InitializationSettings initializationSettings = InitializationSettings(
    android: initializationSettingsAndroid,
    iOS: initializationSettingsIOS,
  );

  // Initialize the plugin
  await flutterLocalNotificationsPlugin.initialize(
    initializationSettings,
    onDidReceiveNotificationResponse: (NotificationResponse response) {
      // Handle notification tap
      final payload = response.payload;
      if (payload != null) {
        try {
          final data = Map<String, dynamic>.from(jsonDecode(payload));
          // Forward to FirebaseService for consistent handling
          FirebaseService().handleNotificationOpen(data);
        } catch (e) {
          debugPrint('Error parsing notification payload: $e');
        }
      }
    },
  );

  // Create notification channel for Android
  const AndroidNotificationChannel channel = AndroidNotificationChannel(
    'high_importance_channel',
    'High Importance Notifications',
    description: 'This channel is used for important notifications.',
    importance: Importance.max,
    playSound: true,
    enableVibration: true,
  );

  await flutterLocalNotificationsPlugin
      .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin>()
      ?.createNotificationChannel(channel);
}

Future<void> disableDefaultNotifications() async {
  // Disable default presentation for iOS
  await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
    alert: false, // Disable system alerts
    badge: true, // Allow badge updates
    sound: false, // Disable system sounds
  );

  // NEW: For Android, set notification priority to min for the default channel
/*   if (Platform.isAndroid) {
    // This prevents FCM from automatically displaying notifications
    await FirebaseMessaging.instance.setAutoInitEnabled(false);

    // Ensure we're subscribed to the topics we need
    // This is still needed even with auto-init disabled
    final token = await FirebaseMessaging.instance.getToken();
    if (token != null) {
      debugPrint("📱 FCM Token obtained manually: $token");
      // Register token with your backend
      await Future.delayed(Duration(seconds: 5));
      FirebaseService().registerFCMTokenWithBackend(token);
    }
  } */
}

class AppLifecycleObserver extends WidgetsBindingObserver {
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {}
}

class MyApp extends StatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // IMPORTANT: Don't register duplicate handlers here
    // Let FirebaseService handle all notifications

    // Only check for initial message and forward to FirebaseService
    FirebaseMessaging.instance.getInitialMessage().then((message) {
      if (message != null && message.data.isNotEmpty) {
        FirebaseService().handleNotificationOpen(message.data);
      }
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      navigatorKey: navigatorKey,
      debugShowCheckedModeBanner: false,
      title: 'Bee Kids',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      initialRoute: AppRoutes.login,
      onGenerateRoute: AppRoutes.generateRoute,
    );
  }
}

class BackendErrorScreen extends StatefulWidget {
  const BackendErrorScreen({Key? key}) : super(key: key);

  @override
  State<BackendErrorScreen> createState() => _BackendErrorScreenState();
}

class _BackendErrorScreenState extends State<BackendErrorScreen> {
  bool _isLoading = false;

  Future<void> _checkConnection() async {
    setState(() {
      _isLoading = true;
    });

    //bool isBackendAvailable = await checkBackendConnection();

    setState(() {
      _isLoading = false;
    });

    runApp(const MyApp());
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'Bee Kids',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.white),
        useMaterial3: true,
      ),
      home: Scaffold(
        appBar: AppBar(
          title: const Text(
            'Bee Kids',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 24,
              color: Colors.white,
            ),
          ),
          centerTitle: true,
          backgroundColor: Colors.green[800],
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.cloud_off,
                size: 80,
                color: Colors.green[800],
              ),
              const SizedBox(height: 20),
              Text(
                'Connexion impossible',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.pink,
                ),
              ),
              const SizedBox(height: 12),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 40),
                child: Text(
                  'Le serveur est actuellement inaccessible. Veuillez vérifier votre connexion internet et réessayer.',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16),
                ),
              ),
              const SizedBox(height: 24),
              _isLoading
                  ? CircularProgressIndicator(
                      color: Colors.pink,
                    )
                  : ElevatedButton(
                      onPressed: _checkConnection,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green[800],
                        padding: const EdgeInsets.symmetric(
                          horizontal: 32,
                          vertical: 16,
                        ),
                      ),
                      child: const Text(
                        'Réessayer',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                    ),
            ],
          ),
        ),
      ),
    );
  }
}

class ForceUpdateScreen extends StatefulWidget {
  @override
  State<ForceUpdateScreen> createState() => _ForceUpdateScreenState();
}

class _ForceUpdateScreenState extends State<ForceUpdateScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _slideController;
  late Animation<double> _pulseAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  bool _isUpdating = false;

  @override
  void initState() {
    super.initState();

    // Pulse animation for the update icon
    _pulseController = AnimationController(
      duration: Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Slide animation for content
    _slideController = AnimationController(
      duration: Duration(milliseconds: 800),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOut,
    ));

    // Start animations
    _pulseController.repeat(reverse: true);
    _slideController.forward();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  void _launchUpdateUrl() async {
    setState(() {
      _isUpdating = true;
    });

    final url = Platform.isAndroid ? ANDROID_STORE_URL : IOS_STORE_URL;

    try {
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      } else {
        print('Could not launch $url');
        _showErrorSnackBar();
      }
    } catch (e) {
      print('Error launching URL: $e');
      _showErrorSnackBar();
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  void _showErrorSnackBar() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Impossible d\'ouvrir le store. Veuillez réessayer.'),
        backgroundColor: Colors.red[600],
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.blue[50]!,
              Colors.green[50]!,
              Colors.orange[50]!,
            ],
            stops: [0.0, 0.5, 1.0],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Animated update icon
                    AnimatedBuilder(
                      animation: _pulseAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _pulseAnimation.value,
                          child: Container(
                            width: 120,
                            height: 120,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Colors.orange[400]!,
                                  Colors.deepOrange[500]!,
                                ],
                              ),
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.orange.withOpacity(0.3),
                                  blurRadius: 20,
                                  spreadRadius: 5,
                                ),
                              ],
                            ),
                            child: Icon(
                              Icons.system_update_alt,
                              size: 60,
                              color: Colors.white,
                            ),
                          ),
                        );
                      },
                    ),

                    SizedBox(height: 40),

                    // Title
                    Text(
                      'Mise à jour requise',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[800],
                        letterSpacing: 0.5,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    SizedBox(height: 16),

                    // Subtitle
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 20),
                      child: Text(
                        'Une nouvelle version de BeeKids est disponible avec des améliorations importantes.',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                          height: 1.5,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),

                    SizedBox(height: 32),

                    // Features card
                    Container(
                      margin: EdgeInsets.symmetric(horizontal: 16),
                      padding: EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 10,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          Text(
                            'Nouveautés de cette version',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey[800],
                            ),
                          ),
                          SizedBox(height: 16),
                          _buildFeatureItem(
                            Icons.security,
                            'Sécurité renforcée',
                            Colors.green[600]!,
                          ),
                          _buildFeatureItem(
                            Icons.speed,
                            'Performances améliorées',
                            Colors.blue[600]!,
                          ),
                          _buildFeatureItem(
                            Icons.bug_report,
                            'Corrections de bugs',
                            Colors.orange[600]!,
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: 40),

                    // Update button
                    Container(
                      width: double.infinity,
                      height: 56,
                      margin: EdgeInsets.symmetric(horizontal: 16),
                      child: ElevatedButton(
                        onPressed: _isUpdating ? null : _launchUpdateUrl,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green[600],
                          foregroundColor: Colors.white,
                          elevation: 8,
                          shadowColor: Colors.green.withOpacity(0.4),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                        ),
                        child: _isUpdating
                            ? Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white,
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 12),
                                  Text(
                                    'Ouverture...',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              )
                            : Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Platform.isAndroid
                                        ? Icons.shop
                                        : Icons.apple,
                                    size: 24,
                                  ),
                                  SizedBox(width: 12),
                                  Text(
                                    Platform.isAndroid
                                        ? 'Mettre à jour sur Play Store'
                                        : 'Mettre à jour sur App Store',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                      ),
                    ),

                    SizedBox(height: 20),

                    // Info text
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 20),
                      child: Text(
                        'Cette mise à jour est obligatoire pour continuer à utiliser l\'application.',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[500],
                          fontStyle: FontStyle.italic,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureItem(IconData icon, String text, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              size: 18,
              color: color,
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Icon(
            Icons.check_circle,
            size: 16,
            color: Colors.green[500],
          ),
        ],
      ),
    );
  }
}
