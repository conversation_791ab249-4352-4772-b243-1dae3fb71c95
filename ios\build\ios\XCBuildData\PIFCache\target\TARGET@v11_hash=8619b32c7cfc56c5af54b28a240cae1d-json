{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9855dabe56d469eefea177bf6af2099306", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98acfb6dc7d710f02238573b78675d49f2", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a4db316249e0cfe9a954b4ff20938efe", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980989764483a905a16ad64a39a5bd0066", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a4db316249e0cfe9a954b4ff20938efe", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b80d9b33cc091c3e70740c20735c162a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982a70cd80294b33173a26bede7bd9c941", "guid": "bfdfe7dc352907fc980b868725387e98c8c5dbab1cc387141919e76d321f3b72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98775ef1dcadc545a05b624e0c3910ae5d", "guid": "bfdfe7dc352907fc980b868725387e98fb0715890e78ff9d8fc79478c32f2ad1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7d4717cf05a1fb696198bcf86d99dbd", "guid": "bfdfe7dc352907fc980b868725387e98da800fb5e420313a1ec86f2101c708ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986caf0f35d2ef8cec7a4a0055a723d989", "guid": "bfdfe7dc352907fc980b868725387e98649ba8915fbf117f678f719830ca57a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8460aa47a430322a5e4a9817129d7ba", "guid": "bfdfe7dc352907fc980b868725387e981108a7567c861d2a81e6b152e5629178", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985720657df14b5a1d845af65207b640f4", "guid": "bfdfe7dc352907fc980b868725387e9864202455627ba5bce36f89d72ce25208", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2a34bf4ef9d48bb517c5efa4845b2b3", "guid": "bfdfe7dc352907fc980b868725387e986d8234ea945e4f24910a87416444cb0f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0d6fa499f0032905c40b2a24d1dcd1c", "guid": "bfdfe7dc352907fc980b868725387e9881324397d78579fa1be74a56dac8b377", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987185cd85234d9c66df8212609af33b0e", "guid": "bfdfe7dc352907fc980b868725387e9843c2d6592723459c2e0550d94a78965b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9ba973164a5ced07efbac623416750a", "guid": "bfdfe7dc352907fc980b868725387e983839329726d71522d237dfa70820337a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811efef6aa3d766c316483e0569d8bf46", "guid": "bfdfe7dc352907fc980b868725387e98e5fb1ccdeb68cbeb218707e1e4c31cba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e784643380599506a9aa4053cc2f3e2f", "guid": "bfdfe7dc352907fc980b868725387e98e117d34050ddafebe0fae77e53175839", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f92c301cd4a82a962bc49c01b6644b1", "guid": "bfdfe7dc352907fc980b868725387e989d47abc260a39b73b58f8db59e793eee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0963de5c3a7dfaa34f45dc135d094fa", "guid": "bfdfe7dc352907fc980b868725387e98b2eb8e4ce8d2f8675faad5161f423c51", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98657e2ea8ccdd76ee9000dff19481d00c", "guid": "bfdfe7dc352907fc980b868725387e98f53cc1a712661ccc841236b6a7bee9ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890be2c81dda6c394b3930043ddf6cfb9", "guid": "bfdfe7dc352907fc980b868725387e98ae489c44c65c9dff69dbaa772c8d2736", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e04cf30ab261b6e8dad770b1bb6a54c", "guid": "bfdfe7dc352907fc980b868725387e98fbde7ff74a740ea09ecde94f91e68100", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a35ff0bf93ba71b0e1a92f49ec8203f", "guid": "bfdfe7dc352907fc980b868725387e98adb76f60a77c944436a1e74a2b26419c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843bbe812d300d5338793881b31a2f6de", "guid": "bfdfe7dc352907fc980b868725387e981081e793da60790a345fec1ef5564ef6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bcda8b4e3cd24439e4333d9a929cf31", "guid": "bfdfe7dc352907fc980b868725387e98cab1f191f2df0d51e8813c78667de2e1", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986391b77831ca480d67d8cc94dd43539f", "guid": "bfdfe7dc352907fc980b868725387e9898efaec758226803e65db91826616f92", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d25e0dd5f1904280a9ce3defde535f7", "guid": "bfdfe7dc352907fc980b868725387e9848fdce90a110b407d774b091badc71d5", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981379793fb6b7d9e8b60839164072fae2", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e6661f4959a956ba0822b481b6b6f359", "guid": "bfdfe7dc352907fc980b868725387e9853480efe4fa162c7a2e0bae2f4a0ff3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c14fda5340f1f841151ceb43d3259dab", "guid": "bfdfe7dc352907fc980b868725387e981b286f13bd0780f972c1f55617843c2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ed484256d9de0abf6e5304e29c7de1e", "guid": "bfdfe7dc352907fc980b868725387e982350ea2a63175c77341bfb3ea215befa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e935f64fc605cb9ebd80ce14ef72a18f", "guid": "bfdfe7dc352907fc980b868725387e9801ad664b222eccb8b4dd3248a363a256"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ddf86cd9a82862b8f1d3ba38964a51c", "guid": "bfdfe7dc352907fc980b868725387e9837c8ce049d16bddee606bb237b2a0f1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819b585d47ad95f5ef25a9df5bc51d333", "guid": "bfdfe7dc352907fc980b868725387e98aeabf614e3a62a11e505b03a815e540c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b724d94f63b3483f81eeaf885099c0ad", "guid": "bfdfe7dc352907fc980b868725387e989501412681400df10d2607a883882bd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984adaacc55910fd48d1bfa55f9f08e135", "guid": "bfdfe7dc352907fc980b868725387e985790803121e1a68e40942d585a08347d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae895d4448bf05665c82a97bf5b8d0ed", "guid": "bfdfe7dc352907fc980b868725387e98d9f263a951e8c66fd043fa7f5c5b6897"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854467b58fd105980ffa5fced866445bc", "guid": "bfdfe7dc352907fc980b868725387e98186895657524929021a7edc04a25adba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eea26865ce6c82516ba30a0e8fd0808e", "guid": "bfdfe7dc352907fc980b868725387e98b5c8b8aa320291915cd8fab9018570e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98874113970ebfbbe3fd259798e0118929", "guid": "bfdfe7dc352907fc980b868725387e98d91d8a96af577832fc21b65290684378"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98092bad1c64466bc5d4c08605f5dc641e", "guid": "bfdfe7dc352907fc980b868725387e9845e801bee8d480fb4b61c83348ef0c37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0bf2e2efe4511b1afa990c524254557", "guid": "bfdfe7dc352907fc980b868725387e982ad7dc23340f1ee6e71ab7d1b08d461c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bb348d65e454433004faef59e802448", "guid": "bfdfe7dc352907fc980b868725387e98619956e3dd5e4b71c2d0bb45425b5397"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f471202cd8814f7b63c99980453b5d30", "guid": "bfdfe7dc352907fc980b868725387e98fecca00f8d1fdfc74cda5cf5a075418c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983adbf510d9614c8cb8522a4126f94698", "guid": "bfdfe7dc352907fc980b868725387e9860429f53a3a3baa10dc8bb22098c0601"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985015b7aaaeabdbb7d9e86971bbce64c1", "guid": "bfdfe7dc352907fc980b868725387e98f0a5d5bccac1929ceb2499ceba3cec7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edcad9073483f32da409d80636ac5978", "guid": "bfdfe7dc352907fc980b868725387e983612740fdc2ead9ff12aec81ccdd6e03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c4626c97201fb5aad7bcb8c1e22d4f3", "guid": "bfdfe7dc352907fc980b868725387e986abaa33fbbf86719f3ddc2da2bcd7087"}], "guid": "bfdfe7dc352907fc980b868725387e9862c96640697a0bee0144c557f7fe9f3f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e984bafc255c963b13bc482c676c69c4e52"}], "guid": "bfdfe7dc352907fc980b868725387e98f17d45aa4bcaef3aba5286a77bc43e52", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98abf1afe0b8210418176d7238260a0c75", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98302b838c032e9e9648ec871dd1aef60f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}