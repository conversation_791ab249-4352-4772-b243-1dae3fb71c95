group 'com.example.imagegallerysaver'
version '1.0-SNAPSHOT'


buildscript {
    ext.kotlin_version = '1.7.20'
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}


allprojects {
    repositories {
        google()
        mavenCentral()
    }
}


apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'


android {
    namespace 'com.example.imagegallerysaver'  // mettre le namespace approprié
    compileSdkVersion 33


    defaultConfig {
        minSdkVersion 16
        targetSdkVersion 33
        multiDexEnabled true
        // IMPORTANT : Ne PAS mettre applicationId dans une librairie Android
    }


    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
        coreLibraryDesugaringEnabled true
    }


    kotlinOptions {
        jvmTarget = "1.8"
    }
}


dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.2.2'
    implementation 'androidx.multidex:multidex:2.0.1'


    // autres dépendances...
}



