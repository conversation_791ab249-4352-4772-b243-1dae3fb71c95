import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:bee_kids_mobile/model/event.dart';
import 'package:bee_kids_mobile/resources/environnement/apiUrl.dart';
import 'package:bee_kids_mobile/view_model/tokenService.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:intl/intl.dart';
import 'package:image/image.dart' as img;

class EventService {
  final baseUrl = ApiUrl.baseUrl;
  final TokenService tokenService = TokenService();
  final userId = TokenService().getId();

  // Get headers with Authorization token
  Future<Map<String, String>> _getAuthHeaders() async {
    final token = await tokenService.getToken();
    if (token != null) {
      return {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };
    }
    return {
      'Content-Type': 'application/json',
    };
  }

  // Optimized image compression
  Future<File> _compressImage(File imageFile) async {
    try {
      final Uint8List bytes = await imageFile.readAsBytes();
      
      // If image is already small enough, return it
      if (bytes.length < 300000) { // Reduced from 500000 to 300000
        return imageFile;
      }
      
      final img.Image? image = img.decodeImage(bytes);
      if (image == null) return imageFile;
      
      // More aggressive resizing for faster processing
      int newWidth = image.width;
      int newHeight = image.height;
      
      // Reduce max dimensions for faster processing
      const int maxDimension = 600; // Reduced from 800
      
      if (image.width > maxDimension || image.height > maxDimension) {
        if (image.width > image.height) {
          newWidth = maxDimension;
          newHeight = (image.height * maxDimension / image.width).round();
        } else {
          newHeight = maxDimension;
          newWidth = (image.width * maxDimension / image.height).round();
        }
      }
      
      // Use faster interpolation
      final img.Image resized = img.copyResize(
        image,
        width: newWidth,
        height: newHeight,
        interpolation: img.Interpolation.nearest, // Faster than linear
      );
      
      // More aggressive compression
      final List<int> compressedBytes = img.encodeJpg(resized, quality: 60); // Reduced from 70
      
      final String tempPath = '${imageFile.parent.path}/temp_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final File compressedFile = File(tempPath);
      await compressedFile.writeAsBytes(compressedBytes);
      
      return compressedFile;
    } catch (e) {
      print('Error compressing image: $e');
      return imageFile;
    }
  }

  Future<void> _cleanupTempFile(File file) async {
    try {
      if (file.path.contains('temp_') && await file.exists()) {
        await file.delete();
      }
    } catch (e) {
      print('Error cleaning up temp file: $e');
    }
  }

  Future<Event> createEvenement({
    required String titre,
    required String description,
    required DateTime dateEvent,
    required int? userId,
    File? photo,
  }) async {
    final uri = Uri.parse('${baseUrl}api/evenements/create/$userId');
    final formattedDate = DateFormat('yyyy-MM-dd').format(dateEvent);
    
    final client = http.Client();
    File? compressedPhoto;
    
    try {
      // Get headers once
      final headers = await _getAuthHeaders();
      
      final request = http.MultipartRequest('POST', uri)
        ..fields['titre'] = titre
        ..fields['description'] = description
        ..fields['dateEvent'] = formattedDate;
      
      // Only add auth headers, not content-type for multipart
      request.headers.addAll({
        'Authorization': headers['Authorization'] ?? '',
      });

      // Compress image in parallel if it exists
      if (photo != null) {
        // Start compression immediately
        compressedPhoto = await _compressImage(photo);
        final mimeType = _getMimeType(compressedPhoto.path);
        
        request.files.add(
          await http.MultipartFile.fromPath(
            'photo',
            compressedPhoto.path,
            contentType: MediaType(mimeType[0], mimeType[1]),
          ),
        );
      }

      // Reduced timeout for faster failure detection
      final response = await client.send(request).timeout(
        const Duration(seconds: 30), // Reduced from 60
        onTimeout: () {
          throw Exception('Délai d\'attente dépassé - veuillez vérifier votre connexion');
        },
      );
      
      final responseString = await response.stream.bytesToString();
      
      if (response.statusCode != 200 && response.statusCode != 201) {
        throw Exception('Erreur serveur: ${response.statusCode} - $responseString');
      }
      
      final responseJson = json.decode(responseString);
      return Event.fromJson(responseJson);
      
    } catch (e) {
      // Better error handling
      if (e.toString().contains('SocketException') || e.toString().contains('HandshakeException')) {
        throw Exception('Problème de connexion réseau');
      } else if (e.toString().contains('TimeoutException')) {
        throw Exception('Délai d\'attente dépassé');
      } else {
        throw Exception('Erreur lors de la création: ${e.toString()}');
      }
    } finally {
      client.close();
      if (compressedPhoto != null && compressedPhoto != photo) {
        await _cleanupTempFile(compressedPhoto);
      }
    }
  }

  // UPDATE EVENT - simplified without progress indicators
  Future<Event> updateEvenement({
    required int? id,
    required String titre,
    required String description,
    required DateTime dateEvent,
    File? photo,
  }) async {
    final uri = Uri.parse('${baseUrl}api/evenements/update/$id');
    final formattedDate = DateFormat('yyyy-MM-dd').format(dateEvent);
    
    final client = http.Client();
    File? compressedPhoto;
    
    try {
      final headers = await _getAuthHeaders();
      
      final request = http.MultipartRequest('PUT', uri)
        ..fields['titre'] = titre
        ..fields['description'] = description
        ..fields['dateEvent'] = formattedDate;
      
      // Only add auth headers
      request.headers.addAll({
        'Authorization': headers['Authorization'] ?? '',
      });

      if (photo != null) {
        compressedPhoto = await _compressImage(photo);
        final mimeType = _getMimeType(compressedPhoto.path);
        
        request.files.add(
          await http.MultipartFile.fromPath(
            'photo',
            compressedPhoto.path,
            contentType: MediaType(mimeType[0], mimeType[1]),
          ),
        );
      }

      final response = await client.send(request).timeout(
        const Duration(seconds: 30), // Reduced timeout
        onTimeout: () {
          throw Exception('Délai d\'attente dépassé - veuillez vérifier votre connexion');
        },
      );
      
      final responseString = await response.stream.bytesToString();
      
      if (response.statusCode != 200 && response.statusCode != 201) {
        throw Exception('Erreur serveur: ${response.statusCode} - $responseString');
      }
      
      final responseJson = json.decode(responseString);
      return Event.fromJson(responseJson);
      
    } catch (e) {
      if (e.toString().contains('SocketException') || e.toString().contains('HandshakeException')) {
        throw Exception('Problème de connexion réseau');
      } else if (e.toString().contains('TimeoutException')) {
        throw Exception('Délai d\'attente dépassé');
      } else {
        throw Exception('Erreur lors de la mise à jour: ${e.toString()}');
      }
    } finally {
      client.close();
      if (compressedPhoto != null && compressedPhoto != photo) {
        await _cleanupTempFile(compressedPhoto);
      }
    }
  }

  Future<void> deleteEvenement(int id) async {
    final headers = await _getAuthHeaders();
    final response = await http.delete(
      Uri.parse('${baseUrl}api/evenements/delete/$id'),
      headers: headers,
    );
    if (response.statusCode == 204) {
      // Success
    } else {
      throw Exception("Erreur HTTP: ${response.statusCode}");
    }
  }

  Future<List<Event>> getAllEvents() async {
    final headers = await _getAuthHeaders();
    final response = await http.get(
      Uri.parse('${baseUrl}api/evenements/getAllEvenements'),
      headers: headers,
    );
   
    if (response.statusCode == 200 || response.statusCode == 201) {
      final List<dynamic> jsonResponse = json.decode(response.body);
      return jsonResponse.map((e) => Event.fromJson(e)).toList();
    } else {
      throw Exception('Failed to fetch events');
    }
  }

  Future<List<Event>> getEventsAfterToday() async {
    final headers = await _getAuthHeaders();
    final response = await http.get(
      Uri.parse('${baseUrl}api/evenements/after-today'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      final List<dynamic> jsonResponse = json.decode(response.body);
      return jsonResponse.map((e) => Event.fromJson(e)).toList();
    } else {
      throw Exception('Échec de récupération des événements');
    }
  }

  Future<List<Event>> getEvenementsByDate(DateTime dateEvent) async {
    final headers = await _getAuthHeaders();
    final formattedDate = DateFormat('yyyy-MM-dd').format(dateEvent);

    final response = await http.get(
      Uri.parse('${baseUrl}api/evenements/date?dateEvent=$formattedDate'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      final List<dynamic> jsonResponse = json.decode(response.body);
      return jsonResponse.map((e) => Event.fromJson(e)).toList();
    } else {
      throw Exception('Aucune événement trouvée pour cette date.');
    }
  }

  List<String> _getMimeType(String filePath) {
    final extension = filePath.split('.').last.toLowerCase();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return ['image', 'jpeg'];
      case 'png':
        return ['image', 'png'];
      default:
        return ['application', 'octet-stream'];
    }
  }


      ////////////// Tableau de bord ////////////

Future<Event> getEventPlusProche() async {
  final headers = await _getAuthHeaders();
  final response = await http.get(
    Uri.parse('${baseUrl}api/evenements/EventPlusProche'),
    headers: headers,
  );

  if (response.statusCode == 200) {
    final jsonMap = json.decode(response.body);
    return Event.fromJson(jsonMap);
  } else {
    throw Exception('Échec de récupération de l\'événement le plus proche');
  }
}

}
