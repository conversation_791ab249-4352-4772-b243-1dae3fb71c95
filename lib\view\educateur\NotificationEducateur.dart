import 'dart:async';
import 'dart:convert';
import 'package:bee_kids_mobile/view/educateur/footer.dart';
import 'package:bee_kids_mobile/view_model/eleveService.dart';
import 'package:bee_kids_mobile/view_model/notificationService.dart';
import 'package:bee_kids_mobile/model/Notification.dart';
import 'package:flutter/material.dart';

class NotificationEducateur extends StatefulWidget {
  const NotificationEducateur({super.key});

  @override
  State<NotificationEducateur> createState() => _NotificationEducateurState();
}

class _NotificationEducateurState extends State<NotificationEducateur> {
  final NotificationService _notificationService = NotificationService();
  final eleveService _eleveService = eleveService();
  late Future<List<NotificationModel>> _notifications;
  StreamSubscription? _notificationSubscription;

  // Define BeeKids theme colors
  final Color beeGreen = const Color(0xFF478B49);
  final Color beePink = Colors.pink;
  final Color beeBackground = const Color(0xFFF5F5F5);

  @override
  void initState() {
    super.initState();
    _notifications = _notificationService.fetchNotifications();

    // Listen for real-time updates
    _notificationSubscription = _notificationService.notificationUpdates.listen((message) {
      if (mounted) {
        setState(() {
          _notifications = _notificationService.fetchNotifications();
        });
      }
    });

    // Add a periodic refresh as fallback
    Timer.periodic(const Duration(minutes: 1), (timer) {
      if (mounted) {
        setState(() {
          _notifications = _notificationService.fetchNotifications();
        });
      }
    });
  }

  @override
  void dispose() {
    _notificationSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: beeBackground,
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: beeGreen),
          onPressed: () => Navigator.pushNamed(context, '/educateur/menu'),
        ),
        backgroundColor: Colors.white,
        elevation: 2,
        shadowColor: Colors.black.withOpacity(0.1),
        title: Row(
          children: [
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const CircleAvatar(
                backgroundImage: AssetImage('lib/resources/images/logo.png'),
                radius: 18,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: 'BEE',
                      style: TextStyle(
                        color: beeGreen,
                        fontWeight: FontWeight.bold,
                        fontSize: 20,
                      ),
                    ),
                    TextSpan(
                      text: '-KIDS',
                      style: TextStyle(
                        color: beePink,
                        fontWeight: FontWeight.bold,
                        fontSize: 20,
                      ),
                    ),
                  ],
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        // Add a centered title for the notifications page
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(48),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 12),
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 3,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.notifications_active,
                  color: beeGreen,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Notifications',
                  style: TextStyle(
                    color: beeGreen,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.white,
              beeBackground,
            ],
          ),
        ),
        child: RefreshIndicator(
          onRefresh: () async {
            setState(() {
              _notifications = _notificationService.fetchNotifications();
            });
          },
          color: beeGreen,
          child: _buildNotificationsContent(),
        ),
      ),
      bottomNavigationBar:
          const MyFooterEducateur(currentRoute: '/educateur/enAttente'),
    );
  }

  Widget _buildNotificationsContent() {
    return FutureBuilder<List<NotificationModel>>(
      future: _notifications,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  color: beeGreen,
                  strokeWidth: 3,
                ),
                const SizedBox(height: 20),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 10,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 5,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Text(
                    'Chargement des notifications...',
                    style: TextStyle(
                      color: Colors.grey[700],
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: beePink.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.error_outline,
                    size: 60,
                    color: beePink,
                  ),
                ),
                const SizedBox(height: 20),
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 40),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 5,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Text(
                        'Erreur: ${snapshot.error}',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: beePink,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        onPressed: () {
                          setState(() {
                            _notifications =
                                _notificationService.fetchNotifications();
                          });
                        },
                        icon: const Icon(Icons.refresh),
                        label: const Text('Réessayer'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: beeGreen,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                          elevation: 3,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }

        final notifications = snapshot.data;

        if (notifications == null || notifications.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(25),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.notifications_off_rounded,
                    size: 80,
                    color: Colors.grey[400],
                  ),
                ),
                const SizedBox(height: 24),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 30,
                    vertical: 16,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Text(
                    'Aucune notification disponible',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                TextButton.icon(
                  onPressed: () {
                    setState(() {
                      _notifications =
                          _notificationService.fetchNotifications();
                    });
                  },
                  icon: Icon(Icons.refresh, color: beeGreen),
                  label: Text(
                    'Actualiser',
                    style: TextStyle(
                      color: beeGreen,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        notifications.sort((a, b) {
          // Try to parse timeStamps safely
          int timeA = 0;
          int timeB = 0;

          try {
            if (a.timeStamp != null) {
              if (a.timeStamp!.contains('T')) {
                // Handle ISO format date string
                timeA = DateTime.parse(a.timeStamp!).millisecondsSinceEpoch;
              } else {
                // Handle numeric timeStamp
                timeA = int.parse(a.timeStamp!);
              }
            }
          } catch (e) {
            debugPrint('Error parsing timeStamp A: ${a.timeStamp}');
          }

          try {
            if (b.timeStamp != null) {
              if (b.timeStamp!.contains('T')) {
                // Handle ISO format date string
                timeB = DateTime.parse(b.timeStamp!).millisecondsSinceEpoch;
              } else {
                // Handle numeric timeStamp
                timeB = int.parse(b.timeStamp!);
              }
            }
          } catch (e) {
            debugPrint('Error parsing timeStamp B: ${b.timeStamp}');
          }

          return timeB.compareTo(timeA); // Reverse order for newest first
        });

        // Count unread notifications
        int unreadCount = notifications.where((n) => !n.isRead).length;

        return Column(
          children: [
            if (unreadCount > 0)
              Container(
                margin: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      beeGreen.withOpacity(0.8),
                      beeGreen,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: beeGreen.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                      child: Text(
                        unreadCount.toString(),
                        style: TextStyle(
                          color: beeGreen,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Vous avez $unreadCount notification${unreadCount > 1 ? 's' : ''} non lue${unreadCount > 1 ? 's' : ''}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        // Mark all as read functionality could be added here
                      },
                      icon: const Icon(
                        Icons.mark_email_read,
                        color: Colors.white,
                      ),
                      tooltip: 'Marquer tout comme lu',
                    ),
                  ],
                ),
              ),
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: notifications.length,
                itemBuilder: (context, index) {
                  final notification = notifications[index];

                  // Determine if this is the first notification of a new day
                  bool showDateHeader = false;
                  String? currentDate;

                  try {
                    DateTime notifDate;
                    if (notification.timeStamp != null) {
                      if (notification.timeStamp!.contains('T')) {
                        notifDate = DateTime.parse(notification.timeStamp!);
                      } else {
                        notifDate = DateTime.fromMillisecondsSinceEpoch(
                            int.parse(notification.timeStamp!));
                      }

                      // Add 1 hour to the time
                      notifDate = notifDate.add(const Duration(hours: 1));

                      currentDate =
                          '${notifDate.day}/${notifDate.month}/${notifDate.year}';

                      if (index == 0) {
                        showDateHeader = true;
                      } else {
                        final prevNotification = notifications[index - 1];
                        DateTime prevDate;
                        if (prevNotification.timeStamp!.contains('T')) {
                          prevDate =
                              DateTime.parse(prevNotification.timeStamp!);
                        } else {
                          prevDate = DateTime.fromMillisecondsSinceEpoch(
                              int.parse(prevNotification.timeStamp!));
                        }

                        // Add 1 hour to the time
                        prevDate = prevDate.add(const Duration(hours: 1));

                        final prevDateStr =
                            '${prevDate.day}/${prevDate.month}/${prevDate.year}';
                        showDateHeader = currentDate != prevDateStr;
                      }
                    }
                  } catch (e) {
                    debugPrint('Error calculating date header: $e');
                  }

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (showDateHeader && currentDate != null)
                        Padding(
                          padding: const EdgeInsets.only(
                              left: 8, right: 8, top: 16, bottom: 8),
                          child: Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 6,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.grey[200],
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Text(
                                  currentDate,
                                  style: TextStyle(
                                    color: Colors.grey[800],
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Divider(
                                  color: Colors.grey[300],
                                  thickness: 1,
                                  indent: 8,
                                ),
                              ),
                            ],
                          ),
                        ),
                      Container(
                        margin: const EdgeInsets.only(bottom: 12),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              blurRadius: 5,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Card(
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                            side: !notification.isRead
                                ? BorderSide(color: beeGreen, width: 1.5)
                                : BorderSide.none,
                          ),
                          color: !notification.isRead
                              ? Colors.white
                              : const Color.fromARGB(255, 245, 245, 245),
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Container(
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                          color: beeGreen,
                                          width: 2,
                                        ),
                                        boxShadow: [
                                          BoxShadow(
                                            color:
                                                Colors.black.withOpacity(0.1),
                                            blurRadius: 4,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: CircleAvatar(
                                        backgroundImage: NetworkImage(
                                          notification.photoUrl ??
                                              'https://via.placeholder.com/150',
                                        ),
                                        onBackgroundImageError:
                                            (exception, stackTrace) {
                                          debugPrint(
                                              'Error loading image: $exception');
                                        },
                                        radius: 24,
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            getNotificationTitle(
                                                notification.is1to1Message,
                                                notification.isGroupMessage,
                                                notification.isEvent,
                                                notification.isNewPubReq,
                                                notification.isNewPub,
                                                notification.is1to1Message,
                                                notification.isGroupMessage,
                                                notification.content),
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 16,
                                              color: !notification.isRead
                                                  ? beeGreen
                                                  : Colors.black87,
                                            ),
                                          ),
                                          const SizedBox(height: 4),
                                          Row(
                                            children: [
                                              Icon(
                                                Icons.access_time,
                                                size: 12,
                                                color: Colors.grey[600],
                                              ),
                                              const SizedBox(width: 4),
                                              Text(
                                                formatTime(
                                                    notification.timeStamp),
                                                style: TextStyle(
                                                  color: Colors.grey[600],
                                                  fontSize: 12,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    Container(
                                      decoration: BoxDecoration(
                                        color: beeGreen.withOpacity(0.1),
                                        shape: BoxShape.circle,
                                      ),
                                      child: IconButton(
                                        onPressed: () async {
                                          if (notification.isGroupMessage ||
                                              notification.is1to1Message) {
                                            Navigator.pushNamed(context,
                                                '/educateur/Discussions');
                                          } else if (notification.isSuivie) {
                                            if (notification.eleveId !=
                                                null) {
                                              // Show loading indicator
                                              showDialog(
                                                context: context,
                                                barrierDismissible: false,
                                                builder:
                                                    (BuildContext context) {
                                                  return Center(
                                                    child:
                                                        CircularProgressIndicator(
                                                      color: beeGreen,
                                                    ),
                                                  );
                                                },
                                              );

                                              // Fetch student details
                                              final student =
                                                  await _eleveService
                                                      .getEleveById(
                                                          notification
                                                              .eleveId!);

                                              // Close loading indicator
                                              Navigator.pop(context);

                                              // Navigate to suivie_enfant with the required arguments
                                              Navigator.pushNamed(
                                                context,
                                                '/educateur/suivie_enfant',
                                                arguments: {
                                                  'id': student.id,
                                                  'dateDeNaissance':
                                                      student.dateDeNaissance,
                                                  'nom': student.nom,
                                                  'prenom': student.prenom,
                                                },
                                              );
                                            }
                                          } else if (notification.content
                                              .contains('Vous avez')) {
                                            Navigator.pushNamed(
                                              context,
                                              '/educateur/Evennements',
                                              arguments: {
                                                'selectedDate':
                                                    notification.dateEvent,
                                              },
                                            );
                                          } else if (notification.isMenu) {
                                            Navigator.pushNamed(context,
                                                '/educateur/cantine');
                                          } else if (notification.content
                                              .contains(
                                                  'une nouvelle publication')) {
                                            Navigator.pushNamed(
                                                context, '/educateur',
                                                arguments: {
                                                  'postId':
                                                      notification.pubId,
                                                });
                                          }

                                          _notificationService.markAsRead(
                                              notification.notificationId!);
                                        },
                                        icon: Icon(
                                          Icons.visibility,
                                          color: beeGreen,
                                          size: 22,
                                        ),
                                        tooltip: 'Voir le détail',
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 12),
                                Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Colors.grey[100],
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: Colors.grey[300]!,
                                      width: 1,
                                    ),
                                  ),
                                  child: Text(
                                    _safeDecodeContent(notification.content),
                                    style: const TextStyle(fontSize: 14),
                                  ),
                                ),
                                if (!notification.isRead)
                                  Align(
                                    alignment: Alignment.centerRight,
                                    child: Container(
                                      margin: const EdgeInsets.only(top: 8),
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 10,
                                        vertical: 4,
                                      ),
                                     /*    decoration: BoxDecoration(
                                          color: beeGreen.withOpacity(0.1),
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          border: Border.all(color: beeGreen),
                                        ),
                                        child: Text(
                                          'Non lu',
                                          style: TextStyle(
                                            color: beeGreen,
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ), */
                                      ),
                                    ),
                                  
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  String _safeDecodeContent(String content) {
    try {
      // Third attempt: try the latin1 to utf8 conversion
      return utf8.decode(latin1.encode(content));
    } catch (e) {
      // If all else fails, return a placeholder
      return content;
    }
  }

  String getRouteUrl(String? content) {
    if (content == null) return 'Nouvelle Notification';
    final lowerContent = content.toLowerCase();
    if (lowerContent.contains('message')) {
      return '/educateur/Discussions';
    } else if (lowerContent.contains('suivi')) {
      return '/educateur/suivie_enfants';
    } else if (lowerContent.contains('vous avez un ')) {
      return '/educateur/Evennements';
    }
    return 'Nouvelle Notification';
  }

  String getNotificationTitle(
      bool isMenu,
      bool isSuivie,
      bool isEvent,
      bool isNewPubReq,
      bool isNewPub,
      bool is1to1Message,
      bool isGroupMessage,
      String content) {
    final lowerContent = content.toLowerCase();
    if (lowerContent.contains('vous avez un ')) {
      return 'Nouvel Évènement';
    } else if (lowerContent.contains('menu')) {
      return 'Nouveau Menu';
    } else if (isSuivie) {
      return 'Nouveau Suivi';
    } else if (isNewPub) {
      return 'Nouvelle Publication';
    }

    return 'Nouvelle Notification';
  }

  String formatDate(String? timeStamp) {
    if (timeStamp == null || timeStamp.isEmpty) return 'No date';
    try {
      DateTime dateTime;
      if (timeStamp.contains('T')) {
        dateTime = DateTime.parse(timeStamp);
      } else {
        final timeInMillis = int.parse(timeStamp);
        dateTime = DateTime.fromMillisecondsSinceEpoch(timeInMillis);
      }

      // Add 1 hour to the time
      dateTime = dateTime.add(const Duration(hours: 1));

      return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      debugPrint('Error formatting date: $timeStamp - $e');
      return 'Invalid date';
    }
  }

  String formatTime(String? timeStamp) {
    if (timeStamp == null || timeStamp.isEmpty) return '';
    try {
      DateTime dateTime;
      if (timeStamp.contains('T')) {
        dateTime = DateTime.parse(timeStamp);
      } else {
        final timeInMillis = int.parse(timeStamp);
        dateTime = DateTime.fromMillisecondsSinceEpoch(timeInMillis);
      }

      // Add 1 hour to the time
      dateTime = dateTime.add(const Duration(hours: 1));

      return '${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      debugPrint('Error formatting time: $timeStamp - $e');
      return '';
    }
  }
}
