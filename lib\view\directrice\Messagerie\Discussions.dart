import 'dart:convert';

import 'package:bee_kids_mobile/model/UserConversation.dart';
import 'package:bee_kids_mobile/model/messagerie.dart';
import 'package:bee_kids_mobile/model/userDTO.dart';
import 'package:bee_kids_mobile/view/directrice/footer.dart';
import 'package:bee_kids_mobile/view_model/WebSocketService.dart';
import 'package:flutter/material.dart';
import 'package:bee_kids_mobile/view/directrice/Messagerie/Conversation.dart';
import 'package:bee_kids_mobile/model/user.dart';
import 'package:bee_kids_mobile/view_model/userService.dart';
import 'package:bee_kids_mobile/view_model/messagerieService.dart';
import 'package:bee_kids_mobile/view_model/tokenService.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// This helper function attempts to fix mis‑encoded text (for example, Arabic)
/// by re‑encoding the text from Latin1 to UTF‑8. If it fails (for texts with emoji),
/// it returns the original text.
String fixText(String text) {
  try {
    return utf8.decode(latin1.encode(text));
  } catch (e) {
    return text;
  }
}

class DiscussionsScreen extends StatefulWidget {
  const DiscussionsScreen({Key? key}) : super(key: key);

  @override
  _DiscussionsScreenState createState() => _DiscussionsScreenState();
}

class _DiscussionsScreenState extends State<DiscussionsScreen> {
  final MessagerieService _messagerieService = MessagerieService();
  late WebSocketService _webSocketService;

  final TokenService _tokenService = TokenService();

  String? currentUserId;
  List<dynamic> discussions = [];
  List<dynamic> groupes = [];
  List<User> selectedUsers = [];
  bool isLoading = true;
  bool isDiscussionSelected = true;

  String searchText = ""; // Nouveau : Texte de recherche

  @override
  void initState() {
    super.initState();
    initializeDateFormatting('fr_FR', null);
    _initializeWebSocket();
    _initializeUserId().then((_) {
      fetchDiscussionsAndGroups();
      _messagerieService.resetMessageCounter(); // Add this line
    });
  }

  void _initializeWebSocket() {
    _webSocketService = WebSocketService();

    _webSocketService.conversationUpdates.listen((update) {
      try {
        print('🔔 Nouveau message WebSocket reçu: $update');
        var message = json.decode(update);

        // Vérifier si les clés importantes sont présentes
        if (!message.containsKey('conversationId') ||
            !message.containsKey('content') ||
            !message.containsKey('senderId')) {
          print('⚠ Message WebSocket invalide: $message');
          return;
        }

        setState(() {
          int index = discussions.indexWhere(
              (d) => d['conversationId'] == message['conversationId']);

          if (index != -1) {
            // Mise à jour de la conversation existante
            discussions[index]['lastMessageContent'] = message['content'];
            discussions[index]['messageDate'] =
                DateTime.now().toIso8601String();
          } else {
            // Ajouter une nouvelle conversation si elle n'existe pas
            discussions.insert(0, {
              'conversationId': message['conversationId'],
              'otherUserId': message['senderId'],
              'photoUrl': message['senderPhotoUrl'] ?? '',
              'nom': message['senderName'] ?? 'Utilisateur inconnu',
              'lastMessageContent': message['content'],
              'messageDate': DateTime.now().toIso8601String(),
            });
          }

          print('✅ Discussions mises à jour: $discussions');
        });
      } catch (e) {
        print('❌ Erreur lors du traitement du message WebSocket: $e');
      }
    });
  }

  Future<void> fetchDiscussionsAndGroups() async {
    try {
      if (currentUserId == null) {
        throw Exception('User ID is not initialized.');
      }

      final List<UserConversationDTO> conversations =
          await _messagerieService.getUserConversations(currentUserId!);

      List<Map<String, dynamic>> processedDiscussions = [];
      for (var conversation in conversations) {
        int? timestamp;

        // Validate timestamp
        if (conversation.timestamp != null &&
            conversation.timestamp!.isNotEmpty) {
          timestamp = int.tryParse(conversation.timestamp!);
        }

        if (timestamp == null) {
          print('Invalid or missing timestamp for conversation: $conversation');
          continue;
        }

        DateTime messageDate = DateTime.fromMillisecondsSinceEpoch(timestamp);

        processedDiscussions.add({
          'conversationId': conversation.conversationId,
          'otherUserId': conversation.otherUserId,
          'photoUrl': conversation.photoUrl ?? '',
          'nom': fixText(conversation.fullName),
          'lastMessageContent': conversation.lastMessage != null
              ? fixText(conversation.lastMessage!)
              : 'Pas de message',
          'messageDate': messageDate.toIso8601String(),
        });
      }

      setState(() {
        discussions = processedDiscussions;
        isLoading = false;
      });
    } catch (e) {
      print('Error loading discussions: $e');
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> _initializeUserId() async {
    final prefs = await SharedPreferences.getInstance();
    final id = prefs.getInt('userId'); // Retrieve userId as integer

    if (id != null) {
      setState(() {
        currentUserId = id.toString(); // Convert to String
      });
    } else {
      throw Exception('User ID is not available in shared preferences.');
    }
  }

  void _showRecipientPopup() async {
    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => const Center(child: CircularProgressIndicator()),
    );

    try {
      // Fetch user ID from shared preferences
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getInt('userId')?.toString();

      if (userId == null) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('User ID not found in shared preferences'),
          ),
        );
        return;
      }

      // Fetch users from the service
      UserService userService = UserService();
      List<UserDTO> users =
          await userService.fetchDirecteursAndFormateurs(userId);

      Navigator.pop(context);

      // Show recipient selection popup
      showModalBottomSheet(
        context: context,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(25)),
        ),
        isScrollControlled: true,
        builder: (BuildContext context) {
          return FractionallySizedBox(
            heightFactor: 0.5, // Occupy 50% of the screen height
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(25),
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    'Choisir un Destinataire',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: ListView.builder(
                      itemCount: users.length,
                      itemBuilder: (context, index) {
                        final user = users[index];
                        return GestureDetector(
                          onTap: () {
                            Navigator.pop(context);
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => ConversationScreen(
                                  currentUserId: currentUserId!,
                                  conversationId:
                                      user.conversationId.toString(),
                                  recipientUserId: user.userId.toString(),
                                  recipientUserName: fixText(user.fullName),
                                  recipientPhotoUrl: user.photoUrl ?? '',
                                ),
                              ),
                            );
                          },
                          child: ListTile(
                            leading: CircleAvatar(
                              backgroundImage: user.photoUrl != null &&
                                      user.photoUrl!.isNotEmpty
                                  ? NetworkImage(user.photoUrl!)
                                  : AssetImage(
                                          'lib/resources/images/avatar_girl.png')
                                      as ImageProvider,
                              radius: 20,
                            ),
                            title: Text(
                              fixText(user.fullName),
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    } catch (e) {
      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur lors du chargement des utilisateurs : $e'),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final filteredDiscussions = discussions.where((item) {
      final fullName = '${item['nom']} '.toLowerCase();
      return fullName.contains(searchText.toLowerCase());
    }).toList();

    return Scaffold(
      backgroundColor: const Color.fromARGB(255, 37, 135, 44),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pushNamed(context, '/directrice/menu'),
        ),
        title: const Text(
          "Messagerie",
          style: TextStyle(
              color: Colors.white, fontSize: 24, fontWeight: FontWeight.bold),
        ),
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Color.fromARGB(255, 36, 132, 43),
                Color.fromARGB(255, 37, 135, 44),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
      ),
      body: isLoading
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                        Color.fromARGB(255, 37, 135, 44)),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    "Chargement des conversations...",
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            )
          : RefreshIndicator(
              onRefresh: fetchDiscussionsAndGroups,
              color: const Color.fromARGB(255, 37, 135, 44),
              child: Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(30),
                    topRight: Radius.circular(30),
                  ),
                ),
                child: Column(
                  children: [
                    // En-tête avec barre de recherche améliorée
                    Container(
                      padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(30),
                          topRight: Radius.circular(30),
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            offset: const Offset(0, 2),
                            blurRadius: 10,
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Padding(
                            padding: EdgeInsets.only(left: 4, bottom: 12),
                            child: Text(
                              "Conversations",
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Color.fromARGB(255, 37, 135, 44),
                              ),
                            ),
                          ),
                          Container(
                            height: 50,
                            decoration: BoxDecoration(
                              color: Colors.grey[100],
                              borderRadius: BorderRadius.circular(25),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.05),
                                  offset: const Offset(0, 2),
                                  blurRadius: 4,
                                  spreadRadius: 0,
                                ),
                              ],
                            ),
                            child: TextField(
                              onChanged: (value) {
                                setState(() {
                                  searchText = value;
                                });
                              },
                              decoration: InputDecoration(
                                hintText: "Rechercher par nom ou prénom",
                                hintStyle: TextStyle(
                                  color: Colors.grey[500],
                                  fontSize: 15,
                                ),
                                prefixIcon: const Icon(
                                  Icons.search,
                                  color: Color.fromARGB(255, 37, 135, 44),
                                  size: 22,
                                ),
                                border: InputBorder.none,
                                contentPadding: const EdgeInsets.symmetric(
                                  vertical: 15,
                                  horizontal: 20,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Liste des conversations améliorée
                    Expanded(
                      child: filteredDiscussions.isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.chat_bubble_outline,
                                    size: 70,
                                    color: Colors.grey[400],
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    "Aucune conversation trouvée",
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Colors.grey[600],
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    "Commencez une nouvelle conversation",
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.grey[500],
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : ListView.builder(
                              padding: const EdgeInsets.only(top: 8),
                              itemCount: filteredDiscussions.length,
                              itemBuilder: (context, index) {
                                final item = filteredDiscussions[index];

                                // Formatage de la date pour affichage
                                final DateTime messageDate =
                                    DateTime.parse(item['messageDate']);
                                final DateTime now = DateTime.now();
                                final bool isToday =
                                    messageDate.day == now.day &&
                                        messageDate.month == now.month &&
                                        messageDate.year == now.year;

                                String formattedDate;
                                if (isToday) {
                                  formattedDate =
                                      DateFormat('HH:mm').format(messageDate);
                                } else if (messageDate.isAfter(
                                    now.subtract(const Duration(days: 7)))) {
                                  // Use French locale for day of week
                                  formattedDate = DateFormat('E', 'fr_FR')
                                      .format(messageDate);
                                } else {
                                  formattedDate =
                                      DateFormat('dd/MM').format(messageDate);
                                }

                                return Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 4),
                                  child: Card(
                                    elevation: 0,
                                    margin: EdgeInsets.zero,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(16),
                                      side: BorderSide(
                                        color: Colors.grey.shade200,
                                        width: 1,
                                      ),
                                    ),
                                    child: InkWell(
                                      borderRadius: BorderRadius.circular(16),
                                      onTap: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) =>
                                                ConversationScreen(
                                              currentUserId: currentUserId!,
                                              conversationId:
                                                  item['conversationId']
                                                      .toString(),
                                              recipientUserId:
                                                  item['otherUserId']
                                                      .toString(),
                                              recipientUserName:
                                                  '${item['nom']} ',
                                              recipientPhotoUrl:
                                                  item['photoUrl'],
                                            ),
                                          ),
                                        );
                                      },
                                      child: Padding(
                                        padding: const EdgeInsets.all(12),
                                        child: Row(
                                          children: [
                                            // Photo de profil améliorée
                                            Container(
                                              width: 60,
                                              height: 60,
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                border: Border.all(
                                                  color: const Color.fromARGB(
                                                      255, 37, 135, 44),
                                                  width: 2,
                                                ),
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: Colors.black
                                                        .withOpacity(0.1),
                                                    blurRadius: 5,
                                                    spreadRadius: 0,
                                                    offset: const Offset(0, 2),
                                                  ),
                                                ],
                                              ),
                                              child: ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(30),
                                                child:
                                                    (item['photoUrl'] != null &&
                                                            item['photoUrl']
                                                                .isNotEmpty)
                                                        ? Image.network(
                                                            item['photoUrl'],
                                                            fit: BoxFit.cover,
                                                            errorBuilder: (context,
                                                                    error,
                                                                    stackTrace) =>
                                                                Image.asset(
                                                              'lib/resources/images/avatar_girl.png',
                                                              fit: BoxFit.cover,
                                                            ),
                                                          )
                                                        : Image.asset(
                                                            'lib/resources/images/avatar_girl.png',
                                                            fit: BoxFit.cover,
                                                          ),
                                              ),
                                            ),

                                            const SizedBox(width: 16),

                                            // Informations de la conversation
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      // Nom du contact
                                                      Flexible(
                                                        child: Text(
                                                          item['nom']
                                                              .toString(),
                                                          style:
                                                              const TextStyle(
                                                            fontSize: 16,
                                                            fontWeight:
                                                                FontWeight.w600,
                                                            color:
                                                                Colors.black87,
                                                          ),
                                                          overflow: TextOverflow
                                                              .ellipsis,
                                                        ),
                                                      ),

                                                      // Date du dernier message
                                                      Container(
                                                        padding:
                                                            const EdgeInsets
                                                                .symmetric(
                                                          horizontal: 8,
                                                          vertical: 4,
                                                        ),
                                                        decoration:
                                                            BoxDecoration(
                                                          color: isToday
                                                              ? const Color
                                                                      .fromARGB(
                                                                      255,
                                                                      37,
                                                                      135,
                                                                      44)
                                                                  .withOpacity(
                                                                      0.1)
                                                              : Colors
                                                                  .grey[100],
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(12),
                                                        ),
                                                        child: Text(
                                                          formattedDate,
                                                          style: TextStyle(
                                                            fontSize: 12,
                                                            fontWeight: isToday
                                                                ? FontWeight
                                                                    .w600
                                                                : FontWeight
                                                                    .normal,
                                                            color: isToday
                                                                ? const Color
                                                                    .fromARGB(
                                                                    255,
                                                                    37,
                                                                    135,
                                                                    44)
                                                                : Colors
                                                                    .grey[600],
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),

                                                  const SizedBox(height: 6),

                                                  // Dernier message
                                                  Text(
                                                    item['lastMessageContent']
                                                        .toString(),
                                                    style: TextStyle(
                                                      fontSize: 14,
                                                      color: Colors.grey[600],
                                                      height: 1.3,
                                                    ),
                                                    maxLines: 2,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                    ),
                  ],
                ),
              ),
            ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: const Color.fromARGB(255, 255, 125, 171),
        onPressed: _showRecipientPopup,
        child: const Icon(Icons.add, size: 20),
      ),
      bottomNavigationBar:
          const MyFooter(currentRoute: '/directrice/Discussions'),
    );
  }
}
