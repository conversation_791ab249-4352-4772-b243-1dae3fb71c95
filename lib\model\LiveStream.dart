class LiveStream {
  final int id;
  final int userId;
  final String userName;
  final String streamKey;
  final String streamUrl;
  final DateTime startTime;
  final DateTime? endTime;
  final bool isLive;

  LiveStream({
    required this.id,
    required this.userId,
    required this.userName,
    required this.streamK<PERSON>,
    required this.streamUrl,
    required this.startTime,
    this.endTime,
    required this.isLive,
  });

  factory LiveStream.fromJson(Map<String, dynamic> json) {
    return LiveStream(
      id: json['id'],
      userId: json['userId'],
      userName: json['userName'],
      streamKey: json['streamKey'],
      streamUrl: json['streamUrl'],
      startTime: DateTime.parse(json['startTime']),
      endTime: json['endTime'] != null ? DateTime.parse(json['endTime']) : null,
      isLive: json['isLive'] != null
          ? json['isLive'] as bool
          : false, // Ensure isLive is never null
    );
  }
}
