import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../lib/view_model/tokenService.dart';

void main() {
  late TokenService tokenService;
  
  setUp(() {
    SharedPreferences.setMockInitialValues({});
    tokenService = TokenService();
  });

  group('TokenService Tests', () {
    test('should save and retrieve token successfully', () async {
      // Arrange
      const testToken = 'test_jwt_token';
      const testUserId = 1;
      const testEmail = '<EMAIL>';
      const testName = 'Test User';

      // Act
      await tokenService.saveToken(testToken, testUserId, testEmail, testName);
      final retrievedToken = await tokenService.getToken();
      final retrievedId = await tokenService.getId();

      // Assert
      expect(retrievedToken, equals(testToken));
      expect(retrievedId, equals(testUserId));
    });

    test('should return null when token not found', () async {
      // Act
      final retrievedToken = await tokenService.getToken();
      final retrievedId = await tokenService.getId();

      // Assert
      expect(retrievedToken, isNull);
      expect(retrievedId, isNull);
    });

    test('should remove token successfully', () async {
      // Arrange
      const testToken = 'test_jwt_token';
      const testUserId = 1;
      const testEmail = '<EMAIL>';
      const testName = 'Test User';
      await tokenService.saveToken(testToken, testUserId, testEmail, testName);

      // Act
      await tokenService.removeToken();
      final retrievedToken = await tokenService.getToken();

      // Assert
      expect(retrievedToken, isNull);
    });

    test('should handle empty token save', () async {
      // Arrange
      const emptyToken = '';
      const testUserId = 1;
      const testEmail = '<EMAIL>';
      const testName = 'Test User';

      // Act
      await tokenService.saveToken(emptyToken, testUserId, testEmail, testName);
      final retrievedToken = await tokenService.getToken();

      // Assert
      expect(retrievedToken, equals(emptyToken));
    });

    test('should update existing token', () async {
      // Arrange
      const initialToken = 'initial_token';
      const updatedToken = 'updated_token';
      const testUserId = 1;
      const testEmail = '<EMAIL>';
      const testName = 'Test User';

      // Act
      await tokenService.saveToken(initialToken, testUserId, testEmail, testName);
      await tokenService.saveToken(updatedToken, testUserId, testEmail, testName);
      final retrievedToken = await tokenService.getToken();

      // Assert
      expect(retrievedToken, equals(updatedToken));
    });
  });
}