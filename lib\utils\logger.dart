import 'dart:developer' as developer;

/// Centralized logging utility for the application
class AppLogger {
  static const String _tag = 'BeeKids';
  
  /// Log info messages
  static void info(String message, {String? tag}) {
    developer.log(
      message,
      name: tag ?? _tag,
      level: 800, // Info level
    );
  }
  
  /// Log warning messages
  static void warning(String message, {String? tag}) {
    developer.log(
      message,
      name: tag ?? _tag,
      level: 900, // Warning level
    );
  }
  
  /// Log error messages
  static void error(String message, {Object? error, StackTrace? stackTrace, String? tag}) {
    developer.log(
      message,
      name: tag ?? _tag,
      level: 1000, // Error level
      error: error,
      stackTrace: stackTrace,
    );
  }
  
  /// Log debug messages (only in debug mode)
  static void debug(String message, {String? tag}) {
    assert(() {
      developer.log(
        message,
        name: tag ?? _tag,
        level: 700, // Debug level
      );
      return true;
    }());
  }
  
  /// Log network requests
  static void network(String method, String url, {int? statusCode, String? error}) {
    final message = '$method $url${statusCode != null ? ' - $statusCode' : ''}${error != null ? ' - ERROR: $error' : ''}';
    if (error != null) {
      AppLogger.error(message, tag: 'Network');
    } else {
      AppLogger.info(message, tag: 'Network');
    }
  }
  
  /// Log service operations
  static void service(String serviceName, String operation, {String? details, Object? error}) {
    final message = '$serviceName.$operation${details != null ? ' - $details' : ''}';
    if (error != null) {
      AppLogger.error(message, error: error, tag: 'Service');
    } else {
      AppLogger.info(message, tag: 'Service');
    }
  }
}
