import 'dart:convert';
import 'package:bee_kids_mobile/view_model/classeService.dart';
// Add this import for TokenService
import 'package:bee_kids_mobile/view_model/tokenService.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:http/http.dart' as http;
import 'package:bee_kids_mobile/model/classe.dart';
import 'package:bee_kids_mobile/model/eleve.dart';

import 'eleve_service_test.dart';

// Generate mocks for the classes we want to mock
@GenerateMocks([http.Client, TokenService])
void main() {
  late MockClient mockHttpClient;
  late MockTokenService mockTokenService;
  late classeService service;

  setUp(() {
    mockHttpClient = MockClient();
    mockTokenService = MockTokenService();
    service = classeService(tokenService: mockTokenService);
  });

  group('ClasseService', () {
    test('getClassesByEducateurId returns list of Classe on success', () async {
      when(mockTokenService.getToken()).thenAnswer((_) async => 'testToken');
      when(mockTokenService.getId()).thenAnswer((_) async => 1);
      final responsePayload = jsonEncode([
        {"id": 1, "name": "Math"},
        {"id": 2, "name": "Science"}
      ]);

      when(mockHttpClient.get(
        Uri.parse('${service.baseUrl}api/classes/formateur/1'),
        headers: anyNamed('headers'),
      )).thenAnswer((_) async => http.Response(responsePayload, 200));

      final result = await service.getClassesByEducateurId();

      expect(result, isA<List<Classe>>());
      expect(result.length, 2);
    });

    test('getAllClasses returns list of Classe on success', () async {
      when(mockTokenService.getToken()).thenAnswer((_) async => 'testToken');
      final responsePayload = jsonEncode([
        {"id": 1, "name": "Math"},
      ]);

      when(mockHttpClient.get(
        Uri.parse('${service.baseUrl}api/classes'),
        headers: anyNamed('headers'),
      )).thenAnswer((_) async => http.Response(responsePayload, 200));

      final result = await service.getAllClasses();

      expect(result, isA<List<Classe>>());
      expect(result.length, 1);
    });

    test('getElevesByClasseId returns list of Eleve on success', () async {
      when(mockTokenService.getToken()).thenAnswer((_) async => 'testToken');
      final responsePayload = jsonEncode([
        {"id": 1, "name": "John"},
        {"id": 2, "name": "Jane"}
      ]);

      when(mockHttpClient.get(
        Uri.parse('${service.baseUrl}api/classes/1/eleves'),
        headers: anyNamed('headers'),
      )).thenAnswer((_) async => http.Response(responsePayload, 200));

      final result = await service.getElevesByClasseId(1);

      expect(result, isA<List<Eleve>>());
      expect(result.length, 2);
    });

    test('getClassesByEleveId returns list of Classe on success', () async {
      when(mockTokenService.getToken()).thenAnswer((_) async => 'testToken');
      final responsePayload = jsonEncode([
        {"id": 1, "name": "Math"},
      ]);

      when(mockHttpClient.get(
        Uri.parse('${service.baseUrl}api/classes/eleve/1'),
        headers: anyNamed('headers'),
      )).thenAnswer((_) async => http.Response(responsePayload, 200));

      final result = await service.getClassesByEleveId(1);

      expect(result, isA<List<Classe>>());
      expect(result.length, 1);
    });

    test('postEmploiByClasseId sends POST request successfully', () async {
      when(mockTokenService.getToken()).thenAnswer((_) async => 'testToken');

      final request = http.MultipartRequest(
        'POST',
        Uri.parse('${service.baseUrl}api/emplois/create/1'),
      );

      final response = http.StreamedResponse(Stream.value(utf8.encode('OK')), 200);

      // Assume MultipartRequest will be sent successfully
      when(mockHttpClient.send(any)).thenAnswer((_) async => response);

      await service.postEmploiByClasseId(1, 'TestName', 'testFilePath');
    });
  });
}