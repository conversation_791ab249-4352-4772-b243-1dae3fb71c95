import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:path_provider/path_provider.dart';

class PDFViewerScreen extends StatefulWidget {
  final String pdfUrl;

  const PDFViewerScreen({super.key, required this.pdfUrl});

  @override
  State<PDFViewerScreen> createState() => _PDFViewerScreenState();
}

class _PDFViewerScreenState extends State<PDFViewerScreen> {
  String? localFilePath;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _downloadAndLoadPDF();
  }

  Future<void> _downloadAndLoadPDF() async {
    try {
      final dio = Dio();
      final tempDir = await getTemporaryDirectory();
      final filePath = "${tempDir.path}/${widget.pdfUrl.split('/').last}";
      await dio.download(widget.pdfUrl, filePath);

      setState(() {
        localFilePath = filePath;
        isLoading = false;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load PDF: $e')),
      );
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('View PDF'),
        actions: [
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: () => _downloadFile(context, widget.pdfUrl),
          ),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : localFilePath != null
              ? PDFView(
                  filePath: localFilePath!,
                )
              : const Center(child: Text('Failed to load PDF')),
    );
  }

  Future<void> _downloadFile(BuildContext context, String fileUrl) async {
    try {
      final dio = Dio();
      final fileName = fileUrl.split('/').last;
      final path = '/storage/emulated/0/Download/$fileName';
      await dio.download(fileUrl, path);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Downloaded: $path')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to download: $e')),
      );
    }
  }
}
