import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

class LiveStreamPlayer extends StatefulWidget {
  final String streamUrl; // Add this

  const LiveStreamPlayer({Key? key, required this.streamUrl}) : super(key: key);

  @override
  _LiveStreamPlayerState createState() => _LiveStreamPlayerState();
}

class _LiveStreamPlayerState extends State<LiveStreamPlayer> {
  late VideoPlayerController _controller;

  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.networkUrl(
      Uri.parse(widget.streamUrl),
    )..initialize().then((_) {
        setState(() {});
        _controller.play(); // Auto-play the stream
      });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Live Stream")),
      body: Center(
        child: _controller.value.isInitialized
            ? AspectRatio(
                aspectRatio: _controller.value.aspectRatio,
                child: VideoPlayer(_controller),
              )
            : const CircularProgressIndicator(), // Show loader while loading
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
