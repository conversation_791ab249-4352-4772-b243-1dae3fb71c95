import 'dart:convert';

import 'package:bee_kids_mobile/model/classe.dart';
import 'package:bee_kids_mobile/model/eleve.dart';
import 'package:bee_kids_mobile/view/educateur/footer.dart';
import 'package:bee_kids_mobile/view_model/classeService.dart';
import 'package:bee_kids_mobile/view_model/eleveService.dart';
import 'package:flutter/material.dart';

class SuivieEnfantsEducateur extends StatefulWidget {
  const SuivieEnfantsEducateur({super.key});

  @override
  _SuivieEnfantsState createState() => _SuivieEnfantsState();
}

class _SuivieEnfantsState extends State<SuivieEnfantsEducateur> {
  final eleveService _eleveService = eleveService();
  final classeService _classeService = classeService();
  List<Classe> classes = [];
  List<Eleve> eleves = [];
  List<Eleve> filteredEleves = [];
  TextEditingController searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _fetchEleves();
    _fetchClasses();
    _filterEleves();

    searchController.addListener(() {
      _filterEleves();
    });
  }

  void _fetchClasses() async {
    try {
      final response = await _classeService.getClassesByEducateurId();
      setState(() {
        classes = response.isNotEmpty ? response : [];
      });
    } catch (e) {
      // print('Error fetching classes: $e');
    }
  }

  void _fetchEleves() async {
    try {
      final response = await _eleveService.getAllElevesByEducateurId();
      setState(() {
        eleves = response.isNotEmpty ? response : [];
        filteredEleves = eleves;
      });
    } catch (e) {
      //print('Error fetching eleves: $e');
    }
  }

  void _fetchElevesByClasseId(id) async {
    try {
      final response = await _classeService.getElevesByClasseId(id);
      setState(() {
        eleves = response.isNotEmpty ? response : [];
        filteredEleves = eleves;
      });
    } catch (e) {
      // print('Error fetching eleves by classe ID: $e');
    }
  }

  void _filterEleves() {
    setState(() {
      filteredEleves = eleves.where((eleve) {
        return eleve.nom
            .toLowerCase()
            .contains(searchController.text.toLowerCase());
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pushNamed(context, '/educateur/menu'),
        ),
        title: const Text(
          'Suivi des enfants',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.green,
        elevation: 0,
      ),
      body: Container(
        color: Colors.green,
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.all(screenWidth * 0.04),
              child: TextField(
                controller: searchController,
                decoration: InputDecoration(
                  hintText: 'Rechercher par nom',
                  prefixIcon: const Icon(Icons.search),
                  filled: true,
                  fillColor: Colors.white,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(screenWidth * 0.1),
                    borderSide: BorderSide.none,
                  ),
                ),
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.04),
              height: screenHeight * 0.06,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: classes.length,
                itemBuilder: (context, index) {
                  final classe = classes[index];
                  return Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: screenWidth * 0.02),
                    child: ElevatedButton(
                      onPressed: () async {
                        _fetchElevesByClasseId(classe.id);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: Colors.green,
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(screenWidth * 0.06),
                        ),
                      ),
                      child: Text(classe.nomClasse,
                          style: TextStyle(fontSize: screenWidth * 0.035)),
                    ),
                  );
                },
              ),
            ),
            SizedBox(height: screenHeight * 0.03),
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(screenWidth * 0.02),
                child: CenteredCardWithImages(
                  eleves: filteredEleves,
                  fetchEleves: _fetchEleves,
                  screenWidth: screenWidth,
                  screenHeight: screenHeight,
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar:
          const MyFooterEducateur(currentRoute: '/educateur/menu'),
    );
  }
}

class CenteredCardWithImages extends StatelessWidget {
  final List<Eleve> eleves;
  final VoidCallback fetchEleves;
  final double screenWidth;
  final double screenHeight;

  const CenteredCardWithImages({
    super.key,
    required this.eleves,
    required this.fetchEleves,
    required this.screenWidth,
    required this.screenHeight,
  });

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.topCenter,
      child: Card(
        elevation: 5,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(screenWidth * 0.04),
        ),
        child: Padding(
          padding: EdgeInsets.all(screenWidth * 0.03),
          child: Column(
            children: [
              Expanded(
                child: eleves.isEmpty
                    ? const Center(
                        child: Text(
                          'Choisir un groupe pour consulter la liste des eleves.',
                          style: TextStyle(fontSize: 16),
                        ),
                      )
                    : SingleChildScrollView(
                        child: GridView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            crossAxisSpacing: screenWidth * 0.03,
                            mainAxisSpacing: screenWidth * 0.03,
                          ),
                          itemCount: eleves.length,
                          itemBuilder: (context, index) {
                            final eleve = eleves[index];

                            return GestureDetector(
                              onTap: () {
                                final args = {
                                  'id': eleve.id,
                                  'dateDeNaissance': eleve.dateDeNaissance,
                                  'nom': eleve.nom,
                                  'prenom': eleve.prenom,
                                };

                                //  print('Arguments passed: $args');

                                if (args['id'] != null &&
                                    args['dateDeNaissance'] != null &&
                                    args['nom'] != null &&
                                    args['prenom'] != null) {
                                  Navigator.pushNamed(
                                    context,
                                    '/educateur/suivie_enfant',
                                    arguments: args,
                                  );
                                } else {
                                  //  print('Error: Missing or null arguments.');
                                }
                              },
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  FutureBuilder<String>(
                                    future: eleveService()
                                        .getPhotoByEleveById(eleve.id),
                                    builder: (context, snapshot) {
                                      if (snapshot.connectionState ==
                                          ConnectionState.waiting) {
                                        return SizedBox(
                                          width: screenWidth * 0.2,
                                          height: screenWidth * 0.2,
                                          child: const Center(
                                              child:
                                                  CircularProgressIndicator()),
                                        );
                                      } else if (snapshot.hasData &&
                                          snapshot.data != null) {
                                        try {
                                          final photoData =
                                              jsonDecode(snapshot.data!);
                                          final photoUrl =
                                              photoData['photoUrl'];

                                          if (photoUrl == null ||
                                              photoUrl.isEmpty) {
                                            return buildDefaultAvatar(
                                                screenWidth);
                                          }

                                          return Image.network(
                                            photoUrl,
                                            width: screenWidth * 0.2,
                                            height: screenWidth * 0.2,
                                            fit: BoxFit.cover,
                                            errorBuilder:
                                                (context, error, stackTrace) {
                                              // print('Image load error: $error');
                                              return buildDefaultAvatar(
                                                  screenWidth);
                                            },
                                            loadingBuilder: (context, child,
                                                loadingProgress) {
                                              if (loadingProgress == null)
                                                return child;
                                              return SizedBox(
                                                width: screenWidth * 0.2,
                                                height: screenWidth * 0.2,
                                                child: const Center(
                                                    child:
                                                        CircularProgressIndicator()),
                                              );
                                            },
                                          );
                                        } catch (e) {
                                          //  print('Error parsing photo data: $e');
                                          return buildDefaultAvatar(
                                              screenWidth);
                                        }
                                      }
                                      return buildDefaultAvatar(screenWidth);
                                    },
                                  ),
                                  SizedBox(height: screenHeight * 0.01),
                                  Flexible(
                                    child: Text(
                                      eleve.nom,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        fontSize: screenWidth * 0.03,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildDefaultAvatar(double screenWidth) {
    return Container(
      width: screenWidth * 0.2,
      height: screenWidth * 0.2,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        shape: BoxShape.circle,
      ),
      child: Icon(
        Icons.person,
        size: screenWidth * 0.1,
        color: Colors.grey[600],
      ),
    );
  }
}

void main() {
  runApp(const MaterialApp(
    debugShowCheckedModeBanner: false,
    home: SuivieEnfantsEducateur(),
  ));
}
