import 'package:flutter/material.dart';
import '../view_model/tokenService.dart';
import '../view/login.dart';

class AuthGuard {
  static final TokenService _tokenService = TokenService();

  static Future<bool> isAuthenticated() async {
    final token = await _tokenService.getToken();
    return token != null;
  }

  static Route<dynamic> guardRoute(Route<dynamic> route) {
    return MaterialPageRoute(
      builder: (context) => FutureBuilder<bool>(
        future: isAuthenticated(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Scaffold(
              body: Center(child: CircularProgressIndicator()),
            );
          }
          
          if (snapshot.data == true) {
            // Cast route to MaterialPageRoute to access its builder
            return (route as MaterialPageRoute).builder(context);
          }
          
          return const LoginScreen();
        },
      ),
    );
  }
}