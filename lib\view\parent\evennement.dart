import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:bee_kids_mobile/model/event.dart';
import 'package:bee_kids_mobile/view/parent/footer.dart';
import 'package:bee_kids_mobile/view_model/evennementService.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:intl/intl.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:flutter/rendering.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Evennements',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: CalendarAppParent(),
    );
  }
}

class CalendarAppParent extends StatefulWidget {
  final String? selectedDate;

  CalendarAppParent({this.selectedDate});

  @override
  _CalendarScreenState createState() => _CalendarScreenState();
}

class _CalendarScreenState extends State<CalendarAppParent> {
  final EventService _eventService = EventService();
  late Future<List<Event>> _eventsFuture;
  late DateTime _selectedDay;
  late DateTime _focusedDay;
  TextEditingController _dateController = TextEditingController();
  ScrollController _scrollController = ScrollController();
  bool _isCalendarExpanded = true;

  Set<DateTime> _eventDays = {}; // Store the days with events
  Map<DateTime, List<Event>> _allEvents =
      {}; // Store events for all dates persistently

  @override
  void initState() {
    super.initState();
    initializeDateFormatting('fr_FR', null);

    // Parse the selectedDate if provided
    if (widget.selectedDate != null && widget.selectedDate!.isNotEmpty) {
      try {
        // Try different date formats
        DateTime parsedDate;
        if (widget.selectedDate!.contains('-')) {
          // Format: YYYY-MM-DD
          parsedDate = DateFormat('yyyy-MM-dd').parse(widget.selectedDate!);
        } else if (widget.selectedDate!.contains('/')) {
          // Format: DD/MM/YYYY
          parsedDate = DateFormat('dd/MM/yyyy').parse(widget.selectedDate!);
        } else {
          parsedDate = DateTime.now();
        }

        _selectedDay = parsedDate;
        _focusedDay = parsedDate;
      } catch (e) {
        print("Error parsing date: ${widget.selectedDate} - $e");
        _selectedDay = DateTime.now();
        _focusedDay = DateTime.now();
      }
    } else {
      _selectedDay = DateTime.now();
      _focusedDay = DateTime.now();
    }

    _loadEventsForSelectedDate(_selectedDay);

    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  // Function to remove emojis and flags from text
  String _removeEmojisAndFlags(String text) {
    // Regular expression to detect emojis, flags, and special Unicode characters
    final emojiRegex = RegExp(
      r'[\u{1F600}-\u{1F64F}]|' // Emoticons
      r'[\u{1F300}-\u{1F5FF}]|' // Misc Symbols and Pictographs
      r'[\u{1F680}-\u{1F6FF}]|' // Transport and Map
      r'[\u{1F1E0}-\u{1F1FF}]|' // Regional indicator symbols (flags)
      r'[\u{2600}-\u{26FF}]|' // Misc symbols
      r'[\u{2700}-\u{27BF}]|' // Dingbats
      r'[\u{1F900}-\u{1F9FF}]|' // Supplemental Symbols and Pictographs
      r'[\u{1F018}-\u{1F270}]|' // Various symbols
      r'[\u{238C}-\u{2454}]|' // Various symbols
      r'[\u{20D0}-\u{20FF}]|' // Combining Diacritical Marks for Symbols
      r'[\u{FE00}-\u{FE0F}]', // Variation Selectors
      unicode: true,
    );

    return text.replaceAll(emojiRegex, '').trim();
  }

  // Validation function to check for emojis and flags in input
  String? _validateInput(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName ne peut pas être vide';
    }

    // Regular expression to detect emojis, flags, and special Unicode characters
    final emojiRegex = RegExp(
      r'[\u{1F600}-\u{1F64F}]|' // Emoticons
      r'[\u{1F300}-\u{1F5FF}]|' // Misc Symbols and Pictographs
      r'[\u{1F680}-\u{1F6FF}]|' // Transport and Map
      r'[\u{1F1E0}-\u{1F1FF}]|' // Regional indicator symbols (flags)
      r'[\u{2600}-\u{26FF}]|' // Misc symbols
      r'[\u{2700}-\u{27BF}]|' // Dingbats
      r'[\u{1F900}-\u{1F9FF}]|' // Supplemental Symbols and Pictographs
      r'[\u{1F018}-\u{1F270}]|' // Various symbols
      r'[\u{238C}-\u{2454}]|' // Various symbols
      r'[\u{20D0}-\u{20FF}]|' // Combining Diacritical Marks for Symbols
      r'[\u{FE00}-\u{FE0F}]', // Variation Selectors
      unicode: true,
    );

    if (emojiRegex.hasMatch(value)) {
      return '$fieldName ne peut pas contenir d\'emojis, de drapeaux ou de symboles spéciaux';
    }

    return null;
  }

  void _scrollListener() {
    if (_scrollController.position.userScrollDirection ==
        ScrollDirection.reverse) {
      if (_isCalendarExpanded) {
        setState(() {
          _isCalendarExpanded = false;
        });
      }
    } else if (_scrollController.position.userScrollDirection ==
        ScrollDirection.forward) {
      if (!_isCalendarExpanded) {
        setState(() {
          _isCalendarExpanded = true;
        });
      }
    }
  }

  void _loadEventsForSelectedDate(DateTime date) async {
    if (DateFormat('yyyy-MM-dd').format(date) ==
        DateFormat('yyyy-MM-dd').format(DateTime.now())) {
      setState(() {
        _eventsFuture = _eventService.getEventsAfterToday();
      });
    } else {
      setState(() {
        _eventsFuture = _eventService.getEvenementsByDate(date);
      });
    }

    final events = await _eventsFuture;
    setState(() {
      _allEvents[date] = events;
      _eventDays = _allEvents.values
          .expand((e) => e)
          .map((event) => DateTime.utc(
              event.dateEvent.year, event.dateEvent.month, event.dateEvent.day))
          .toSet();
    });
  }

  void _goToDate() {
    final inputDate = _dateController.text;
    try {
      DateTime parsedDate = DateFormat('yyyy-MM-dd').parseStrict(inputDate);
      setState(() {
        _focusedDay = parsedDate;
        _selectedDay = parsedDate;
      });
      _loadEventsForSelectedDate(parsedDate);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
        content: Text('Format de date invalide ! Veuillez utiliser AAAA-MM-JJ'),
        backgroundColor: Colors.orange,
        behavior: SnackBarBehavior.floating,
      ));
    }
  }

  void _goToToday() {
    setState(() {
      _focusedDay = DateTime.now();
      _selectedDay = DateTime.now();
    });
    _loadEventsForSelectedDate(DateTime.now());
  }

  Widget buildEventCard(Event event) {
    String encodedTitle =
        utf8.decode(latin1.encode(_removeEmojisAndFlags(event.titre)));
    String encodedDescription =
        utf8.decode(latin1.encode(_removeEmojisAndFlags(event.description)));
    String EncodedUserName =
        utf8.decode(latin1.encode(_removeEmojisAndFlags(event.userNom!)));
    String EncodedUserPrenom =
        utf8.decode(latin1.encode(_removeEmojisAndFlags(event.userPrenom!)));
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
      child: ListTile(
        leading: event.photoUrl != null &&
                event.photoUrl!.isNotEmpty &&
                Uri.tryParse(event.photoUrl!)?.hasScheme == true
            ? Image.network(
                event.photoUrl!,
                width: 50,
                height: 50,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return const Icon(Icons.event, size: 50, color: Colors.green);
                },
              )
            : const Icon(Icons.event, size: 50, color: Colors.green),
        title: Text(
          encodedTitle,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
            '${encodedDescription}\nDate: ${DateFormat('yyyy-MM-dd').format(event.dateEvent)}\n Créé par: ${EncodedUserName} ${EncodedUserPrenom}'),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pushNamed(context, '/parent/menu'),
        ),
        title: const Text(
          'Calendrier des événements',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.green[800],
        actions: [
          IconButton(
            icon: const Icon(Icons.today),
            onPressed: _goToToday,
          ),
          IconButton(
            icon: Icon(_isCalendarExpanded
                ? Icons.keyboard_arrow_up
                : Icons.keyboard_arrow_down),
            onPressed: () {
              setState(() {
                _isCalendarExpanded = !_isCalendarExpanded;
              });
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextField(
              controller: _dateController,
              decoration: const InputDecoration(
                labelText: 'Entrez une date (AAAA-MM-JJ)',
                border: OutlineInputBorder(),
              ),
              onSubmitted: (value) => _goToDate(),
            ),
          ),
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            height: _isCalendarExpanded ? null : 0,
            child: _isCalendarExpanded
                ? TableCalendar(
                    firstDay: DateTime.utc(1000, 1, 1),
                    lastDay: DateTime.utc(9999, 1, 1),
                    focusedDay: _focusedDay,
                    selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
                    locale: 'fr_FR',
                    headerStyle: HeaderStyle(
                      formatButtonVisible: false,
                      titleCentered: true,
                    ),
                    daysOfWeekStyle: DaysOfWeekStyle(
                      weekdayStyle: TextStyle(color: Colors.black),
                      weekendStyle: TextStyle(color: Colors.red),
                    ),
                    calendarStyle: CalendarStyle(
                      weekendTextStyle: TextStyle(color: Colors.red),
                      selectedDecoration: BoxDecoration(
                        color: Colors.green,
                        shape: BoxShape.circle,
                      ),
                      todayDecoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.5),
                        shape: BoxShape.circle,
                      ),
                    ),
                    onDaySelected: (selectedDay, focusedDay) {
                      setState(() {
                        _selectedDay = selectedDay;
                        _focusedDay = focusedDay;
                      });
                      _loadEventsForSelectedDate(selectedDay);
                    },
                    calendarBuilders: CalendarBuilders(
                      markerBuilder: (context, date, events) {
                        if (_eventDays.contains(date)) {
                          return Positioned(
                            bottom: 1,
                            right: 1,
                            child: Icon(
                              Icons.circle,
                              color: Colors.green,
                              size: 8,
                            ),
                          );
                        }
                        return SizedBox.shrink();
                      },
                    ),
                  )
                : SizedBox.shrink(),
          ),
          Expanded(
            child: FutureBuilder<List<Event>>(
              future: _eventsFuture,
              builder:
                  (BuildContext context, AsyncSnapshot<List<Event>> snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                } else if (snapshot.hasError) {
                  // Vérifier si l'erreur est liée à l'absence d'événements pour la date
                  if (snapshot == 'Aucune événement trouvée pour cette date.') {
                    return const Center(
                        child:
                            Text('Aucun événement trouvé pour cette date..'));
                  } else {
                    return Center(
                        child: Text('Aucun événement trouvé pour cette date!'));
                  }
                } else {
                  return ListView.builder(
                    controller: _scrollController,
                    itemCount: snapshot.data!.length,
                    itemBuilder: (context, index) =>
                        buildEventCard(snapshot.data![index]),
                  );
                }
              },
            ),
          )
        ],
      ),
      bottomNavigationBar: const MyFooterParent(currentRoute: '/parent/menu'),
    );
  }
}
