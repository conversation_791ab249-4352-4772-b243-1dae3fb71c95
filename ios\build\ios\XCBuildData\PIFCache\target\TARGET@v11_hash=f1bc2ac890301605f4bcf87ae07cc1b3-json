{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983e115f7ad1de2d401dcf77f2ca1e789f", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e983d22020c1dcfa1bb24a0448b6555c274", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981321d48267162fad09ff269a23db57de", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98d7057cffc75641385299c6e96c2b9dc1", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981321d48267162fad09ff269a23db57de", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e989534e9e0c5d31313e20233ece3d7e8cb", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e982fdf0cf647d141de8670b2bd5fea6173", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e981e4c25cd6ade14cc0548c27d696d21c2", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f266d8fd76a3050d8050436962858a73", "guid": "bfdfe7dc352907fc980b868725387e98aed75d68af1547af66ef9db4b20502c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832f38689f079a548f224947cdee50904", "guid": "bfdfe7dc352907fc980b868725387e9829275f1dc7b1fbdf2fea5c2f9bbd3386"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e42b9ec84941cd50c09b5dfdc57c2143", "guid": "bfdfe7dc352907fc980b868725387e980a2c83c38655cfb616c9a16b257a6b69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989480d0ea6206d455aebe3bb538dea0fb", "guid": "bfdfe7dc352907fc980b868725387e9812b9761073f0d5975f858975e27753a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982843f6e375c69b654053580a17bc5e4d", "guid": "bfdfe7dc352907fc980b868725387e9877cc607d2bd602493499bb8c7f148b1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2d89731509bd47abe7d5c666f157124", "guid": "bfdfe7dc352907fc980b868725387e9849287bbf574affd2f507022a7637e1e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3b1ef201a1faffcc49e2102137a58ac", "guid": "bfdfe7dc352907fc980b868725387e987407e8466c0efb711bf4eca07522fe99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd101c9b631cc943c1adb9143f40938d", "guid": "bfdfe7dc352907fc980b868725387e988c3fd8251ce034db4982fd2a3189ce01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e867982dfd40dbb0e23f29b48dab1fa", "guid": "bfdfe7dc352907fc980b868725387e988f4e1405f3852a0845153bf8a1fdf0cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee58f8d549baca835c6432c33b9ce488", "guid": "bfdfe7dc352907fc980b868725387e98ba92f8d84f32f39f9f79b422445cc28c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98704c975beb426923d83a06b957ad5d9d", "guid": "bfdfe7dc352907fc980b868725387e98059d32e62a06aed0e750c3ad16d9538a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a950dbf3ad0229ca3a3e7fed2e4956d", "guid": "bfdfe7dc352907fc980b868725387e9869273c50e5e5618292fec822f3e9826e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98420a4fd2d0fa93d1083be935affe142b", "guid": "bfdfe7dc352907fc980b868725387e9892914a4292b6abbc869cc3368450b2b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827d80a37aa4521d50d1b14d49150121a", "guid": "bfdfe7dc352907fc980b868725387e98954e1c82fef00d563e2a30d8d1f7ff42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d76c1a47f43439c100b62c4da5ff632", "guid": "bfdfe7dc352907fc980b868725387e984e1a7d3aca0bcd7328dc05b01ca0561c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b5a7e5522e08171681f61c18fb80bdb", "guid": "bfdfe7dc352907fc980b868725387e989d8af503565605f8f9878fc2a324b1df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c57b7c093dbb55183832d6e18c01ee8b", "guid": "bfdfe7dc352907fc980b868725387e986c84a5dd92b24e577e7cde256a8693df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98123cebeec6ebcee806d68b442860a060", "guid": "bfdfe7dc352907fc980b868725387e987330255f9e89dd4cde080a125cb88280"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9af219fabdde0830bbba5944eb8f5ae", "guid": "bfdfe7dc352907fc980b868725387e986e8b37fbb2a35dff9fc7a060bc99cd56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf2c79c0cd663a02b8c18d44c1d7f02b", "guid": "bfdfe7dc352907fc980b868725387e98e9429feafbe9799f8089a689f0dc1da3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c00907b64c7167ec309f76f26598300b", "guid": "bfdfe7dc352907fc980b868725387e988609c8eb4d8bbcc4869181928bad7004"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c175ea621524a8b31fbc7c0b3a01ae7a", "guid": "bfdfe7dc352907fc980b868725387e9872ec46be75a4b449f4f930d419cb4130"}], "guid": "bfdfe7dc352907fc980b868725387e986ec6e29b8772cb57b76c4af21cf1ea0e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}