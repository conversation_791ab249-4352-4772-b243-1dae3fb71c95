import 'package:flutter_test/flutter_test.dart';
import 'package:intl/intl.dart';
import 'package:mocktail/mocktail.dart';
import '../../lib/view_model/suivieService.dart';  // Import your service
import '../../lib/view_model/tokenService.dart';  // Import your token service
import '../../lib/model/suivie.dart';  // Ensure correct Suivis import

// Mock classes
class MockTokenService extends Mock implements TokenService {}
class MockSuivieService extends Mock implements SuivieService {}

void main() {
  late MockTokenService mockTokenService;
  late MockSuivieService mockSuivieService;

  setUp(() {
    mockTokenService = MockTokenService();
    mockSuivieService = MockSuivieService();

    // Ensure mockTokenService returns a valid token
    when(() => mockTokenService.getToken()).thenAnswer((_) async => 'mockToken');
    
    // Setup other mock behaviors for SuivieService as necessary
    // E.g., mocking responses for API calls
    when(() => mockSuivieService.getSuivisByEleveIdAndDate(any(), any()))
        .thenAnswer((_) async => []);
  });

  test('SuivieService getSuivisByEleveIdAndDate returns Suivis list when status code is 200', () async {
    // Your test setup here
    final result = await mockSuivieService.getSuivisByEleveIdAndDate(10, '2024-12-16');
    
    // Ensure correct results
    expect(result, isNotNull);  // Expect result to be non-null
    expect(result, isEmpty);  // Expect an empty list for now (adjust based on mock response)
  });

  test('SuivieService getSuivisByEleveIdAndDate returns empty list when status code is not 200', () async {
    // Test when status code is not 200 (set different mock behavior if needed)
    when(() => mockSuivieService.getSuivisByEleveIdAndDate(any(), any()))
        .thenAnswer((_) async => []);

    final result = await mockSuivieService.getSuivisByEleveIdAndDate(10, '2024-12-16');
    
    expect(result, isEmpty);  // Expect an empty list for now
  });

  test('SuivieService postSuivis returns true when status code is 201', () async {
    // Create a Suivis object instead of using a map
    final suivis = Suivis( id: 15, 
    jour: DateFormat('dd/MM/yyyy').parse('13/12/2024') ,
    repas: 'faible', 
    sommeil: 'faible', 
    participation: 'faible', 
    jeuxExterieur: 'non', 
    interaction: 'faible', 
    humeur: 'faible', 
    note: 'bien');  
    // Mock response for postSuivis
    when(() => mockSuivieService.postSuivis(suivis))
        .thenAnswer((_) async => true);

    final result = await mockSuivieService.postSuivis(suivis);

    expect(result, isTrue);  // Expect true if status code is 201
  });

  test('SuivieService postSuivis returns false when status code is not 201', () async {
    // Create a Suivis object
    final suivis = Suivis( id: 15, 
    jour: DateFormat('dd/MM/yyyy').parse('13/12/2024') ,
    repas: 'faible', 
    sommeil: 'faible', 
    participation: 'faible', 
    jeuxExterieur: 'non', 
    interaction: 'faible', 
    humeur: 'faible', 
    note: 'bien'); 
    // Mock response for postSuivis with non-201 status code
    when(() => mockSuivieService.postSuivis(suivis))
        .thenAnswer((_) async => false);

    final result = await mockSuivieService.postSuivis(suivis);

    expect(result, isFalse);  // Expect false for other status codes
  });
}
