import 'package:bee_kids_mobile/view/parent/ActivitesEtProgramme.dart';
import 'package:bee_kids_mobile/view/parent/EmploiEnfant.dart';
import 'package:bee_kids_mobile/view/parent/Messagerie/Discussions.dart';
import 'package:bee_kids_mobile/view/parent/NotificationParent.dart';
import 'package:bee_kids_mobile/view/parent/cantineUI.dart';
import 'package:bee_kids_mobile/view/parent/evennement.dart';
import 'package:bee_kids_mobile/view/parent/menu.dart';
import 'package:bee_kids_mobile/view/parent/profile.dart';
import 'package:bee_kids_mobile/view/parent/suivie_enfant.dart';
import 'package:bee_kids_mobile/view/parent/suivie_enfants.dart';
import 'package:flutter/material.dart';
import 'package:bee_kids_mobile/view/parent/AccueilParent.dart';

class ParentRoutes {
  static Route<dynamic>? onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case '/parent':
      // Check if arguments contain a postId
      if (settings.arguments != null) {
        final args = settings.arguments as Map<String, dynamic>?;
        final postId = args?['postId'] as int?;
        return MaterialPageRoute(builder: (_) => ParentHome(postId: postId));
      }
        return MaterialPageRoute(
            builder: (_) => const ParentHome()); // Main screen for parent
      case '/parent/accueil':
      // Check if arguments contain a postId
      if (settings.arguments != null) {
        final args = settings.arguments as Map<String, dynamic>?;
        final postId = args?['postId'] as int?;
        return MaterialPageRoute(builder: (_) => ParentHome(postId: postId));
      }
        return MaterialPageRoute(builder: (_) => const ParentHome());

        
      case '/parent/profile':
        return MaterialPageRoute(builder: (_) => UserProfilePageParent());
      case '/parent/menu':
        return MaterialPageRoute(builder: (_) => const MenuScreenParent());
      case '/parent/cantine':
        return MaterialPageRoute(builder: (_) => const CantineScreenParent());
        case '/parent/Discussions':
        return MaterialPageRoute(builder: (_) => DiscussionsScreenParent());
        case '/parent/Evennements':
        final args = settings.arguments as Map<String, dynamic>?;
        final selectedDate = args != null ? args['selectedDate'] as String? : null;
        return MaterialPageRoute(builder: (_) => CalendarAppParent(selectedDate: selectedDate));
         case '/parent/Notifications':
        return MaterialPageRoute(builder: (_) => const NotificationParent());
        case '/parent/suivie_enfants':
        return MaterialPageRoute(builder: (_) => const SuivieEnfantsParent());
      case '/parent/suivie_enfant':
        final args = settings.arguments;
        if (args is Map<String, dynamic>) {
          return MaterialPageRoute(
            builder: (_) => SuivieEnfantParent(
              eleveId: args['id'] as int,
              dateDeNaissance: args['dateDeNaissance'] as String,
              nom: args['nom'] as String,
              prenom: args['prenom'] as String,
                  
            ),
          );
        } else {
          print('Invalid or missing arguments: $args');
          return MaterialPageRoute(
            builder: (_) => Scaffold(
              body: Center(
                child: Text(
                  'Invalid arguments for route: ${settings.name}',
                  style: const TextStyle(fontSize: 18, color: Colors.red),
                ),
              ),
            ),
          );
        }
         case '/parent/ActivitesEtProgramme':
        return MaterialPageRoute(builder: (_) => ActivitesEtProgrammeParent());
        case '/parent/EmploiEnfant':
         final args = settings.arguments;
        if (args is Map<String, dynamic>) {
          return MaterialPageRoute(
            builder: (_) => EmploiEnfantParent(
              eleveId: args['id'] as int,
              nom: args['nom'] as String,
              prenom: args['prenom'] as String,
                   classeId: args['classeId'] as int,
              
            ),
          );
        } else {
          print('Invalid or missing arguments: $args');
          return MaterialPageRoute(
            builder: (_) => Scaffold(
              body: Center(
                child: Text(
                  'Invalid arguments for route: ${settings.name}',
                  style: const TextStyle(fontSize: 18, color: Colors.red),
                ),
              ),
            ),
          );
        }

      default:
        return MaterialPageRoute(
          builder: (_) => Scaffold(
            body: Center(
              child: Text('Route non trouvée: ${settings.name}'),
            ),
          ),
        );
    }
  }
}
