import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:bee_kids_mobile/model/user.dart';
import 'package:bee_kids_mobile/view/directrice/Messagerie/Conversation.dart';
import 'package:bee_kids_mobile/view/educateur/Messagerie/Conversation.dart';
import 'package:bee_kids_mobile/view/parent/Messagerie/Conversation.dart';
import 'package:bee_kids_mobile/view_model/eleveService.dart';
import 'package:bee_kids_mobile/view_model/notificationService.dart';
import 'package:bee_kids_mobile/view_model/tokenService.dart';
import 'package:bee_kids_mobile/view_model/userService.dart';
import 'dart:convert';
import 'package:overlay_support/overlay_support.dart';
import 'package:bee_kids_mobile/model/Notification.dart' as app_notif;
import 'package:bee_kids_mobile/main.dart';

class Notification_Handler {
  static final Notification_Handler _instance =
      Notification_Handler._internal();
  final eleveService _eleveService = eleveService();
  final NotificationService _notificationService = NotificationService();
  final Set<String> _processedNotificationIds = <String>{};
  final int _notificationCacheTime = 5;

//  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  factory Notification_Handler() {
    return _instance;
  }

  Notification_Handler._internal();
  Future<void> initializeNotifications() async {}
  Future<void> handleWebSocketMessage(String payload) async {
    final data = json.decode(payload);
    final isAppForeground =
        WidgetsBinding.instance.lifecycleState == AppLifecycleState.resumed;

    // Early validation
    int senderId = data['sender'] ?? 0;
    if (senderId == 0 || senderId == TokenService().getId()) {
      return;
    }

    User user = await UserService().getUsersByUserId(senderId);
    String photoUrl = await UserService().fetchPhotoUsersByUserId(senderId);

    // Use only one notification type based on app state
  }

  @pragma('vm:entry-point')
  Future<void> handleBackgroundMessage(Map<String, dynamic> message) async {
    try {
      // Generate a unique ID for this notification
      String notificationUniqueId = _generateNotificationId(message);

      // Check if we've already processed this notification recently
      if (_processedNotificationIds.contains(notificationUniqueId)) {
        debugPrint(
            'Duplicate notification detected, skipping: $notificationUniqueId');
        return;
      }

      // Add to processed set and schedule removal after cache time
      _processedNotificationIds.add(notificationUniqueId);
      Future.delayed(Duration(seconds: _notificationCacheTime), () {
        _processedNotificationIds.remove(notificationUniqueId);
      });

      // Safely check if message is not null and has required fields
      if (message.isEmpty) {
        debugPrint('Empty message data received in background handler');
        return;
      }

      // Create a notification with the available data
      String title = 'BeeKids Notification';
      String body = 'You have a new notification';

      // Try to extract content from the message
      if (message.containsKey('content') && message['content'] != null) {
        body = message['content'].toString();
      }

      // Determine notification type and title
      String notificationType = 'general';
      if (message.containsKey('isEvent') && message['isEvent'] == true) {
        notificationType = 'evenement';
        title = 'Nouvel événement';
      } else if (message.containsKey('isSuivie') &&
          message['isSuivie'] == true) {
        notificationType = 'suivi';
        title = 'Nouveau suivi';
      } else if (message.containsKey('isNewPub') &&
          message['isNewPub'] == true) {
        notificationType = 'publication_acceptee';
        title = 'Publication acceptée';
      } else if (message.containsKey('isMenu') && message['isMenu'] == true) {
        notificationType = 'menu';
        title = 'Nouveau menu';
      } else if ((message.containsKey('is1to1Message') &&
              message['is1to1Message'] == true) ||
          (message.containsKey('isGroupMessage') &&
              message['isGroupMessage'] == true)) {
        notificationType = 'message';
        title = 'Nouveau message';

        // Try to get sender name
        if (message.containsKey('senderName') &&
            message['senderName'] != null) {
          title = 'Nouveau message de ${message['senderName']}';
        }
      }

      debugPrint(
          'Creating notification: Type=$notificationType, Title=$title, Body=$body');
    } catch (e) {
      debugPrint('Error handling background message: $e');
    }
  }

  Future<void> handleWebSocketNotification(String payload) async {
    final data = json.decode(payload);
    final isAppActive =
        WidgetsBinding.instance.lifecycleState == AppLifecycleState.resumed;

    // Generate a unique ID for this notification
    String notificationUniqueId = _generateNotificationId(data);

    // Check if we've already processed this notification recently
    if (_processedNotificationIds.contains(notificationUniqueId)) {
      debugPrint(
          'Duplicate notification detected, skipping: $notificationUniqueId');
      return;
    }

    // Add to processed set and schedule removal after cache time
    _processedNotificationIds.add(notificationUniqueId);
    Future.delayed(Duration(seconds: _notificationCacheTime), () {
      _processedNotificationIds.remove(notificationUniqueId);
    });

    // Early return if content is null or empty
    if (!data.containsKey('content') ||
        data['content'] == null ||
        data['content'].toString().isEmpty) {
      debugPrint(
          'Notification content is null or empty, skipping notification');
      return;
    }

    String content = data['content'] ?? '';
    int userId = int.tryParse(data['userId'].toString()) ?? 0;
    if (userId == 0) {
      debugPrint('Invalid user ID received');
      return;
    }

    app_notif.NotificationModel notification =
        app_notif.NotificationModel.fromJson(data);
    print('Notification data from data model: $notification');

    // Determine notification type
    String notificationType = 'general';
    if (notification.content.contains('Vous avez')) {
      notificationType = 'evenement';
    } else if (notification.isSuivie) {
      notificationType = 'suivi';
    } else if (notification.content.contains('une nouvelle publication')) {
      notificationType = 'publication_acceptee';
    } else if (notification.isMenu) {
      notificationType = 'menu';
    } else if (notification.is1to1Message || notification.isGroupMessage) {
      notificationType = 'message';
    }

    User user = await UserService().getUsersByUserId(userId);
    String photoUrl = await UserService().fetchPhotoUsersByUserId(userId);

    // IMPORTANT: Instead of creating a new map, use the original data and just add what's missing
    Map<String, dynamic> navigationData = Map<String, dynamic>.from(data);

    // Add any additional fields needed for navigation
    navigationData['title'] = user.nom;
    navigationData['photoUrl'] = photoUrl;

    if (isAppActive) {
      showOverlayNotification(
        (context) {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: _getNotificationColors(notificationType),
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ListTile(
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  leading: CircleAvatar(
                    backgroundColor: Colors.white,
                    backgroundImage:
                        photoUrl.isNotEmpty ? NetworkImage(photoUrl) : null,
                    child: photoUrl.isEmpty
                        ? Icon(_getNotificationIcon(notificationType),
                            color: Colors.green[800])
                        : null,
                  ),
                  title: Text(
                    _getNotificationTitle(notificationType, user.nom),
                    style: const TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  subtitle: Text(
                    content,
                    style: const TextStyle(
                      color: Colors.black,
                      fontSize: 14,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  trailing: IconButton(
                    icon: const Icon(Icons.close, color: Colors.white70),
                    onPressed: () => OverlaySupportEntry.of(context)?.dismiss(),
                  ),
                  onTap: () {
                    OverlaySupportEntry.of(context)?.dismiss();

                    // Pass the complete data to navigateToDestination
                    navigateToDestination(notificationType, data);
                  },
                ),
              ),
            ),
          );
        },
        duration: const Duration(seconds: 4),
        position: NotificationPosition.top,
      );
    }
    /* else {


        // For background notifications
        await AwesomeNotifications().createNotification(
          content: NotificationContent(
            id: DateTime.now().millisecondsSinceEpoch.remainder(100000),
            channelKey: 'basic_channel',
            title: _getNotificationTitle(notificationType, user.nom),
            body: content,
            notificationLayout: NotificationLayout.Default,
            category: _getNotificationCategory(notificationType),
            wakeUpScreen: true,
            fullScreenIntent: true,
            criticalAlert: true,
            largeIcon: photoUrl,
            payload: {
              'notificationType': notificationType,
              'data': json.encode(navigationData),  
            },
          ),
          actionButtons: _getNotificationActions(notificationType),
        );
      } */
  }

  void navigateToDestination(
      String notificationType, Map<String, dynamic> data) async {
    String? role = await TokenService().getRole();

    // Try to create a notification model from the data
    app_notif.NotificationModel? notification;
    try {
      notification = app_notif.NotificationModel.fromJson(data);
      if (notification.notificationId != null) {
        _notificationService.markAsRead(notification.notificationId!);
      }
    } catch (e) {
      debugPrint('Error creating notification model: $e');
      // Continue without the notification model
    }

    debugPrint('Navigating to destination: $notificationType with role: $role');

    if (role == null) {
      debugPrint('Role not found, cannot navigate');
      return;
    }

    // Use a delayed approach to wait for the app to initialize
    Future.delayed(Duration(milliseconds: 300), () async {
      try {
        final context = navigatorKey.currentContext;
        if (context == null) {
          debugPrint('No valid context available, cannot navigate');
          return;
        }

        // Now proceed with navigation using the context
        switch (notificationType) {
          case 'evenement':
            _navigateToEvent(context, role, data);
            break;

          case 'suivi':
            _navigateToSuivi(context, role, data, notification);
            break;

          case 'publication':
            _navigateToPublication(context, role);
            break;

          case 'publication_acceptee':
            _navigateToPublicationAcceptee(context, role, data, notification);
            break;

          case 'menu':
            _navigateToMenu(context, role);
            break;

          case 'message':
            debugPrint(
                'Navigating to destination: $notificationType with role: $role data: $data');
            _navigateToMessage(context, role, data);
            break;

          default:
            _navigateToHome(context, role);
            break;
        }
      } catch (e) {
        debugPrint('Error during navigation: $e');
      }
    });
  }

  // Helper methods for each navigation type
  void _navigateToEvent(
      BuildContext context, String role, Map<String, dynamic> data) {
    String route;
    if (role == 'Parent') {
      route = '/parent/Evennements';
    } else if (role == 'Directeur') {
      route = '/directrice/Evennements';
    } else {
      route = '/educateur/Evennements';
    }

    Navigator.of(context).pushNamed(
      route,
      arguments: {
        'selectedDate': data['dateEvent'],
      },
    );
  }

  Future<void> _navigateToSuivi(
      BuildContext context,
      String role,
      Map<String, dynamic> data,
      app_notif.NotificationModel? notification) async {
    int? eleveId = notification?.eleveId;

    if (eleveId == null && data.containsKey('eleveId')) {
      // Try to parse eleveId from data if not available in notification model
      eleveId = _parseIntSafely(data['eleveId']);
    }

    if (eleveId != null) {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        },
      );

      try {
        // Fetch student details
        final student = await _eleveService.getEleveById(eleveId);

        // Close loading indicator
        Navigator.pop(context);

        // Navigate to suivie_enfant with the required arguments
        String route;
        if (role == 'Parent') {
          route = '/parent/suivie_enfant';
        } else if (role == 'Directeur') {
          route = '/directrice/suivie_enfant';
        } else {
          route = '/educateur/suivie_enfant';
        }

        Navigator.pushNamed(
          context,
          route,
          arguments: {
            'id': student.id,
            'dateDeNaissance': student.dateDeNaissance,
            'nom': student.nom,
            'prenom': student.prenom,
          },
        );
      } catch (e) {
        // Close loading indicator if there was an error
        if (Navigator.canPop(context)) {
          Navigator.pop(context);
        }

        debugPrint('Error fetching student details: $e');

        // Navigate to the general suivie_enfants page as fallback
        _navigateToSuiviList(context, role);
      }
    } else {
      // No specific eleveId, navigate to the list
      _navigateToSuiviList(context, role);
    }
  }

  void _navigateToSuiviList(BuildContext context, String role) {
    String route;
    if (role == 'Parent') {
      route = '/parent/suivie_enfants';
    } else if (role == 'Directeur') {
      route = '/directrice/suivie_enfants';
    } else {
      route = '/educateur/suivie_enfants';
    }

    Navigator.of(context).pushNamed(route);
  }

  void _navigateToPublication(BuildContext context, String role) {
    if (role == 'Directeur') {
      Navigator.of(context).pushNamed('/directrice/enAttente');
    }
    // Other roles don't have access to this feature
  }

  void _navigateToPublicationAcceptee(BuildContext context, String role,
      Map<String, dynamic> data, app_notif.NotificationModel? notification) {
    int? pubId = notification?.pubId;

    if (pubId == null && data.containsKey('pubId')) {
      // Try to parse pubId from data if not available in notification model
      pubId = _parseIntSafely(data['pubId']);
    }

    String route;
    if (role == 'Parent') {
      route = '/parent/accueil';
    } else if (role == 'Directeur') {
      route = '/directrice/acceuil';
    } else {
      route = '/educateur/accueil';
    }

    Navigator.of(context).pushNamed(
      route,
      arguments: {
        'postId': pubId,
      },
    );
  }

  void _navigateToMenu(BuildContext context, String role) {
    String route;
    if (role == 'Parent') {
      route = '/parent/cantine';
    } else if (role == 'Directeur') {
      route = '/directrice/cantine';
    } else {
      route = '/educateur/cantine';
    }

    Navigator.of(context).pushNamed(route);
  }

  Future<void> _navigateToMessage(
      BuildContext context, String role, Map<String, dynamic> data) async {
    // Check all possible locations for conversationId
    String? conversationId = data['conversationId']?.toString();
    print('conversationId: $conversationId');

    // Make sure all IDs are properly converted to the expected types
    String senderId = data['userId']?.toString() ?? '';

    // Fetch user details
    User sender =
        await UserService().getUsersByUserId(_parseIntSafely(senderId) ?? 0);
    String userName = sender.prenom + ' ' + sender.nom;

    // Make sure receiverId is also a string
    String receiverId = data['receiverId']?.toString() ?? '';

    /*   print('\\\\\\\\\_navigateToMessage////////');
    print('senderId: $senderId TYPE ${senderId.runtimeType}');
    print('receiverId: $receiverId TYPE ${receiverId.runtimeType}');
    print('conversationId: $conversationId TYPE ${conversationId.runtimeType}');
    print('userName: $userName TYPE ${userName.runtimeType}');
    print('role: $role TYPE ${role.runtimeType}');
 */

    if (senderId == '0') {
      _navigateToDiscussions(context, role);
      return;
    }

    try {
      // Get current user ID from TokenService
      int? currentUserId = await TokenService().getId();
      if (currentUserId == null) {
        _navigateToDiscussions(context, role);
        return;
      }

      // Get conversation details from notification data
      String photoUrl = data['senderPhoto']?.toString() ?? '';

      try {
        // Ensure all IDs are properly converted to the expected types for each screen
        if (role == 'Parent') {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => ConversationScreenparent(
                currentUserId: currentUserId.toString(),
                conversationId: conversationId ?? '',
                recipientUserId: senderId,
                recipientUserName: userName,
                recipientPhotoUrl: photoUrl,
              ),
            ),
          );
        } else if (role == 'Formateur') {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => ConversationScreenEducateur(
                currentUserId: currentUserId.toString(),
                conversationId: conversationId ?? '',
                recipientUserId: senderId,
                recipientUserName: userName,
                recipientPhotoUrl: photoUrl,
              ),
            ),
          );
        } else if (role == 'Directeur') {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => ConversationScreen(
                currentUserId: currentUserId.toString(),
                conversationId: conversationId ?? '',
                recipientUserId: senderId,
                recipientUserName: userName,
                recipientPhotoUrl: photoUrl,
              ),
            ),
          );
        }
      } catch (e) {
        debugPrint('Error getting user: $e');
        _navigateToDiscussions(context, role);
      }
    } catch (e) {
      debugPrint('Error in navigateToMessage: $e');
      _navigateToDiscussions(context, role);
    }
  }

  void _navigateToDiscussions(BuildContext context, String role) {
    String route;
    if (role == 'Parent') {
      route = '/parent/Discussions';
    } else if (role == 'Directeur') {
      route = '/directrice/Discussions';
    } else {
      route = '/educateur/Discussions';
    }

    Navigator.of(context).pushNamed(route);
  }

  void _navigateToHome(BuildContext context, String role) {
    String route;
    if (role == 'Parent') {
      route = '/parent/accueil';
    } else if (role == 'Directeur') {
      route = '/directrice/acceuil';
    } else {
      route = '/educateur/accueil';
    }

    Navigator.of(context).pushNamed(route);
  }

  // Helper method to safely parse integers
  static int? _parseIntSafely(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) {
      return int.tryParse(value);
    }
    return null;
  }

  List<Color> _getNotificationColors(String type) {
    switch (type) {
      case 'evenement':
        return [Colors.white, Colors.orange.shade200];
      case 'suivi':
        return [Colors.white, Colors.blue.shade200];
      case 'publication':
        return [Colors.white, Colors.green.shade200];
      case 'publication_acceptee':
        return [Colors.white, Colors.purple.shade200];
      default:
        return [Colors.white, const Color.fromARGB(255, 165, 241, 144)];
    }
  }

  IconData _getNotificationIcon(String type) {
    switch (type) {
      case 'message':
        return Icons.message;
      case 'evenement':
        return Icons.event;
      case 'suivi':
        return Icons.track_changes;
      case 'publication':
        return Icons.post_add;
      case 'publication_acceptee':
        return Icons.check_circle;
      default:
        return Icons.notifications;
    }
  }

  String _getNotificationTitle(String type, String userName) {
    switch (type) {
      case 'message':
        return 'Nouveau message de $userName';
      case 'evenement':
        return 'Nouvel événement de $userName';
      case 'suivi':
        return 'Nouveau suivi de $userName';
      case 'publication':
        return 'Nouvelle publication de $userName';
      case 'publication_acceptee':
        return 'Publication acceptée de $userName';
      default:
        return 'Nouvelle notification de $userName';
    }
  }

  void showInAppNotification(Map<String, dynamic> data) {
    // You can use any in-app notification package here
    // Example using overlay_support
    showSimpleNotification(
      Text(data['title'] ?? 'New Notification'),
      subtitle: Text(data['message'] ?? ''),
      background: Colors.blue,
      duration: const Duration(seconds: 3),
    );
  }

  /*   Future<void> showChatNotification({
    required String senderName,
    required String message,
    String? avatarUrl,
  }) async {
    await AwesomeNotifications().createNotification(
      content: NotificationContent(
        id: DateTime.now().millisecondsSinceEpoch.remainder(100000),
        channelKey: 'chat_channel',
        title: senderName,
        body: message,
        notificationLayout: NotificationLayout.Messaging,
        largeIcon: avatarUrl,
      )
    );
  }  */
}

String _generateNotificationId(Map<String, dynamic> data) {
  // Use notification ID if available
  if (data.containsKey('notificationId') && data['notificationId'] != null) {
    return data['notificationId'].toString();
  }

  // For messages, use conversation ID + timestamp if available
  if ((data.containsKey('is1to1Message') && data['is1to1Message'] == true) ||
      (data.containsKey('isGroupMessage') && data['isGroupMessage'] == true)) {
    String conversationId = data['conversationId']?.toString() ?? '';
    String messageId = data['messageId']?.toString() ?? '';
    if (conversationId.isNotEmpty && messageId.isNotEmpty) {
      return 'msg_${conversationId}_${messageId}';
    }
    if (conversationId.isNotEmpty) {
      return 'conv_${conversationId}_${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  // For other notifications, create a hash from content + timestamp
  String content = data['content']?.toString() ?? '';
  String userId = data['userId']?.toString() ?? '';
  return 'notif_${userId}_${content.hashCode}_${DateTime.now().millisecondsSinceEpoch}';
}
