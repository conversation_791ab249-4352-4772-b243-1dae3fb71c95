import 'dart:convert';
import 'lib/view_model/suivieService.dart';

void main() {
  // Exemple de réponse de l'API basé sur l'image fournie
  String jsonResponse = '''
  [
    {
      "nom": "test",
      "prenom": "test",
      "classe": "classe 3ans",
      "enseignant": "makramEd"
    },
    {
      "nom": "testtest",
      "prenom": "testtest",
      "classe": "classe 3ans",
      "enseignant": "makramEd"
    },
    {
      "nom": "azerty",
      "prenom": "azerty",
      "classe": "classe 3ans",
      "enseignant": "makramEd"
    }
  ]
  ''';

  // Test de parsing avec le nouveau modèle EleveSansSuivie
  try {
    final List<dynamic> jsonData = json.decode(jsonResponse);
    
    final List<EleveSansSuivie> elevesSansSuivieList = jsonData.map((item) {
      return EleveSansSuivie.fromMap(item);
    }).toList();

    print("✅ Test réussi ! Nombre d'élèves sans suivie parsés: ${elevesSansSuivieList.length}");
    
    // Affichage des détails de tous les élèves
    print("\n📋 Liste des élèves sans suivie:");
    for (int i = 0; i < elevesSansSuivieList.length; i++) {
      final eleve = elevesSansSuivieList[i];
      print("   ${i + 1}. ${eleve.nomComplet}");
      print("      - Classe: ${eleve.classe}");
      print("      - Enseignant: ${eleve.enseignant}");
      print("      - Nom: ${eleve.nom}");
      print("      - Prénom: ${eleve.prenom}");
      print("");
    }
    
    // Test des propriétés calculées
    print("🔍 Test des propriétés calculées:");
    if (elevesSansSuivieList.isNotEmpty) {
      final premier = elevesSansSuivieList.first;
      print("   - Nom complet du premier élève: '${premier.nomComplet}'");
      print("   - toString(): ${premier.toString()}");
    }
    
  } catch (e) {
    print("❌ Erreur lors du parsing: $e");
  }
}
