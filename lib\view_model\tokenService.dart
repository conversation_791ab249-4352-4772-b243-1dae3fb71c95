import 'dart:convert';
import 'package:bee_kids_mobile/model/user.dart';
import 'package:bee_kids_mobile/view_model/WebSocketService.dart';
import 'package:bee_kids_mobile/view_model/firebase_service.dart';
import 'package:bee_kids_mobile/view_model/userService.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:bee_kids_mobile/resources/environnement/apiUrl.dart';


class TokenService {
  static const String _tokenKey = 'jwt_token';
  static const String _idKey = 'userId';
  static const String _userNameKey = 'user_name';
  static const String _role = 'role';
final baseUrl = ApiUrl.baseUrl;
  Future<void> saveToken(
      String token, user_id, String userName, String role) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, token);
    await prefs.setInt(_idKey, user_id);
    await prefs.setString(_userNameKey, userName);
    await prefs.setString(_role, role);
    print(
        'TOKEN SAVED: Token=$token, UserID=$user_id, UserName=$userName, Role=$role');
  }

  Future<String?> getToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(_tokenKey);
      // print('Retrieved token: ${token != null ? "Token exists" : "No token found"}');
      return token;
    } catch (e) {
      print('Error retrieving token: $e');
      return null;
    }
  }

  Future<bool> hasValidToken() async {
    try {
      final token = await getToken();
      final role = await getRole();
      final id = await getId();

      print(
          'Token validation check: token=${token != null}, role=${role != null}, id=${id != null}');

      // Token is valid if all three exist
      return token != null &&
          token.isNotEmpty &&
          role != null &&
          role.isNotEmpty &&
          id != null;
    } catch (e) {
      print('Error checking token validity: $e');
      return false;
    }
  }

  Future<bool> isConnected() async {
    return await hasValidToken();
  }

  Future<void> Security() async {
    if (!await isConnected()) {
      Get.toNamed('/login');
    }
  }

  Future<String?> getUserName() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userName = prefs.getString(_userNameKey);
      print('Retrieved username: $userName');
      return userName;
    } catch (e) {
      print('Error retrieving username: $e');
      return null;
    }
  }

  Future<int?> getId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final id = prefs.getInt(_idKey);
      print('Retrieved ID: $id');
      return id;
    } catch (e) {
      print('Failed to retrieve ID: $e');
      return null;
    }
  }

  Future<String?> getRole() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final role = prefs.getString(_role);
      print('Retrieved role: $role');
      return role;
    } catch (e) {
      print('Failed to retrieve role: $e');
      return null;
    }
  }

  Future<void> removeToken() async {
  try {
    final prefs = await SharedPreferences.getInstance();
    int? id = prefs.getInt(_idKey);
    
    // Remove local data first (this is fast and reliable)
    await prefs.remove(_tokenKey);
    await prefs.remove(_idKey);
    await prefs.remove(_userNameKey);
    await prefs.remove(_role);
    await prefs.clear();
    
    print('Local token data removed successfully');
    
    // Do network operations in the background (don't await them)
    _cleanupNetworkResources(id);
    
  } catch (e) {
    print('Failed to remove local token data: $e');
    // Even if local cleanup fails, we should still try to clear what we can
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
    } catch (clearError) {
      print('Failed to clear preferences: $clearError');
    }
  }
}

// Separate method for network cleanup that runs in background
void _cleanupNetworkResources(int? id) async {
  try {
    // Disconnect WebSocket first (usually faster)
    WebSocketService().disconnect();
    
    // Then try to delete Firebase token (network operation)
    if (id != null) {
      await FirebaseService().deleteFirebaseToken(id);
      print('Firebase token deleted successfully');
    }
  } catch (e) {
    print('Failed to cleanup network resources: $e');
    // Don't throw - this shouldn't prevent logout
  }
}


  // Debug method to print all auth info
  Future<void> printAuthInfo() async {
    final token = await getToken();
    final id = await getId();
    final role = await getRole();
    final userName = await getUserName();

    print('=== AUTH INFO ===');
    print('Token exists: ${token != null && token.isNotEmpty}');
    print('User ID: $id');
    print('Role: $role');
    print('Username: $userName');
    print('=================');
  }

  Future<void> debugAuthState() async {
    final token = await getToken();
    final id = await getId();
    final role = await getRole();
    final userName = await getUserName();

    print('=== AUTH STATE DEBUG ===');
    print('Token exists: ${token != null && token.isNotEmpty}');
    print('User ID: $id');
    print('Role: $role');
    print('Username: $userName');
    print('========================');
  }
}
