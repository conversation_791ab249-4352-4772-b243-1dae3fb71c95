{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a55850f2702dc52c927f6613693f6680", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a971204ab052cfb56302344180ea64d9", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a971204ab052cfb56302344180ea64d9", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985ac2f243e774e5bfde8f845870cae328", "guid": "bfdfe7dc352907fc980b868725387e98a27a49b00be0ac981e5db9f07832563d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98147e6d38e2ea93777f93a742129731af", "guid": "bfdfe7dc352907fc980b868725387e989878394bd4334bc5b75f69e19db62bf2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98419d43f6eb2caf3d7ae52f2998815932", "guid": "bfdfe7dc352907fc980b868725387e98e1897e3d8aed17671192779f85b1c248", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d80e9d6af6111347a9bad4a1ec826a11", "guid": "bfdfe7dc352907fc980b868725387e981450c482f5c290824d437888a184ae8b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98910bb78c8f67d42a4d7d4575eb977671", "guid": "bfdfe7dc352907fc980b868725387e98e4fc9878a7a9815c87ead588b617e13f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcdb0b84a1d362512db1a40fd24d7d29", "guid": "bfdfe7dc352907fc980b868725387e98931f24651cd163bcb560cc6bf0293fcb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4820d2797998447040bd6b6e5a48a0d", "guid": "bfdfe7dc352907fc980b868725387e98d4cfb9e1cbc63fdd6505f85f9a29df9c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878e6531be0f41b2a2046b000de9e04bb", "guid": "bfdfe7dc352907fc980b868725387e98603cbbc70b8dca9ffe9ae016055178e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841ffd5c98112d852540a3fddb2e748e2", "guid": "bfdfe7dc352907fc980b868725387e98de1a9153e9195626d2e18082bddd5583", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bea1e543a1aa33394ad9fd4cb9289f0", "guid": "bfdfe7dc352907fc980b868725387e984a1d400fa247fcd1999957a8682fc831", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ad4bd7e9a6086a5b625850b6e54c784", "guid": "bfdfe7dc352907fc980b868725387e98d32709d3a2dbac2c57e7e408179d0866", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bece80c55accdef94fdf978122d6ff66", "guid": "bfdfe7dc352907fc980b868725387e9852b51035de7e7268120a09bd61060c70", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ef208534b328d1991f81516c8b1baa7", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989765a2d3f058fa45f5dd55159f3cb971", "guid": "bfdfe7dc352907fc980b868725387e9851124daa4e024f512c6435eeca4166ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c947703a5d376d44dbe7fb06366320d", "guid": "bfdfe7dc352907fc980b868725387e98d1ab4894e5f8a787a882b5c8c35da9b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4696d58bb7c6669f5d3003025d2cc86", "guid": "bfdfe7dc352907fc980b868725387e980ef321af2f9f2c834e49de13f3d55bc9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f10966968c3bb9a63211944df40e5f7e", "guid": "bfdfe7dc352907fc980b868725387e98f0d31a429c82f17b0991aeffa8fa048c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802059775384e7c1edbc008cfad096f00", "guid": "bfdfe7dc352907fc980b868725387e98a42a0c118dedfd60efdbb4d16492c7b8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980669cbf80186047fab5690ea11d3174e", "guid": "bfdfe7dc352907fc980b868725387e98b3c686ca16e5bce7ed24eabc68c27a7a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f085722913ddcf064bca8a8e5ec39ad", "guid": "bfdfe7dc352907fc980b868725387e9828accb1b5b23f919db719dddafe64428", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d52efb412faf8163e6b03760be262b9", "guid": "bfdfe7dc352907fc980b868725387e981a70531bb3ca84e549cc6ba35f299fa1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d55d5a44f9ab8952685920212d0208a5", "guid": "bfdfe7dc352907fc980b868725387e98d126da608ed212ace70c11a62a0c1637", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980471aa5413c000bc729c84f08c3279a0", "guid": "bfdfe7dc352907fc980b868725387e98482d9659cbec4dc229fbd14d4097dbb0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dff7c3659e76b222ad0b0467a0e5844f", "guid": "bfdfe7dc352907fc980b868725387e989514a07acecd425069c301ac83629a34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df1f5746791d1f8532351982839295c4", "guid": "bfdfe7dc352907fc980b868725387e98c5aeecdf68b8bd150764901c66af09a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98595c8f8ff35ebea8cb2de457b5484d3c", "guid": "bfdfe7dc352907fc980b868725387e98845832e3ddc9b9efa82804181189873f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc0083d05a7efbeb706cc99adcbe5653", "guid": "bfdfe7dc352907fc980b868725387e988422c8d41a531ab0e5feda22b2729450"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854f001634fe0f046eac105c9fb01acaa", "guid": "bfdfe7dc352907fc980b868725387e98032b5432a99c409a15c23484d253896f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e73ffb49fa4009de38161c3cf44db6fe", "guid": "bfdfe7dc352907fc980b868725387e988ec79e04b31e29a98b9058ffe01c7fe6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b93743ec06705dac8ccdb0baab9d2895", "guid": "bfdfe7dc352907fc980b868725387e980e70a7a6c573b1b7ccd3e1d5ab5d07e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf7a0cf938f48993c7f52d08fca3bbdd", "guid": "bfdfe7dc352907fc980b868725387e983e056fb9529e4b65a3f33b7e5569c272"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5f195cc180eb60b2f15926fb6b9a04c", "guid": "bfdfe7dc352907fc980b868725387e980341c01be7632895d55fd1dac2b54ca7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1efe2aa7ccd17ec9d70146f2962beb9", "guid": "bfdfe7dc352907fc980b868725387e9844b9c045f1529acb68ed027e0a48b614"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985059fb09c2118d6e7bbb8d3826b9c54b", "guid": "bfdfe7dc352907fc980b868725387e98b1b6e64bf6811487ed4c6e1384077226"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4e7f847e3a95b6d309c6d3545432cf7", "guid": "bfdfe7dc352907fc980b868725387e981f2b6f47d148085767ead35147c92b0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981070533996241463552af00d2350b659", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98896d00bfedfa94052b66bc10bb205a9f", "guid": "bfdfe7dc352907fc980b868725387e98ff4558f6e06cbb7fbd15dd015ca18854"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810a80c5c91ed2929a5c7e4c2de7adb0c", "guid": "bfdfe7dc352907fc980b868725387e983caf79b54a68f27872c153b09b7289cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98689c8d181f6d03a7f3cbe43212946cb0", "guid": "bfdfe7dc352907fc980b868725387e983550715f5dc47c6e779aeb12290a7c48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f50e6382e8528e11b64d48b5d640deb", "guid": "bfdfe7dc352907fc980b868725387e985800f051739a623bb35f7cc22cbdb0d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5b17e5f59873c746a2c6530daef73ab", "guid": "bfdfe7dc352907fc980b868725387e98f2203b5d78f4ac52bb8f2618d0202e75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9055d62d94e64cbb450790d71e01762", "guid": "bfdfe7dc352907fc980b868725387e983616275298339a71f8ce5405edcae646"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890cb1e9e62431437af05e81553a76ba7", "guid": "bfdfe7dc352907fc980b868725387e9859b2f7d951b31f73b36500eee5613569"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899f23bd1d8e37c7ca4b85958723ce827", "guid": "bfdfe7dc352907fc980b868725387e9800fc7c6d98b1d598e7f0946391c24f5b"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}