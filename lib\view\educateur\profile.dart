import 'dart:convert';
import 'dart:io';
import 'package:bee_kids_mobile/model/user.dart';
import 'package:bee_kids_mobile/routes/app_routes.dart';
import 'package:bee_kids_mobile/view/educateur/footer.dart';

import 'package:bee_kids_mobile/view_model/authService.dart';
import 'package:bee_kids_mobile/view_model/tokenService.dart';
import 'package:bee_kids_mobile/view_model/userService.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

class UserProfilePageEducateur extends StatefulWidget {
  @override
  _UserProfileTabState createState() => _UserProfileTabState();
}

class _UserProfileTabState extends State<UserProfilePageEducateur> {
  String _photoUrl = "https://via.placeholder.com/150";
  User? _user;

  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _telephonelController = TextEditingController();
  final _CinController = TextEditingController();

  final ImagePicker _picker = ImagePicker();
  bool _isLoading = true;

  // Contrôleurs pour le changement de mot de passe
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _fetchUserData();
  }

  Future<void> _fetchUserData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      User user = await UserService().fetchUsersById();
      String photoUrl = await UserService().fetchPhotoUsersId();
      setState(() {
        _user = user;
        _firstNameController.text =
            utf8.decode(user.nom?.codeUnits ?? []) ?? '';
        _lastNameController.text =
            utf8.decode(user.prenom?.codeUnits ?? []) ?? '';
        _emailController.text = user.userEmail ?? '';
        _telephonelController.text = user.userPhoneNumber ?? '';
        _CinController.text = user.cinParent?.toString() ?? '';
        _photoUrl =
            photoUrl.isNotEmpty ? photoUrl : "https://via.placeholder.com/150";
        _isLoading = false;
      });
    } catch (error) {
      print('Error fetching user data: $error');
      setState(() {
        _photoUrl = "https://via.placeholder.com/150";
        _isLoading = false;
      });
    }
  }

  void _showEditModal() {
    final formKey = GlobalKey<FormState>();

    // Créer des contrôleurs temporaires avec les valeurs actuelles
    final tempFirstNameController =
        TextEditingController(text: _firstNameController.text);
    final tempLastNameController =
        TextEditingController(text: _lastNameController.text);
    final tempTelephoneController =
        TextEditingController(text: _telephonelController.text);
    final tempCinController = TextEditingController(text: _CinController.text);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return StatefulBuilder(builder: (context, setState) {
          return Container(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(25),
                topRight: Radius.circular(25),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Form(
                key: formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // En-tête du formulaire
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "Modifier mon profil",
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.green[800],
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.close),
                            onPressed: () => Navigator.pop(context),
                          ),
                        ],
                      ),
                      const Divider(),
                      const SizedBox(height: 10),

                      // Champ Nom
                      _buildFormField(
                        controller: tempFirstNameController,
                        label: "Nom",
                        icon: Icons.person,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Le nom est obligatoire';
                          }
                          if (!RegExp(r'^[a-zA-ZÀ-ÿ\s]+$').hasMatch(value)) {
                            return 'Le nom doit contenir uniquement des lettres';
                          }
                          return null;
                        },
                      ),

                      // Champ Prénom
                      _buildFormField(
                        controller: tempLastNameController,
                        label: "Prénom",
                        icon: Icons.person_outline,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Le prénom est obligatoire';
                          }
                          if (!RegExp(r'^[a-zA-ZÀ-ÿ\s]+$').hasMatch(value)) {
                            return 'Le prénom doit contenir uniquement des lettres';
                          }
                          return null;
                        },
                      ),

                      // Champ Email (désactivé)
                      _buildFormField(
                        controller: _emailController,
                        label: "Email",
                        icon: Icons.email,
                        enabled: false,
                        validator: null,
                      ),

                      // Champ Téléphone
                      _buildFormField(
                        controller: tempTelephoneController,
                        label: "Téléphone",
                        icon: Icons.phone,
                        keyboardType: TextInputType.phone,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Le numéro de téléphone est obligatoire';
                          }
                          if (!RegExp(r'^\+?[0-9]+$').hasMatch(value)) {
                            return 'Format invalide (chiffres uniquement)';
                          }
                          return null;
                        },
                      ),

                      // Champ CIN
                      _buildFormField(
                        controller: tempCinController,
                        label: "CIN",
                        icon: Icons.credit_card,
                        keyboardType: TextInputType.number,
                        maxLength: 8,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Le CIN est obligatoire';
                          }
                          if (!RegExp(r'^\d+$').hasMatch(value)) {
                            return 'CIN doit contenir uniquement des chiffres';
                          }
                          if (value.length != 8) {
                            return 'CIN doit contenir exactement 8 chiffres';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 20),

                      // Boutons d'action
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () async {
                                if (formKey.currentState!.validate()) {
                                  try {
                                    // Mettre à jour les contrôleurs principaux avec les valeurs temporaires
                                    _firstNameController.text =
                                        tempFirstNameController.text;
                                    _lastNameController.text =
                                        tempLastNameController.text;
                                    _telephonelController.text =
                                        tempTelephoneController.text;
                                    _CinController.text =
                                        tempCinController.text;

                                    setState(() {
                                      if (_user != null) {
                                        _user!.nom = _firstNameController.text;
                                        _user!.prenom =
                                            _lastNameController.text;
                                        _user!.userEmail =
                                            _emailController.text;
                                        _user!.userPhoneNumber =
                                            _telephonelController.text;
                                        _user!.cinParent = _CinController.text;
                                      }
                                    });

                                    if (_user != null) {
                                      // Afficher un indicateur de chargement
                                      showDialog(
                                        context: context,
                                        barrierDismissible: false,
                                        builder: (BuildContext context) {
                                          return const Center(
                                            child: CircularProgressIndicator(),
                                          );
                                        },
                                      );

                                      await UserService().updateUser(_user!);

                                      // Fermer l'indicateur de chargement
                                      Navigator.pop(context);
                                      // Fermer le modal
                                      Navigator.pop(context);
                                      // REFRESH THE PAGE - Add this line
                                      await _fetchUserData();

                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        SnackBar(
                                          content: Text(
                                              "Profil mis à jour avec succès!"),
                                          backgroundColor: Colors.green,
                                          behavior: SnackBarBehavior.floating,
                                        ),
                                      );
                                    }
                                  } catch (e) {
                                    // Fermer l'indicateur de chargement s'il est affiché
                                    Navigator.of(context)
                                        .popUntil((route) => route.isFirst);

                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text("Erreur: $e"),
                                        backgroundColor:
                                            Color.fromARGB(255, 241, 128, 153),
                                        behavior: SnackBarBehavior.floating,
                                      ),
                                    );
                                  }
                                }
                              },
                              icon: const Icon(Icons.save),
                              label: const Text("Enregistrer"),
                              style: ElevatedButton.styleFrom(
                                foregroundColor: Colors.white,
                                backgroundColor: Colors.green,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        });
      },
    );
  }

  // Nouvelle méthode pour changer le mot de passe
  void _showChangePasswordModal() {
    final formKey = GlobalKey<FormState>();
    bool _obscureCurrentPassword = true;
    bool _obscureNewPassword = true;
    bool _obscureConfirmPassword = true;

    // Réinitialiser les contrôleurs
    _currentPasswordController.clear();
    _newPasswordController.clear();
    _confirmPasswordController.clear();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return StatefulBuilder(builder: (context, setModalState) {
          return Container(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(25),
                topRight: Radius.circular(25),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Form(
                key: formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // En-tête du formulaire
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "Changer le mot de passe",
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.green[800],
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.close),
                            onPressed: () => Navigator.pop(context),
                          ),
                        ],
                      ),
                      const Divider(),
                      const SizedBox(height: 10),

                      // Mot de passe actuel
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16.0),
                        child: TextFormField(
                          controller: _currentPasswordController,
                          obscureText: _obscureCurrentPassword,
                          decoration: InputDecoration(
                            labelText: "Mot de passe actuel",
                            prefixIcon: Icon(Icons.lock_outline,
                                color: Colors.green[700]),
                            suffixIcon: IconButton(
                              icon: Icon(
                                _obscureCurrentPassword
                                    ? Icons.visibility
                                    : Icons.visibility_off,
                                color: Colors.grey[600],
                              ),
                              onPressed: () {
                                setModalState(() {
                                  _obscureCurrentPassword =
                                      !_obscureCurrentPassword;
                                });
                              },
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                              borderSide:
                                  BorderSide(color: Colors.grey.shade300),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                              borderSide:
                                  BorderSide(color: Colors.grey.shade300),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                              borderSide: BorderSide(
                                  color: Colors.green.shade700, width: 2),
                            ),
                            filled: true,
                            fillColor: Colors.white,
                            contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 16),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Veuillez entrer votre mot de passe actuel';
                            }
                            return null;
                          },
                        ),
                      ),

                      // Nouveau mot de passe
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16.0),
                        child: TextFormField(
                          controller: _newPasswordController,
                          obscureText: _obscureNewPassword,
                          decoration: InputDecoration(
                            labelText: "Nouveau mot de passe",
                            prefixIcon:
                                Icon(Icons.lock, color: Colors.green[700]),
                            suffixIcon: IconButton(
                              icon: Icon(
                                _obscureNewPassword
                                    ? Icons.visibility
                                    : Icons.visibility_off,
                                color: Colors.grey[600],
                              ),
                              onPressed: () {
                                setModalState(() {
                                  _obscureNewPassword = !_obscureNewPassword;
                                });
                              },
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                              borderSide:
                                  BorderSide(color: Colors.grey.shade300),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                              borderSide:
                                  BorderSide(color: Colors.grey.shade300),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                              borderSide: BorderSide(
                                  color: Colors.green.shade700, width: 2),
                            ),
                            filled: true,
                            fillColor: Colors.white,
                            contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 16),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Veuillez entrer un nouveau mot de passe';
                            }
                            if (value.length < 8) {
                              return 'Le mot de passe doit contenir au moins 8 caractères';
                            }
                            return null;
                          },
                        ),
                      ),

                      // Confirmer le nouveau mot de passe
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16.0),
                        child: TextFormField(
                          controller: _confirmPasswordController,
                          obscureText: _obscureConfirmPassword,
                          decoration: InputDecoration(
                            labelText: "Confirmer le mot de passe",
                            prefixIcon: Icon(Icons.lock_reset,
                                color: Colors.green[700]),
                            suffixIcon: IconButton(
                              icon: Icon(
                                _obscureConfirmPassword
                                    ? Icons.visibility
                                    : Icons.visibility_off,
                                color: Colors.grey[600],
                              ),
                              onPressed: () {
                                setModalState(() {
                                  _obscureConfirmPassword =
                                      !_obscureConfirmPassword;
                                });
                              },
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                              borderSide:
                                  BorderSide(color: Colors.grey.shade300),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                              borderSide:
                                  BorderSide(color: Colors.grey.shade300),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                              borderSide: BorderSide(
                                  color: Colors.green.shade700, width: 2),
                            ),
                            filled: true,
                            fillColor: Colors.white,
                            contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 16),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Veuillez confirmer votre mot de passe';
                            }
                            if (value != _newPasswordController.text) {
                              return 'Les mots de passe ne correspondent pas';
                            }
                            return null;
                          },
                        ),
                      ),

                      const SizedBox(height: 20),

                      // Bouton de validation
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () async {
                                if (formKey.currentState!.validate()) {
                                  try {
                                    // Afficher un indicateur de chargement
                                    showDialog(
                                      context: context,
                                      barrierDismissible: false,
                                      builder: (BuildContext context) {
                                        return Dialog(
                                          backgroundColor: Colors.transparent,
                                          elevation: 0,
                                          child: Center(
                                            child: Container(
                                              padding: EdgeInsets.all(20),
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                borderRadius:
                                                    BorderRadius.circular(15),
                                              ),
                                              child: Column(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  CircularProgressIndicator(
                                                    valueColor:
                                                        AlwaysStoppedAnimation<
                                                                Color>(
                                                            Colors.green),
                                                  ),
                                                  SizedBox(height: 15),
                                                  Text(
                                                    "Modification du mot de passe...",
                                                    style: TextStyle(
                                                      color: Colors.green[800],
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        );
                                      },
                                    );

                                    // Appel au service pour changer le mot de passe
                                    await Authservice().updatePassword(
                                      context,
                                      _currentPasswordController.text,
                                      _newPasswordController.text,
                                      _confirmPasswordController.text,
                                    );

                                    // Fermer l'indicateur de chargement
                                    Navigator.pop(context);
                                    // Fermer le modal
                                    Navigator.pop(context);

                                    // Afficher un message de succès
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                            "Mot de passe modifié avec succès!"),
                                        backgroundColor: Colors.green,
                                        behavior: SnackBarBehavior.floating,
                                      ),
                                    );
                                  } catch (e) {
                                    // Fermer l'indicateur de chargement s'il est affiché
                                    Navigator.of(context)
                                        .popUntil((route) => route.isFirst);

                                    // Afficher un message d'erreur
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text("Erreur: $e"),
                                        backgroundColor:
                                            Color.fromARGB(255, 241, 128, 153),
                                        behavior: SnackBarBehavior.floating,
                                      ),
                                    );
                                  }
                                }
                              },
                              icon: const Icon(Icons.save),
                              label: const Text("Changer le mot de passe"),
                              style: ElevatedButton.styleFrom(
                                foregroundColor: Colors.white,
                                backgroundColor: Colors.green,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        });
      },
    );
  }

  // Helper pour construire un champ de formulaire cohérent
  Widget _buildFormField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    bool enabled = true,
    TextInputType? keyboardType,
    int? maxLength,
    String? Function(String?)? validator,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: TextFormField(
        controller: controller,
        enabled: enabled,
        keyboardType: keyboardType,
        maxLength: maxLength,
        decoration: InputDecoration(
          labelText: label,
          prefixIcon: Icon(icon, color: Colors.green[700]),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(color: Colors.green.shade700, width: 2),
          ),
          filled: true,
          fillColor: enabled ? Colors.white : Colors.grey.shade100,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
        validator: validator,
      ),
    );
  }

  void _logout() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          title: Row(
            children: [
              Icon(Icons.logout, color: Color.fromARGB(255, 241, 128, 153)),
              SizedBox(width: 10),
              Text("Déconnexion"),
            ],
          ),
          content: Text("Êtes-vous sûr de vouloir vous déconnecter ?"),
          actions: <Widget>[
            TextButton(
              child: Text(
                "Annuler",
                style: TextStyle(color: Colors.grey[800]),
              ),
              onPressed: () {
                Navigator.of(context).pop();

                // Cancellation SnackBar
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text("Déconnexion annulée."),
                    behavior: SnackBarBehavior.floating,
                    backgroundColor: Color.fromARGB(255, 241, 128, 153),
                  ),
                );
              },
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Color.fromARGB(255, 241, 128, 153),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                "Déconnecter",
                style: TextStyle(color: Colors.white),
              ),
              onPressed: () {
  // Close the dialog first
  Navigator.of(context).pop();
  
  // Remove token in background (don't await)
  TokenService().removeToken();

  // Navigate immediately
  Navigator.of(context).pushNamedAndRemoveUntil(
    AppRoutes.login,
    (Route<dynamic> route) => false,
  );
},

            ),
          ],
        );
      },
    );
  }

  void _editPhoto() async {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          title: Row(
            children: [
              Icon(Icons.photo_camera, color: Colors.green[800]),
              SizedBox(width: 10),
              Text("Changer la photo"),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: Icon(Icons.photo_library, color: Colors.green[600]),
                title: Text("Choisir depuis la galerie"),
                onTap: () async {
                  Navigator.pop(context);
                  final XFile? image = await _picker.pickImage(
                    source: ImageSource.gallery,
                    imageQuality: 80,
                  );
                  if (image != null) {
                    _uploadImage(File(image.path));
                  }
                },
              ),
              ListTile(
                leading: Icon(Icons.camera_alt, color: Colors.green[600]),
                title: Text("Prendre une photo"),
                onTap: () async {
                  Navigator.pop(context);
                  final XFile? photo = await _picker.pickImage(
                    source: ImageSource.camera,
                    imageQuality: 80,
                  );
                  if (photo != null) {
                    _uploadImage(File(photo.path));
                  }
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _uploadImage(File imageFile) async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Afficher un indicateur de chargement
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return Dialog(
            backgroundColor: Colors.transparent,
            elevation: 0,
            child: Center(
              child: Container(
                padding: EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
                    ),
                    SizedBox(height: 15),
                    Text(
                      "Mise à jour de la photo...",
                      style: TextStyle(
                        color: Colors.green[800],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      );

      // Appel au service pour télécharger l'image - correction du nom de la méthode
      await UserService().updateUserPhoto(imageFile.path);

      // Récupérer la nouvelle URL de la photo
      String newPhotoUrl = await UserService().fetchPhotoUsersId();

      // Fermer l'indicateur de chargement
      Navigator.pop(context);

      setState(() {
        _photoUrl = newPhotoUrl;
        _isLoading = false;
      });

      // Afficher un message de succès
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text("Photo de profil mise à jour avec succès!"),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
        ),
      );
    } catch (e) {
      // Fermer l'indicateur de chargement s'il est affiché
      Navigator.of(context).popUntil((route) => route.isFirst);

      setState(() {
        _isLoading = false;
      });

      // Afficher un message d'erreur
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text("Erreur lors de la mise à jour de la photo: $e"),
          backgroundColor: Color.fromARGB(255, 241, 128, 153),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.green[50],
      appBar: AppBar(
        backgroundColor: Colors.green,
        elevation: 0,
        title: Text(
          "Mon Profil",
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () {
            Navigator.pushNamed(context, '/educateur/menu');
          },
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.logout, color: Colors.white),
            onPressed: _logout,
          ),
        ],
      ),
      body: _isLoading
          ? Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
              ),
            )
          : SingleChildScrollView(
              child: Column(
                children: [
                  // Section supérieure avec photo et nom
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(30),
                        bottomRight: Radius.circular(30),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        SizedBox(height: 20),
                        // Photo de profil avec bouton d'édition
                        Stack(
                          children: [
                            Container(
                              height: 120,
                              width: 120,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: Colors.white,
                                  width: 4,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.2),
                                    blurRadius: 10,
                                    offset: Offset(0, 5),
                                  ),
                                ],
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(60),
                                child: Image.network(
                                  _photoUrl,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Container(
                                      color: Colors.grey[300],
                                      child: Icon(
                                        Icons.person,
                                        size: 60,
                                        color: Colors.grey[600],
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),
                            Positioned(
                              bottom: 0,
                              right: 0,
                              child: GestureDetector(
                                onTap: _editPhoto,
                                child: Container(
                                  height: 40,
                                  width: 40,
                                  decoration: BoxDecoration(
                                    color: Color.fromARGB(255, 241, 128, 153),
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: Colors.white,
                                      width: 2,
                                    ),
                                  ),
                                  child: Icon(
                                    Icons.camera_alt,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 15),
                        // Nom et prénom
                        Text(
                          "${_firstNameController.text} ${_lastNameController.text}",
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        // Email
                        Text(
                          _emailController.text,
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.white.withOpacity(0.9),
                          ),
                        ),
                        SizedBox(height: 25),
                      ],
                    ),
                  ),

                  SizedBox(height: 20),

                  // Section des informations personnelles
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  "Informations personnelles",
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.green[800],
                                  ),
                                ),
                                IconButton(
                                  icon: Icon(
                                    Icons.edit,
                                    color: Color.fromARGB(255, 241, 128, 153),
                                  ),
                                  onPressed: _showEditModal,
                                ),
                              ],
                            ),
                            Divider(),
                            _buildInfoItem(
                              icon: Icons.person,
                              title: "Nom",
                              value: _firstNameController.text,
                            ),
                            _buildInfoItem(
                              icon: Icons.person_outline,
                              title: "Prénom",
                              value: _lastNameController.text,
                            ),
                            _buildInfoItem(
                              icon: Icons.email,
                              title: "Email",
                              value: _emailController.text,
                            ),
                            _buildInfoItem(
                              icon: Icons.phone,
                              title: "Téléphone",
                              value: _telephonelController.text,
                            ),
                            _buildInfoItem(
                              icon: Icons.credit_card,
                              title: "CIN",
                              value: _CinController.text,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  SizedBox(height: 20),

                  // Section des actions
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Actions",
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.green[800],
                              ),
                            ),
                            Divider(),
                            ListTile(
                              leading: Container(
                                padding: EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.green.withOpacity(0.1),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  Icons.edit,
                                  color: Colors.green[700],
                                ),
                              ),
                              title: Text("Modifier mon profil"),
                              trailing: Icon(Icons.arrow_forward_ios, size: 16),
                              onTap: _showEditModal,
                            ),
                            // Nouvelle action pour changer le mot de passe
                            ListTile(
                              leading: Container(
                                padding: EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.orange.withOpacity(0.1),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  Icons.lock_reset,
                                  color: Colors.orange[700],
                                ),
                              ),
                              title: Text("Changer mon mot de passe"),
                              trailing: Icon(Icons.arrow_forward_ios, size: 16),
                              onTap: _showChangePasswordModal,
                            ),
                            ListTile(
                              leading: Container(
                                padding: EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Color.fromARGB(255, 241, 128, 153)
                                      .withOpacity(0.1),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  Icons.logout,
                                  color: Color.fromARGB(255, 241, 128, 153),
                                ),
                              ),
                              title: Text("Se déconnecter"),
                              trailing: Icon(Icons.arrow_forward_ios, size: 16),
                              onTap: _logout,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  SizedBox(height: 30),
                ],
              ),
            ),
      bottomNavigationBar: MyFooterEducateur(currentRoute: '/educateur/profile'),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String title,
    required String value,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: Colors.green[700],
              size: 20,
            ),
          ),
          SizedBox(width: 15),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  value.isEmpty ? "Non renseigné" : value,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
