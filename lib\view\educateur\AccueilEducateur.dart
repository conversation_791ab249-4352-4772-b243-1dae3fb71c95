import 'dart:async';
import 'dart:convert';

import 'package:bee_kids_mobile/model/Comment.dart';
import 'package:bee_kids_mobile/model/Like.dart';
import 'package:bee_kids_mobile/model/Post.dart';
import 'package:bee_kids_mobile/view/FullScreenImageView.dart';
import 'package:bee_kids_mobile/view/InlineVideoPlayer.dart';
import 'package:bee_kids_mobile/view/PDFViewerScreen.dart';
import 'package:bee_kids_mobile/view/VideoPlayerScreen.dart';
import 'package:bee_kids_mobile/view/educateur/footer.dart';
import 'package:bee_kids_mobile/view_model/WebSocketService.dart';
import 'package:bee_kids_mobile/view_model/postService.dart';
import 'package:bee_kids_mobile/view_model/tokenService.dart';
import 'package:bee_kids_mobile/view_model/userService.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:dio/dio.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'dart:io';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';


class EducateurHome extends StatefulWidget {
  final int? postId;
  const EducateurHome({super.key, this.postId});

  @override
  _AccueilScreenState createState() => _AccueilScreenState();
}

class _AccueilScreenState extends State<EducateurHome>
    with TickerProviderStateMixin {
  final TextEditingController _commentController = TextEditingController();
  final TextEditingController _editPostController = TextEditingController();
  final PostService postService = Get.put(PostService());
  late Future<List<Post>> postsFuture;
  List<Post> posts = [];
  int page = 0;
  final int size = 3;
  bool isLoading = true;
  final UserService userService = UserService();
  String? userPhotoUrl;
  int? userConnectedId;
  bool _submitedComment = false;
  bool isUpdating = false;
  Set<int> _deletingComments = {};
  Map<String, bool> _downloadStates = {};
  Map<int, bool> _deleteStates = {};
  Map<int, bool> _editStates = {};
  StreamSubscription? _wsSubscription;
  int _photoCounter = 0;
  int _videoCounter = 0;
  int _documentCounter = 0;
  int _fileCounter = 0;
  String? _educateurFirstName;
  String? _educateurLastName;
  late AnimationController _animationController;
  late AnimationController _deleteAnimationController;
  late AnimationController _likeAnimationController;
  // Modifiez ces variables dans la classe _AccueilScreenState
  Map<String, int> _currentImageIndexes = {};

  // Save counter to SharedPreferences
  Future<void> _saveCounter(String key, int value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(key, value);
  }

  // Load all counters from SharedPreferences
  Future<void> _loadCounters() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _photoCounter = prefs.getInt('photo_counter') ?? 0;
      _videoCounter = prefs.getInt('video_counter') ?? 0;
      _documentCounter = prefs.getInt('document_counter') ?? 0;
      _fileCounter = prefs.getInt('file_counter') ?? 0;
    });
  }

  void _setupWebSocketListener() {
    _wsSubscription = WebSocketService().conversationUpdates.listen((message) {
      if (mounted) {
        setState(() {
          loadPosts();
        });
      }
    });
  }

  @override
  void dispose() {
    _wsSubscription?.cancel();
    _animationController.dispose();
    _deleteAnimationController.dispose();
    _likeAnimationController.dispose();
    _editPostController.dispose();
    _commentController.dispose();
    super.dispose();
  }

  void _fetchUserId() async {
    userConnectedId = await TokenService().getId();
    setState(() {});
  }

  Future<void> _fetchEducateurDetails() async {
    try {
      final user = await userService.fetchUsersById();
      setState(() {
        _educateurFirstName = user.nom;
        _educateurLastName = user.prenom;
      });
    } catch (e) {
      print("Erreur lors de la récupération des détails de l'éducateur : $e");
    }
  }

  Future<bool> _requestPermissions() async {
    if (await Permission.storage.request().isGranted) {
      return true;
    }

    if (await Permission.manageExternalStorage.request().isGranted) {
      return true;
    }

    if (await Permission.photos.request().isGranted &&
        await Permission.videos.request().isGranted &&
        await Permission.audio.request().isGranted) {
      return true;
    }

    return false;
  }

  Future<void> _downloadFile(BuildContext context, String fileUrl) async {
    try {
      if (!mounted) return;

      setState(() {
        _downloadStates[fileUrl] = true;
      });

      // Extract original filename and extension from URL
      final originalFileName = fileUrl.split('/').last;
      final fileExtension = originalFileName.split('.').last.toLowerCase();

      // Determine file type and create custom name
      String customFileName;

      if (['jpg', 'jpeg', 'png', 'gif', 'webp'].contains(fileExtension)) {
        // It's an image
        _photoCounter++;
        await _saveCounter('photo_counter', _photoCounter);
        customFileName = "BeekIds_Photo_${_photoCounter}.$fileExtension";
      } else if (['mp4', 'mov', 'avi', 'mkv', 'webm'].contains(fileExtension)) {
        // It's a video
        _videoCounter++;
        await _saveCounter('video_counter', _videoCounter);
        customFileName = "BeekIds_Video_${_videoCounter}.$fileExtension";
      } else if (fileExtension == 'pdf') {
        // It's a PDF
        _documentCounter++;
        await _saveCounter('document_counter', _documentCounter);
        customFileName = "BeekIds_Document_${_documentCounter}.$fileExtension";
      } else {
        // Other file types
        _fileCounter++;
        await _saveCounter('file_counter', _fileCounter);
        customFileName = "BeekIds_File_${_fileCounter}.$fileExtension";
      }

      if (Platform.isAndroid) {
        // Request permissions for Android
        bool hasPermission = await _requestAndroidPermissions();
        if (!hasPermission) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Permission de stockage refusée'),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
          return;
        }

        // Download file to temporary location first
        final dio = Dio();
        final tempDir = await getTemporaryDirectory();
        final tempPath = '${tempDir.path}/$customFileName';

        // Download the file
        await dio.download(fileUrl, tempPath,
            onReceiveProgress: (received, total) {
          if (total != -1) {
            print('${(received / total * 100).toStringAsFixed(0)}%');
          }
        });

        // Save to gallery using ImageGallerySaver for all media types
        if (['jpg', 'jpeg', 'png', 'gif', 'webp'].contains(fileExtension)) {
          // For images - save to gallery
          final result =
              await ImageGallerySaver.saveFile(tempPath, name: customFileName);
          if (result['isSuccess']) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Image sauvegardée dans la galerie'),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
              ),
            );
          } else {
            throw Exception('Failed to save image to gallery');
          }
        } else if (['mp4', 'mov', 'avi', 'mkv', 'webm']
            .contains(fileExtension)) {
          // For videos - save to gallery
          final result =
              await ImageGallerySaver.saveFile(tempPath, name: customFileName);
          if (result['isSuccess']) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Vidéo sauvegardée dans la galerie'),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
              ),
            );
          } else {
            throw Exception('Failed to save video to gallery');
          }
        } else {
          // For other files (PDFs, etc.), save to Downloads directory
          final directory = Directory('/storage/emulated/0/Download');
          final beekidsDir = Directory('${directory.path}/BeekIds');
          if (!await beekidsDir.exists()) {
            await beekidsDir.create(recursive: true);
          }

          final finalPath = '${beekidsDir.path}/$customFileName';
          await File(tempPath).copy(finalPath);

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Fichier téléchargé dans Téléchargements/BeekIds'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }

        // Clean up temp file
        try {
          await File(tempPath).delete();
        } catch (e) {
          print('Could not delete temp file: $e');
        }
      } else if (Platform.isIOS) {
        // iOS implementation (keep your existing iOS code)
        final dio = Dio();
        final tempDir = await getTemporaryDirectory();
        final savePath = '${tempDir.path}/$customFileName';

        // Download the file to temp directory
        await dio.download(fileUrl, savePath,
            onReceiveProgress: (received, total) {
          if (total != -1) {
            print('${(received / total * 100).toStringAsFixed(0)}%');
          }
        });

        // Save to Photos library
        if (['jpg', 'jpeg', 'png', 'gif', 'webp'].contains(fileExtension)) {
          // For images
          final result = await ImageGallerySaver.saveFile(savePath);
          if (result['isSuccess']) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Image sauvegardée dans Photos'),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
              ),
            );
          } else {
            throw Exception('Failed to save image to gallery');
          }
        } else if (['mp4', 'mov', 'avi', 'mkv', 'webm']
            .contains(fileExtension)) {
          // For videos
          final result = await ImageGallerySaver.saveFile(savePath);
          if (result['isSuccess']) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Vidéo sauvegardée dans Photos'),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
              ),
            );
          } else {
            throw Exception('Failed to save video to gallery');
          }
        } else {
          // For other files (PDFs, etc.), save to Documents directory
          final appDocDir = await getApplicationDocumentsDirectory();
          final beekidsDir = Directory('${appDocDir.path}/BeekIds');
          if (!await beekidsDir.exists()) {
            await beekidsDir.create(recursive: true);
          }

          final finalPath = '${beekidsDir.path}/$customFileName';
          await File(savePath).copy(finalPath);

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Fichier téléchargé dans l\'application'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      } else {
        // For other platforms, use the app's documents directory
        final appDocDir = await getApplicationDocumentsDirectory();
        final beekidsDir = Directory('${appDocDir.path}/BeekIds');
        if (!await beekidsDir.exists()) {
          await beekidsDir.create(recursive: true);
        }

        final savePath = '${beekidsDir.path}/$customFileName';

        // Download the file
        final dio = Dio();
        await dio.download(fileUrl, savePath,
            onReceiveProgress: (received, total) {
          if (total != -1) {
            print('${(received / total * 100).toStringAsFixed(0)}%');
          }
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fichier téléchargé dans l\'application'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      print('Error downloading file: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur de téléchargement: $e'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    } finally {
      // Reset download state
      if (mounted) {
        setState(() {
          _downloadStates[fileUrl] = false;
        });
      }
    }
  }

// Add this new method for Android permissions
  Future<bool> _requestAndroidPermissions() async {
    if (Platform.isAndroid) {
      // For Android 13+ (API 33+)
      if (await Permission.photos.request().isGranted &&
          await Permission.videos.request().isGranted) {
        return true;
      }

      // For Android 11-12 (API 30-32)
      if (await Permission.storage.request().isGranted) {
        return true;
      }

      // For Android 11+ with manage external storage
      if (await Permission.manageExternalStorage.request().isGranted) {
        return true;
      }

      return false;
    }
    return true;
  }

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _deleteAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _likeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    loadPosts();
    _fetchUserPhoto();
    _refreshPosts();
    _fetchUserId();
    _setupWebSocketListener();
    _loadCounters();
    _fetchEducateurDetails();

    if (widget.postId != null) {
      Future.delayed(Duration(milliseconds: 500), () {
        if (mounted) {
          _showPostDetailsPopup(widget.postId!);
        }
      });
    }
  }

  Future<Post> _fetchPostById(int id) async {
    return await postService.getPostById(id);
  }

  void _showPostDetailsPopup(int postId) async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            padding: EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
                ),
                SizedBox(height: 15),
                Text(
                  "Chargement...",
                  style: TextStyle(
                    color: Colors.green[800],
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );

    try {
      Post post = await _fetchPostById(postId);
      Navigator.pop(context);

      showDialog(
        context: context,
        builder: (BuildContext context) {
          return Dialog(
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
            child: Container(
              width: MediaQuery.of(context).size.width * 0.9,
              constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [Colors.white, Colors.green.shade50],
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.green.shade600, Colors.green.shade400],
                      ),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.article, color: Colors.white, size: 24),
                        SizedBox(width: 10),
                        Text(
                          'Détails du Post',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        Spacer(),
                        IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: Icon(Icons.close, color: Colors.white),
                        ),
                      ],
                    ),
                  ),
                  Flexible(
                    child: SingleChildScrollView(
                      padding: EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            padding: EdgeInsets.all(15),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(15),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withOpacity(0.1),
                                  spreadRadius: 1,
                                  blurRadius: 5,
                                ),
                              ],
                            ),
                            child: Row(
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                        color: Colors.green, width: 2),
                                  ),
                                  child: post.authorPhotoUrl != null &&
                                          post.authorPhotoUrl!.isNotEmpty
                                      ? CircleAvatar(
                                          backgroundImage: NetworkImage(
                                              post.authorPhotoUrl!),
                                          radius: 25,
                                        )
                                      : const CircleAvatar(
                                          backgroundImage: AssetImage(
                                              'lib/resources/images/avatar_girl.png'),
                                          radius: 25,
                                        ),
                                ),
                                const SizedBox(width: 15),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        post.authorName ??
                                            'Utilisateur inconnu',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                      Text(
                                        '${(post.role == 'Formateur' ? 'Educateur' : post.role) ?? 'Membre'} • ${_timeAgo(post.createdAt ?? DateTime.now())}',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey[600],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 20),
                          Container(
                            width: double.infinity,
                            padding: EdgeInsets.all(15),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(15),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withOpacity(0.1),
                                  spreadRadius: 1,
                                  blurRadius: 5,
                                ),
                              ],
                            ),
                            child: Text(
                              utf8.decode(latin1.encode(post.content!)) ??
                                  'Pas de contenu',
                              style: TextStyle(fontSize: 16, height: 1.5),
                            ),
                          ),
                          const SizedBox(height: 20),
                          if (post.photoUrl != null &&
                              post.photoUrl!.isNotEmpty)
                            Container(
                              height: 250,
                              width: double.infinity,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(15),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.grey.withOpacity(0.1),
                                    spreadRadius: 1,
                                    blurRadius: 5,
                                  ),
                                ],
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(15),
                                child: GridView.builder(
                                  gridDelegate:
                                      SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount: 2,
                                    crossAxisSpacing: 8,
                                    mainAxisSpacing: 8,
                                  ),
                                  shrinkWrap: true,
                                  physics: ClampingScrollPhysics(),
                                  itemCount: post.photoUrl!.length,
                                  itemBuilder: (context, index) {
                                    return GestureDetector(
                                      onTap: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) =>
                                                FullScreenImageView(
                                              imageUrl: post.photoUrl![index],
                                            ),
                                          ),
                                        );
                                      },
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(10),
                                        child: Image.network(
                                          post.photoUrl![index],
                                          fit: BoxFit.cover,
                                          errorBuilder:
                                              (context, error, stackTrace) =>
                                                  Container(
                                            color: Colors.grey[300],
                                            child: Icon(Icons.broken_image),
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    } catch (e) {
      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur lors du chargement du post: $e'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  Future<void> _fetchUserPhoto() async {
    try {
      final photoUrl = await userService.fetchPhotoUsersId();
      setState(() {
        userPhotoUrl = photoUrl;
        isLoading = false;
      });
    } catch (e) {
      print('Error fetching user photo: $e');
      setState(() {
        isLoading = false;
      });
    }
  }

  void _refreshPosts() {
    setState(() {
      posts.clear();
      page = 0;
    });
  }

  Future<void> _deletePost(int postId, int index) async {
    setState(() {
      _deleteStates[postId] = true;
    });

    try {
      _deleteAnimationController.forward();

      await postService.deletePost(postId);

      // Refresh the posts after successful deletion
      await _refreshAndReloadPosts();

      _showSuccessSnackBar('Post supprimé avec succès', Icons.delete_outline);
    } catch (e) {
      _showErrorSnackBar('Erreur lors de la suppression: $e');
    } finally {
      setState(() {
        _deleteStates[postId] = false;
      });
      _deleteAnimationController.reset();
    }
  }

// Add this new method to refresh and reload posts
  Future<void> _refreshAndReloadPosts() async {
    setState(() {
      posts.clear();
      page = 0;
      isLoading = true;
    });

    await loadPosts();
  }

  Future<void> _editPost(Post post, int index) async {
    _editPostController.text = post.content ?? '';
    bool? shouldEdit = await _showEditPostDialog(post);

    if (shouldEdit == true) {
      setState(() {
        _editStates[post.id] = true;
      });

      try {
        // Fix: Add empty list for files parameter
        await postService
            .updatePost(post.id, _editPostController.text.trim(), []);

        // Fix: Create a new Post object with updated content instead of modifying the final field
        setState(() {
          posts[index] = Post(
            id: post.id,
            content: _editPostController.text.trim(), // Updated content
            photoUrl: post.photoUrl,
            videoUrl: post.videoUrl,
            pdfUrl: post.pdfUrl,
            createdAt: post.createdAt,
            authorName: post.authorName,
            authorPhotoUrl: post.authorPhotoUrl,
            role: post.role,
            authorId: post.authorId,
            likeCount: post.likeCount,
            liked: post.liked,
            likedByUsers: post.likedByUsers,
            detailedLikedByUsers: post.detailedLikedByUsers,
            commentCount: post.commentCount,
            comments: post.comments,
            showComments: post.showComments,
          );
          _editStates[post.id] = false;
        });

        _showSuccessSnackBar('Post modifié avec succès', Icons.edit_outlined);
      } catch (e) {
        _showErrorSnackBar('Erreur lors de la modification: $e');
        setState(() {
          _editStates[post.id] = false;
        });
      }
    }
  }

  Future<bool?> _showEditPostDialog(Post post) {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Icon(Icons.edit, color: Colors.blue),
              SizedBox(width: 10),
              Text('Modifier le post'),
            ],
          ),
          content: Container(
            width: double.maxFinite,
            child: TextField(
              controller: _editPostController,
              maxLines: 5,
              decoration: InputDecoration(
                hintText: 'Modifiez votre contenu...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(15),
                  borderSide: BorderSide(color: Colors.blue, width: 2),
                ),
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: Text(
                'Annuler',
                style: TextStyle(color: Colors.grey[600]),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context, true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: Text(
                'Modifier',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showSuccessSnackBar(String message, IconData icon) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(icon, color: Colors.white),
            SizedBox(width: 10),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        duration: Duration(seconds: 3),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.error, color: Colors.white),
            SizedBox(width: 10),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        duration: Duration(seconds: 4),
      ),
    );
  }

  Future<void> loadPosts() async {
    int? userId = await TokenService().getId();
    if (userId == null) return;

    setState(() {
      isLoading = true;
    });

    try {
      List<Post> newPosts = await postService.getPaginatedPosts(page, size);

      for (var post in newPosts) {
        post.commentCount = await postService.getCommentCountForPost(post.id);
        post.likeCount = await postService.getLikeCountForPost(post.id);
        post.liked = await postService.isPostLiked(post.id, userId);
        // Use the detailed method to get users with their information
        post.likedByUsers =
            (await postService.getDetailedLikedUsersWithPhotos(post.id))
                .cast<LikeDTO>();
      }

      setState(() {
        posts.addAll(newPosts);
        page++;
      });
    } catch (e) {
      print("Error loading posts: $e");
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> loadMorePosts() async {
    loadPosts();
  }

  void toggleLike(Post post, int index) async {
    int? userId = await TokenService().getId();
    if (userId == null) return;

    bool originalLiked = post.liked;

    setState(() {
      post.liked = !post.liked;
    });

    if (post.liked) {
      _likeAnimationController.forward();
    }

    try {
      await postService.toggleLike(post.id, userId);
      print('Successfully toggled like');

      int updatedLikeCount = await postService.getLikeCountForPost(post.id);
      bool updatedLikedStatus = await postService.isPostLiked(post.id, userId);
      // Update the liked users list with detailed information
      List<UserLikeDTO> updatedLikedUsers =
          await postService.getDetailedLikedUsersWithPhotos(post.id);

      setState(() {
        post.likeCount = updatedLikeCount;
        post.liked = updatedLikedStatus;
        post.likedByUsers = updatedLikedUsers.cast<LikeDTO>();
      });
    } catch (e) {
      setState(() {
        post.liked = originalLiked;
      });
      print('Error toggling like: $e');
    }
  }

  void fetchComments(Post post) async {
    try {
      final comments = await postService.getCommentsForPost(post.id);
      final commentCount = await postService.getCommentCountForPost(post.id);
      setState(() {
        post.comments = comments;
        post.showComments = !post.showComments;
        post.commentCount = commentCount;
      });
    } catch (e) {
      print('Error fetching comments: $e');
      _showErrorSnackBar('Erreur lors du chargement des commentaires');
    }
  }

  void submitComment(Post post) async {
    int? userId = await TokenService().getId();
    final content = _commentController.text.trim();

    if (content.isEmpty) {
      _showErrorSnackBar('Veuillez saisir un commentaire');
      return;
    }

    // AJOUTEZ CETTE LIGNE : Masquer le clavier immédiatement
    FocusScope.of(context).unfocus();

    setState(() {
      _submitedComment = true;
    });

    try {
      final newComment =
          await postService.addComment(post.id, userId!, content);

      setState(() {
        post.comments.add(newComment);
        post.commentCount++;
        _commentController.clear();
      });

      // Hide keyboard after successful comment submission
      FocusScope.of(context).unfocus();

      _showSuccessSnackBar(
          'Commentaire ajouté avec succès', Icons.comment_outlined);
    } catch (e) {
      print('Error adding comment: $e');
      _showErrorSnackBar('Erreur lors de l\'ajout du commentaire');
    } finally {
      if (mounted) {
        setState(() {
          _submitedComment = false;
        });
      }
    }
  }

  Future<void> updateComment(Comment comment, String updatedContent) async {
    int? userId = await TokenService().getId();

    if (updatedContent.trim().isEmpty) {
      _showErrorSnackBar('Le commentaire ne peut pas être vide');
      return;
    }

    if (isUpdating) return;
    if (!mounted) return;

    // AJOUTEZ CETTE LIGNE : Masquer le clavier avant la mise à jour
    FocusScope.of(context).unfocus();

    setState(() => isUpdating = true);

    try {
      await postService.updateComment(
          comment.id, updatedContent.trim(), userId!);
      if (!mounted) return;
      setState(() {
        comment.commentaire = updatedContent.trim();
        comment.editMode = false;
        comment.tempContent = null;
      });

      _showSuccessSnackBar(
          'Commentaire mis à jour avec succès', Icons.edit_outlined);
    } catch (e) {
      print('Error updating comment: $e');
      _showErrorSnackBar('Erreur lors de la mise à jour du commentaire');
    } finally {
      if (mounted) {
        setState(() => isUpdating = false);
      }
    }
  }

  Future<void> deleteComment(Post post, Comment comment) async {
    int? userId = await TokenService().getId();

    if (_deletingComments.contains(comment.id)) return;

    // AJOUTEZ CETTE LIGNE : Masquer le clavier avant la suppression
    FocusScope.of(context).unfocus();

    if (!mounted) return;

    setState(() {
      _deletingComments.add(comment.id);
    });

    try {
      await postService.deleteComment(comment.id, userId!);

      if (!mounted) return;

      setState(() {
        post.comments.remove(comment);
        if (post.commentCount > 0) {
          post.commentCount--;
        }
      });

      _showSuccessSnackBar(
          'Commentaire supprimé avec succès', Icons.delete_outline);
    } catch (e) {
      print('Error deleting comment: $e');
      _showErrorSnackBar('Erreur lors de la suppression du commentaire');
    } finally {
      if (mounted) {
        setState(() {
          _deletingComments.remove(comment.id);
        });
      }
    }
  }

  String _timeAgo(DateTime date) {
    final difference = DateTime.now().difference(date);
    if (difference.inDays > 1) {
      return '${difference.inDays} jours';
    } else if (difference.inDays == 1) {
      return '1 jour';
    } else if (difference.inHours > 1) {
      return '${difference.inHours} heures';
    } else if (difference.inHours == 1) {
      return '1 heure';
    } else if (difference.inMinutes > 1) {
      return '${difference.inMinutes} minutes';
    } else {
      return 'À l\'instant';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: CustomScrollView(
        slivers: [
          _buildModernAppBar(),
          SliverToBoxAdapter(child: _buildWelcomeSection()),
          SliverToBoxAdapter(child: _buildPostInputForm()),
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                if (index == posts.length) {
                  return Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: isLoading
                        ? _buildLoadingCard()
                        : _buildLoadMoreButton(),
                  );
                }
                final post = posts[index];
                return _buildModernPost(context, post, index);
              },
              childCount: posts.length + 1,
            ),
          ),
        ],
      ),
      bottomNavigationBar:
          const MyFooterEducateur(currentRoute: '/educateur/accueil'),
    );
  }

  Widget _buildModernAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: Colors.transparent,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.green.shade600,
                Colors.green.shade400,
                Colors.teal.shade300,
              ],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: CircleAvatar(
                      backgroundImage:
                          AssetImage('lib/resources/images/logo.png'),
                      radius: 20,
                    ),
                  ),
                  SizedBox(width: 15),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text.rich(
                          TextSpan(
                            children: [
                              TextSpan(
                                text: 'BEE',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 24,
                                ),
                              ),
                              TextSpan(
                                text: '-KIDS',
                                style: TextStyle(
                                  color: Colors.pink.shade200,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 24,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Text(
                          'Partagez les moments précieux',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.9),
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Icon(
                      Icons.notifications_outlined,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Container(
      margin: EdgeInsets.all(16),
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.white, Colors.green.shade50],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: Colors.green, width: 3),
              boxShadow: [
                BoxShadow(
                  color: Colors.green.withOpacity(0.2),
                  spreadRadius: 2,
                  blurRadius: 8,
                ),
              ],
            ),
            child: CircleAvatar(
              radius: 30,
              backgroundImage: isLoading
                  ? const AssetImage('lib/resources/images/avatar_girl.png')
                      as ImageProvider
                  : (userPhotoUrl != null
                      ? NetworkImage(userPhotoUrl!)
                      : const AssetImage(
                          'lib/resources/images/avatar_girl.png')),
            ),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Bonjour ! 👋",
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  "${_educateurFirstName ?? 'Educateur'} ${_educateurLastName ?? ''}",
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 22,
                    color: Colors.green,
                  ),
                ),
                SizedBox(height: 5),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.green.shade100,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    "Espace Éducateur",
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.green.shade700,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green.shade100,
              borderRadius: BorderRadius.circular(15),
            ),
            child: Icon(
              Icons.school,
              color: Colors.green.shade600,
              size: 28,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPostInputForm() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          children: [
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.green.shade300, width: 2),
              ),
              child: CircleAvatar(
                radius: 22,
                backgroundImage: isLoading
                    ? const AssetImage('lib/resources/images/avatar_girl.png')
                        as ImageProvider
                    : (userPhotoUrl != null
                        ? NetworkImage(userPhotoUrl!)
                        : const AssetImage(
                            'lib/resources/images/avatar_girl.png')),
              ),
            ),
            SizedBox(width: 15),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  Navigator.pushNamed(context, '/educateur/poster');
                },
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: Text(
                    'Quoi de neuf ?',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(width: 12),
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.pink.shade400, Colors.pink.shade600],
                ),
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: Colors.pink.withOpacity(0.3),
                    spreadRadius: 1,
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: IconButton(
                onPressed: () {
                  Navigator.pushNamed(context, '/educateur/poster');
                },
                icon: const Icon(Icons.add, color: Colors.white, size: 24),
                padding: EdgeInsets.all(12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingCard() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
          ),
          SizedBox(height: 15),
          Text(
            "Chargement des publications...",
            style: TextStyle(
              color: Colors.green[800],
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadMoreButton() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ElevatedButton(
        onPressed: loadMorePosts,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green,
          foregroundColor: Colors.white,
          padding: EdgeInsets.symmetric(vertical: 15),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          elevation: 2,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.refresh, size: 20),
            SizedBox(width: 8),
            Text(
              "Voir plus de publications",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernPost(BuildContext context, Post post, int index) {
    if (userConnectedId == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 15,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Post Header
          Container(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.green.shade300, width: 2),
                  ),
                  child: post.authorPhotoUrl != null &&
                          post.authorPhotoUrl!.isNotEmpty
                      ? CircleAvatar(
                          backgroundImage: NetworkImage(post.authorPhotoUrl!),
                          radius: 25,
                        )
                      : const CircleAvatar(
                          backgroundImage: AssetImage(
                              'lib/resources/images/avatar_girl.png'),
                          radius: 25,
                        ),
                ),
                const SizedBox(width: 15),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        post.authorName ?? 'Utilisateur inconnu',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      SizedBox(height: 4),
                      Row(
                        children: [
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.blue.shade100,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              (post.role == 'Formateur'
                                      ? 'Educateur'
                                      : post.role) ??
                                  'Membre',
                              style: TextStyle(
                                fontSize: 11,
                                color: Colors.blue.shade700,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          SizedBox(width: 8),
                          Text(
                            _timeAgo(post.createdAt ?? DateTime.now()),
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Action buttons for post author
                if (userConnectedId == post.authorId)
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Edit button
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: _editStates[post.id] == true
                            ? Padding(
                                padding: EdgeInsets.all(12),
                                child: SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Colors.blue.shade600,
                                  ),
                                ),
                              )
                            : IconButton(
                                icon: Icon(Icons.edit,
                                    color: Colors.blue.shade600, size: 20),
                                onPressed: () => _editPost(post, index),
                                constraints:
                                    BoxConstraints(minWidth: 40, minHeight: 40),
                              ),
                      ),
                      SizedBox(width: 8),
                      // Delete button
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: _deleteStates[post.id] == true
                            ? Padding(
                                padding: EdgeInsets.all(12),
                                child: SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Colors.red.shade600,
                                  ),
                                ),
                              )
                            : IconButton(
                                icon: Icon(Icons.delete,
                                    color: Colors.red.shade600, size: 20),
                                onPressed: () async {
                                  bool? confirmDelete = await showDialog<bool>(
                                    context: context,
                                    builder: (BuildContext context) {
                                      return AlertDialog(
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(20),
                                        ),
                                        title: Row(
                                          children: [
                                            Icon(Icons.warning,
                                                color: Colors.orange),
                                            SizedBox(width: 10),
                                            Text('Confirmation'),
                                          ],
                                        ),
                                        content: Text(
                                          'Êtes-vous sûr de vouloir supprimer ce post ?',
                                          style: TextStyle(fontSize: 16),
                                        ),
                                        actions: [
                                          TextButton(
                                            onPressed: () {
                                              Navigator.pop(context, false);
                                            },
                                            child: Text(
                                              'Annuler',
                                              style: TextStyle(
                                                  color: Colors.grey[600]),
                                            ),
                                          ),
                                          ElevatedButton(
                                            onPressed: () {
                                              Navigator.pop(context, true);
                                            },
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.red,
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                              ),
                                            ),
                                            child: Text(
                                              'Supprimer',
                                              style: TextStyle(
                                                  color: Colors.white),
                                            ),
                                          ),
                                        ],
                                      );
                                    },
                                  );

                                  if (confirmDelete == true) {
                                    await _deletePost(post.id, index);
                                  }
                                },
                                constraints:
                                    BoxConstraints(minWidth: 40, minHeight: 40),
                              ),
                      ),
                    ],
                  ),
              ],
            ),
          ),

          // Post Content
          if (post.content != null && post.content!.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Container(
                width: double.infinity,
                padding: EdgeInsets.all(15),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Text(
                  post.content ?? '',
                  style: TextStyle(
                    fontSize: 15,
                    height: 1.5,
                    color: Colors.grey[800],
                  ),
                ),
              ),
            ),

          SizedBox(height: 15),

          // Media Content
          if (post.photoUrl != null && post.photoUrl!.isNotEmpty)
            _buildModernMediaGrid(context, post.photoUrl!, 'photo'),
          if (post.pdfUrl != null && post.pdfUrl!.isNotEmpty)
            _buildModernMediaGrid(context, post.pdfUrl!, 'pdf'),
          if (post.videoUrl != null && post.videoUrl!.isNotEmpty)
            _buildModernMediaGrid(context, post.videoUrl!, 'video'),

          // Action Buttons
          Container(
            padding: EdgeInsets.all(20),
            child: Row(
              children: [
                _buildModernLikeButton(post, index),
                SizedBox(width: 15),
                _buildModernViewLikesButton(post),
                SizedBox(width: 15),
                _buildModernCommentButton(post),
                Spacer(),
              ],
            ),
          ),

          // Comment Section
          if (post.showComments) _buildModernCommentSection(post),
        ],
      ),
    );
  }

  Widget _buildModernLikeButton(Post post, int index) {
    return GestureDetector(
      onTap: () => toggleLike(post, index),
      child: AnimatedContainer(
        duration: Duration(milliseconds: 200),
        padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
        decoration: BoxDecoration(
          color: post.liked ? Colors.red.shade50 : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: post.liked ? Colors.red.shade200 : Colors.grey.shade300,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedSwitcher(
              duration: Duration(milliseconds: 200),
              child: Icon(
                post.liked ? Icons.favorite : Icons.favorite_border,
                key: ValueKey(post.liked),
                color: post.liked ? Colors.red.shade600 : Colors.grey[600],
                size: 18,
              ),
            ),
            SizedBox(width: 8),
            Text(
              'J\'aime',
              style: TextStyle(
                color: post.liked ? Colors.red.shade600 : Colors.grey[600],
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernViewLikesButton(Post post) {
    return GestureDetector(
      onTap: () => _showLikedUsers(post),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
        decoration: BoxDecoration(
          color: Colors.purple.shade50,
          borderRadius: BorderRadius.circular(25),
          border: Border.all(color: Colors.purple.shade200, width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.visibility,
              color: Colors.purple.shade600,
              size: 18,
            ),
            SizedBox(width: 8),
            Text(
              '${post.likeCount}',
              style: TextStyle(
                color: Colors.purple.shade600,
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernCommentButton(Post post) {
    return GestureDetector(
      onTap: () => fetchComments(post),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
        decoration: BoxDecoration(
          color: Colors.green.shade50,
          borderRadius: BorderRadius.circular(25),
          border: Border.all(color: Colors.green.shade200, width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              color: Colors.green.shade600,
              size: 18,
            ),
            SizedBox(width: 8),
            Text(
              '${post.commentCount}',
              style: TextStyle(
                color: Colors.green.shade600,
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showLikedUsers(Post post) async {
    // Show loading dialog first
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            padding: EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
                ),
                SizedBox(height: 15),
                Text(
                  "Chargement des utilisateurs...",
                  style: TextStyle(
                    color: Colors.green[800],
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );

    try {
      // Fetch detailed liked users with photos
      List<UserLikeDTO> detailedLikedUsers =
          await postService.getDetailedLikedUsersWithPhotos(post.id);

      // Close loading dialog
      Navigator.pop(context);

      print(
          'Detailed liked users: ${detailedLikedUsers.map((u) => '${u.prenom} ${u.nom}').toList()}');

      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return Container(
            height: MediaQuery.of(context).size.height * 0.7,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(25),
                topRight: Radius.circular(25),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.3),
                  spreadRadius: 1,
                  blurRadius: 10,
                  offset: Offset(0, -2),
                ),
              ],
            ),
            child: Column(
              children: [
                // Handle bar
                Container(
                  margin: EdgeInsets.only(top: 12),
                  width: 50,
                  height: 5,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),

                // Header
                Container(
                  padding: EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.red.shade50, Colors.pink.shade50],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(25),
                      topRight: Radius.circular(25),
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.red.shade100,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child:
                            Icon(Icons.favorite, color: Colors.red, size: 24),
                      ),
                      SizedBox(width: 15),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Personnes ayant aimé',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey[800],
                              ),
                            ),
                            Text(
                              '${detailedLikedUsers.length} personne${detailedLikedUsers.length > 1 ? 's' : ''}',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: Icon(Icons.close, color: Colors.grey[600]),
                        ),
                      ),
                    ],
                  ),
                ),

                // List of users
                Expanded(
                  child: detailedLikedUsers.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                padding: EdgeInsets.all(20),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Icon(
                                  Icons.thumb_up_outlined,
                                  size: 64,
                                  color: Colors.grey[400],
                                ),
                              ),
                              SizedBox(height: 20),
                              Text(
                                'Aucun like pour le moment',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.grey[600],
                                ),
                              ),
                              SizedBox(height: 8),
                              Text(
                                'Soyez le premier à aimer ce post !',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[500],
                                ),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          padding: EdgeInsets.symmetric(
                              horizontal: 20, vertical: 10),
                          itemCount: detailedLikedUsers.length,
                          itemBuilder: (context, index) {
                            final user = detailedLikedUsers[index];
                            return Container(
                              margin: EdgeInsets.only(bottom: 12),
                              padding: EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(color: Colors.grey.shade200),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.grey.withOpacity(0.1),
                                    spreadRadius: 1,
                                    blurRadius: 6,
                                    offset: Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Row(
                                children: [
                                  Container(
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                          color: Colors.red.shade300, width: 2),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.red.withOpacity(0.2),
                                          spreadRadius: 1,
                                          blurRadius: 4,
                                        ),
                                      ],
                                    ),
                                    child: CircleAvatar(
                                      radius: 28,
                                      backgroundImage: user.userPhotoUrl !=
                                                  null &&
                                              user.userPhotoUrl!.isNotEmpty
                                          ? NetworkImage(user.userPhotoUrl!)
                                          : AssetImage(
                                                  'lib/resources/images/avatar_girl.png')
                                              as ImageProvider,
                                    ),
                                  ),
                                  SizedBox(width: 16),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          '${user.prenom} ${user.nom}',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                            color: Colors.grey[800],
                                          ),
                                        ),
                                        SizedBox(height: 6),
                                        Container(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 10, vertical: 4),
                                          decoration: BoxDecoration(
                                            color: Colors.blue.shade100,
                                            borderRadius:
                                                BorderRadius.circular(12),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Container(
                                    padding: EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.red.shade50,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Icon(
                                      Icons.favorite,
                                      color: Colors.red,
                                      size: 20,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
          );
        },
      );
    } catch (e) {
      // Close loading dialog if still open
      Navigator.pop(context);

      print('Error fetching liked users: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.error, color: Colors.white),
              SizedBox(width: 10),
              Expanded(
                child: Text('Erreur lors du chargement des utilisateurs'),
              ),
            ],
          ),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }

Widget _buildModernMediaGrid(
    BuildContext context, List<String> media, String type) {
  // Créer une clé unique pour ce carousel
  final carouselKey = '${type}_${media.hashCode}';

  // Initialiser seulement l'index si nécessaire
  if (!_currentImageIndexes.containsKey(carouselKey)) {
    _currentImageIndexes[carouselKey] = 0;
  }
  
  return Container(
    margin: EdgeInsets.symmetric(horizontal: 20),
    child: Column(
      children: [
        CarouselSlider.builder(
          // SUPPRIMEZ CETTE LIGNE :
          // carouselController: _carouselControllers[carouselKey],
          options: CarouselOptions(
            height: 300,
            enableInfiniteScroll: false,
            autoPlay: false,
            enlargeCenterPage: true,
            viewportFraction: 0.9,
            onPageChanged: (index, reason) {
              setState(() {
                _currentImageIndexes[carouselKey] = index;
              });
            },
          ),
          itemCount: media.length,
          itemBuilder: (context, index, realIndex) {
            final mediaUrl = media[index];
            final isDownloading = _downloadStates[mediaUrl] ?? false;

            return Container(
              margin: EdgeInsets.symmetric(horizontal: 5),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    spreadRadius: 1,
                    blurRadius: 10,
                    offset: Offset(0, 3),
                  ),
                ],
              ),
              child: Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(20),
                    child: _buildMediaContent(
                        context, mediaUrl, type, isDownloading),
                  ),

                  // Badge du nombre total d'images (en haut à droite)
                  if (media.length > 1)
                    Positioned(
                      top: 12,
                      right: 12,
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              spreadRadius: 1,
                              blurRadius: 4,
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.photo_library,
                              color: Colors.white,
                              size: 14,
                            ),
                            SizedBox(width: 4),
                            Text(
                              '${index + 1}/${media.length}',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            );
          },
        ),

        // Indicateurs de pagination modernes (seulement si plus d'une image)
        if (media.length > 1) ...[
          SizedBox(height: 15),
          _buildSimplePageIndicators(media.length,
              _currentImageIndexes[carouselKey] ?? 0),
        ],
      ],
    ),
  );
}
// Méthode simplifiée pour les indicateurs sans navigation
Widget _buildSimplePageIndicators(int itemCount, int currentIndex) {
  return Container(
    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
    decoration: BoxDecoration(
      gradient: LinearGradient(
        colors: [
          Colors.grey.shade100,
          Colors.white,
        ],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      borderRadius: BorderRadius.circular(25),
      boxShadow: [
        BoxShadow(
          color: Colors.grey.withOpacity(0.2),
          spreadRadius: 1,
          blurRadius: 8,
          offset: Offset(0, 2),
        ),
      ],
      border: Border.all(color: Colors.grey.shade200),
    ),
    child: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Points indicateurs avec animation
        Row(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(
            itemCount,
            (index) => AnimatedContainer(
              duration: Duration(milliseconds: 300),
              margin: EdgeInsets.symmetric(horizontal: 3),
              width: index == currentIndex ? 24 : 8,
              height: 8,
              decoration: BoxDecoration(
                gradient: index == currentIndex
                    ? LinearGradient(
                        colors: [
                          Colors.blue.shade400,
                          Colors.blue.shade600
                        ],
                      )
                    : null,
                color: index == currentIndex ? null : Colors.grey.shade300,
                borderRadius: BorderRadius.circular(4),
                boxShadow: index == currentIndex
                    ? [
                        BoxShadow(
                          color: Colors.blue.withOpacity(0.3),
                          spreadRadius: 1,
                          blurRadius: 4,
                        ),
                      ]
                    : null,
              ),
            ),
          ),
        ),

        SizedBox(width: 12),

        // Compteur
        Container(
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.photo_camera,
                color: Colors.blue.shade600,
                size: 14,
              ),
              SizedBox(width: 4),
              Text(
                '${currentIndex + 1}/$itemCount',
                style: TextStyle(
                  color: Colors.blue.shade600,
                  fontSize: 11,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    ),
  );
}

// Méthode pour créer les indicateurs modernes avec navigation
/*   Widget _buildModernPageIndicators(
      int itemCount, int currentIndex, String carouselKey) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.grey.shade100,
            Colors.white,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Bouton précédent
          GestureDetector(
            onTap: currentIndex > 0
                ? () {
                    _carouselControllers[carouselKey]?.previousPage(
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  }
                : null,
            child: Container(
              padding: EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: currentIndex > 0
                    ? Colors.blue.shade100
                    : Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.chevron_left,
                color: currentIndex > 0
                    ? Colors.blue.shade600
                    : Colors.grey.shade400,
                size: 18,
              ),
            ),
          ),

          SizedBox(width: 12),

          // Points indicateurs avec animation
          Row(
            mainAxisSize: MainAxisSize.min,
            children: List.generate(
              itemCount,
              (index) => GestureDetector(
                onTap: () {
                  _carouselControllers[carouselKey]?.animateToPage(
                    index,
                    duration: Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                },
                child: AnimatedContainer(
                  duration: Duration(milliseconds: 300),
                  margin: EdgeInsets.symmetric(horizontal: 3),
                  width: index == currentIndex ? 24 : 8,
                  height: 8,
                  decoration: BoxDecoration(
                    gradient: index == currentIndex
                        ? LinearGradient(
                            colors: [
                              Colors.blue.shade400,
                              Colors.blue.shade600
                            ],
                          )
                        : null,
                    color: index == currentIndex ? null : Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(4),
                    boxShadow: index == currentIndex
                        ? [
                            BoxShadow(
                              color: Colors.blue.withOpacity(0.3),
                              spreadRadius: 1,
                              blurRadius: 4,
                            ),
                          ]
                        : null,
                  ),
                ),
              ),
            ),
          ),

          SizedBox(width: 12),

          // Compteur
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.photo_camera,
                  color: Colors.blue.shade600,
                  size: 14,
                ),
                SizedBox(width: 4),
                Text(
                  '${currentIndex + 1}/$itemCount',
                  style: TextStyle(
                    color: Colors.blue.shade600,
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(width: 12),

          // Bouton suivant
          GestureDetector(
            onTap: currentIndex < itemCount - 1
                ? () {
                    _carouselControllers[carouselKey]?.nextPage(
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  }
                : null,
            child: Container(
              padding: EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: currentIndex < itemCount - 1
                    ? Colors.blue.shade100
                    : Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.chevron_right,
                color: currentIndex < itemCount - 1
                    ? Colors.blue.shade600
                    : Colors.grey.shade400,
                size: 18,
              ),
            ),
          ),
        ],
      ),
    );
  }
 */
  Widget _buildMediaContent(
      BuildContext context, String mediaUrl, String type, bool isDownloading) {
    if (type == 'photo') {
      return GestureDetector(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => FullScreenImageView(imageUrl: mediaUrl),
            ),
          );
        },
        child: Stack(
          fit: StackFit.expand,
          children: [
            Image.network(
              mediaUrl,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                color: Colors.grey[300],
                child:
                    Icon(Icons.broken_image, size: 50, color: Colors.grey[600]),
              ),
            ),
            if (isDownloading)
              Container(
                color: Colors.black.withOpacity(0.3),
                child: Center(
                  child: Container(
                    padding: EdgeInsets.all(15),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CircularProgressIndicator(color: Colors.green),
                        SizedBox(height: 10),
                        Text('Téléchargement...',
                            style: TextStyle(fontWeight: FontWeight.w600)),
                      ],
                    ),
                  ),
                ),
              ),
            Positioned(
              top: 15,
              right: 15,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.9),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: IconButton(
                  icon: isDownloading
                      ? SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.green,
                          ),
                        )
                      : Icon(Icons.download, color: Colors.green, size: 20),
                  onPressed: isDownloading
                      ? null
                      : () => _downloadFile(context, mediaUrl),
                ),
              ),
            ),
          ],
        ),
      );
    } else if (type == 'video') {
      return Stack(
        fit: StackFit.expand,
        children: [
          InlineVideoPlayer(videoUrl: mediaUrl),
          if (isDownloading)
            Container(
              color: Colors.black.withOpacity(0.3),
              child: Center(
                child: Container(
                  padding: EdgeInsets.all(15),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CircularProgressIndicator(color: Colors.green),
                      SizedBox(height: 10),
                      Text('Téléchargement...',
                          style: TextStyle(fontWeight: FontWeight.w600)),
                    ],
                  ),
                ),
              ),
            ),
          Positioned(
            top: 15,
            right: 15,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.9),
                borderRadius: BorderRadius.circular(25),
              ),
              child: IconButton(
                icon: isDownloading
                    ? SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.green,
                        ),
                      )
                    : Icon(Icons.download, color: Colors.green, size: 20),
                onPressed: isDownloading
                    ? null
                    : () => _downloadFile(context, mediaUrl),
              ),
            ),
          ),
        ],
      );
    } else if (type == 'pdf') {
      return GestureDetector(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => PDFViewerScreen(pdfUrl: mediaUrl),
            ),
          );
        },
        child: Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [Colors.red.shade100, Colors.red.shade50],
                ),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.red.withOpacity(0.2),
                            spreadRadius: 1,
                            blurRadius: 10,
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.picture_as_pdf,
                        color: Colors.red.shade600,
                        size: 50,
                      ),
                    ),
                    SizedBox(height: 15),
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(25),
                      ),
                      child: Text(
                        'Voir le PDF',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.red.shade600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (isDownloading)
              Container(
                color: Colors.black.withOpacity(0.3),
                child: Center(
                  child: Container(
                    padding: EdgeInsets.all(15),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CircularProgressIndicator(color: Colors.green),
                        SizedBox(height: 10),
                        Text('Téléchargement...',
                            style: TextStyle(fontWeight: FontWeight.w600)),
                      ],
                    ),
                  ),
                ),
              ),
            Positioned(
              top: 15,
              right: 15,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.9),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: IconButton(
                  icon: isDownloading
                      ? SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.green,
                          ),
                        )
                      : Icon(Icons.download, color: Colors.green, size: 20),
                  onPressed: isDownloading
                      ? null
                      : () => _downloadFile(context, mediaUrl),
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      return Container(
        color: Colors.grey[300],
        child: Icon(Icons.broken_image, size: 50, color: Colors.grey[600]),
      );
    }
  }

  Widget _buildModernCommentSection(Post post) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Comment input
          Container(
            padding: EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.green.shade300, width: 2),
                  ),
                  child: CircleAvatar(
                    radius: 18,
                    backgroundImage: isLoading
                        ? const AssetImage(
                                'lib/resources/images/avatar_girl.png')
                            as ImageProvider
                        : (userPhotoUrl != null
                            ? NetworkImage(userPhotoUrl!)
                            : const AssetImage(
                                'lib/resources/images/avatar_girl.png')),
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(25),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: TextField(
                      controller: _commentController,
                      // AJOUTEZ CES LIGNES : Gérer le focus manuellement
                      onTap: () {
                        // S'assurer que le focus est correctement géré
                      },
                      onSubmitted: (value) {
                        // Masquer le clavier après soumission
                        FocusScope.of(context).unfocus();
                        if (value.trim().isNotEmpty) {
                          submitComment(post);
                        }
                      },
                      decoration: InputDecoration(
                        hintText: 'Ajouter un commentaire...',
                        hintStyle: TextStyle(color: Colors.grey[500]),
                        border: InputBorder.none,
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 8),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: IconButton(
                    onPressed: _submitedComment
                        ? null
                        : () {
                            // AJOUTEZ CETTE LIGNE : Masquer le clavier après envoi
                            FocusScope.of(context).unfocus();
                            submitComment(post);
                          },
                    icon: _submitedComment
                        ? SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                        : Icon(Icons.send, color: Colors.white, size: 20),
                  ),
                ),
              ],
            ),
          ),

          // Comments list
          _buildModernComments(post),
        ],
      ),
    );
  }

  Widget _buildModernComments(Post post) {
    if (userConnectedId == null) {
      return Container(
        padding: EdgeInsets.all(20),
        child: Center(child: CircularProgressIndicator()),
      );
    }

    if (post.comments.isEmpty) {
      return Container(
        padding: EdgeInsets.all(20),
        child: Center(
          child: Text(
            'Aucun commentaire pour le moment',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
        ),
      );
    }

    return Column(
      children: post.comments.map((comment) {
        return Container(
          margin: EdgeInsets.only(left: 20, right: 20, bottom: 15),
          padding: EdgeInsets.all(15),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(15),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header avec photo et nom
              Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.blue.shade200, width: 2),
                    ),
                    child: comment.userPhotoUrl != null &&
                            comment.userPhotoUrl!.isNotEmpty
                        ? CircleAvatar(
                            backgroundImage:
                                NetworkImage(comment.userPhotoUrl!),
                            radius: 16,
                          )
                        : const CircleAvatar(
                            backgroundImage: AssetImage(
                                'lib/resources/images/avatar_girl.png'),
                            radius: 16,
                          ),
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${comment.prenomUtilisateur} ${comment.nomUtilisateur}',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                            color: Colors.blue.shade700,
                          ),
                        ),
                        Text(
                          'Posté le ${DateFormat('dd/MM/yyyy HH:mm').format(comment.createdAt.add(Duration(hours: 1)).toLocal())}',
                          style: TextStyle(
                            fontSize: 11,
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Boutons d'action pour l'auteur du commentaire
                  if (userConnectedId == comment.userId)
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.blue.shade50,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: IconButton(
                            icon: Icon(Icons.edit,
                                size: 16, color: Colors.blue.shade600),
                            onPressed: () {
                              setState(() {
                                comment.editMode = true;
                                comment.tempContent = comment.commentaire;
                              });
                            },
                            constraints:
                                BoxConstraints(minWidth: 32, minHeight: 32),
                          ),
                        ),
                        SizedBox(width: 5),
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.red.shade50,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: IconButton(
                            icon: _deletingComments.contains(comment.id)
                                ? SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: Colors.red.shade600,
                                    ),
                                  )
                                : Icon(Icons.delete,
                                    size: 16, color: Colors.red.shade600),
                            onPressed: _deletingComments.contains(comment.id)
                                ? null
                                : () => _showDeleteCommentDialog(post, comment),
                            constraints:
                                BoxConstraints(minWidth: 32, minHeight: 32),
                          ),
                        ),
                      ],
                    ),
                ],
              ),
              SizedBox(height: 10),

              // Contenu du commentaire ou champ d'édition
              comment.editMode
                  ? _buildEditCommentField(comment)
                  : _buildCommentContent(comment),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCommentContent(Comment comment) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        comment.commentaire,
        style: TextStyle(
          fontSize: 14,
          height: 1.4,
          color: Colors.grey[800],
        ),
      ),
    );
  }

  Widget _buildEditCommentField(Comment comment) {
    final TextEditingController editController = TextEditingController(
      text: comment.tempContent ?? comment.commentaire,
    );

    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          TextField(
            controller: editController,
            onChanged: (value) {
              comment.tempContent = value;
            },
            // AJOUTEZ CETTE LIGNE : Gérer la soumission avec Enter
            onSubmitted: (value) {
              FocusScope.of(context).unfocus();
              if (value.trim().isNotEmpty) {
                updateComment(comment, value.trim());
              }
            },
            maxLines: 3,
            decoration: InputDecoration(
              hintText: 'Modifier votre commentaire',
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(15),
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () {
                    // AJOUTEZ CETTE LIGNE : Masquer le clavier lors de l'annulation
                    FocusScope.of(context).unfocus();
                    setState(() {
                      comment.editMode = false;
                      comment.tempContent = comment.commentaire;
                    });
                  },
                  child: Text(
                    'Annuler',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ),
                SizedBox(width: 10),
                ElevatedButton(
                  onPressed: isUpdating
                      ? null
                      : () {
                          // AJOUTEZ CETTE LIGNE : Masquer le clavier avant sauvegarde
                          FocusScope.of(context).unfocus();
                          updateComment(comment,
                              comment.tempContent ?? comment.commentaire);
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: isUpdating
                      ? SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : Text(
                          'Sauvegarder',
                          style: TextStyle(color: Colors.white),
                        ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteCommentDialog(Post post, Comment comment) {
    // AJOUTEZ CETTE LIGNE : Masquer le clavier avant d'afficher le dialog
    FocusScope.of(context).unfocus();

    showDialog(
      context: context,
      barrierDismissible: true, // Permettre de fermer en cliquant à l'extérieur
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Icon(Icons.warning, color: Colors.orange),
              SizedBox(width: 10),
              Text('Confirmation'),
            ],
          ),
          content: Text(
            'Êtes-vous sûr de vouloir supprimer ce commentaire ?',
            style: TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () {
                // AJOUTEZ CETTE LIGNE : Masquer le clavier lors de l'annulation
                FocusScope.of(context).unfocus();
                Navigator.pop(context);
              },
              child: Text(
                'Annuler',
                style: TextStyle(color: Colors.grey[600]),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                // AJOUTEZ CETTE LIGNE : Masquer le clavier avant la suppression
                FocusScope.of(context).unfocus();
                Navigator.pop(context);
                deleteComment(post, comment);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: Text(
                'Supprimer',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }
}
