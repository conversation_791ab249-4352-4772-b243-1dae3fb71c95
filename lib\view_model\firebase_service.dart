import 'dart:convert';
import 'dart:io';

import 'package:bee_kids_mobile/model/Notification.dart';
import 'package:bee_kids_mobile/view/directrice/Messagerie/Conversation.dart';
import 'package:bee_kids_mobile/view/educateur/Messagerie/Conversation.dart';
import 'package:bee_kids_mobile/view/parent/Messagerie/Conversation.dart';
import 'package:bee_kids_mobile/view_model/eleveService.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../resources/environnement/apiUrl.dart';
import 'package:bee_kids_mobile/main.dart';
import 'package:bee_kids_mobile/view_model/userService.dart';
import 'package:bee_kids_mobile/view_model/tokenService.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../resources/environnement/apiUrl.dart';

class FirebaseService {
  static final FirebaseService _instance = FirebaseService._internal();
  final TokenService tokenService = TokenService();
  final _userService = UserService();
  final _eleveService = eleveService();
  final baseUrl = ApiUrl.baseUrl;
  bool _initialized = false;
  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  factory FirebaseService() => _instance;
  FirebaseMessaging messaging = FirebaseMessaging.instance;

  FirebaseService._internal();

  Future<void> initialize() async {
    if (_initialized) return;

    try {
      // Check if Firebase is already initialized
      try {
        Firebase.app();
        debugPrint('Firebase was already initialized');
      } catch (e) {
        debugPrint('Initializing Firebase...');
        await Firebase.initializeApp();
        debugPrint('Firebase initialization completed');
      }

      _initialized = true;

      // Initialize local notifications
      await _initializeLocalNotifications();

      // Request permission for notifications
      NotificationSettings settings = await messaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
      );

      debugPrint('User granted permission: ${settings.authorizationStatus}');

      // IMPORTANT: Disable automatic notification display
      await messaging.setForegroundNotificationPresentationOptions(
        alert: false, // Disable system alerts
        badge: true, // Allow badge updates
        sound: false, // Disable system sounds
      );

      // For iOS, try to get APNS token but don't block initialization
      if (Platform.isIOS) {
        try {
          // Wait for APNS token to be available
          String? apnsToken = await messaging.getAPNSToken();
          int retryCount = 0;
          const maxRetries = 5; // Reduced retries

          while (apnsToken == null && retryCount < maxRetries) {
            debugPrint('Waiting for APNS token... Attempt ${retryCount + 1}');
            await Future.delayed(
                const Duration(milliseconds: 500)); // Shorter delay
            apnsToken = await messaging.getAPNSToken();
            retryCount++;
          }

          if (apnsToken != null) {
            debugPrint('✅ APNS Token obtained: $apnsToken');
          } else {
            debugPrint('⚠️ APNS token not available, continuing without it');
            // Continue initialization without APNS token
          }
        } catch (e) {
          debugPrint('⚠️ Error getting APNS token: $e, continuing anyway');
          // Continue initialization even if APNS token fails
        }
      }

      // For Android, disable auto-init but still get the token manually
      if (Platform.isAndroid) {
        await messaging.setAutoInitEnabled(false);
      }

      // Try to get FCM token, but handle errors gracefully
      try {
        String? token = await messaging.getToken();
        debugPrint('FCM Token: $token');
        if (token != null) {
          // Store the token in SharedPreferences to check if it changed
          final prefs = await SharedPreferences.getInstance();
          final storedToken = prefs.getString('fcm_token');

          if (storedToken != token) {
            // Only register if the token has changed
            final success = await registerFCMTokenWithBackend(token);
            if (success) {
              await prefs.setString('fcm_token', token);
              debugPrint('✅ New FCM token stored: $token');
            }
          } else {
            debugPrint('✅ FCM token unchanged, skipping registration');
          }
        }
      } catch (e) {
        debugPrint('⚠️ Error getting FCM token: $e');
        // Continue without FCM token for now
      }

      // Subscribe to the 'all' topic
      try {
        await subscribeToAllTopics();
      } catch (e) {
        debugPrint('⚠️ Error subscribing to topics: $e');
      }

      // Listen for token refresh
      messaging.onTokenRefresh.listen((newToken) {
        debugPrint('FCM Token refreshed: $newToken');
        registerFCMTokenWithBackend(newToken);
      });

      // Set up message handlers
      _setupFirebaseMessagingHandlers();

      debugPrint('✅ Firebase service initialization completed');
    } catch (e) {
      debugPrint('❌ Error initializing Firebase: $e');
      _initialized = false; // Reset flag so we can try again
      // Don't throw the error, just log it and continue
      debugPrint('⚠️ Continuing app initialization despite Firebase error');
    }
  }

  Future<void> cancelAllSystemNotifications() async {
    if (Platform.isAndroid) {
      try {
        const platform = MethodChannel('com.beeKids.bee_kids/notifications');
        await platform.invokeMethod('cancelAllNotifications');
        debugPrint('✅ Cancelled all system notifications');
      } catch (e) {
        debugPrint('❌ Error cancelling system notifications: $e');
      }
    }
  }

  Future<void> _initializeLocalNotifications() async {
    // Android initialization settings
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    // iOS initialization settings
    final DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    // Initialize settings for both platforms
    final InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    // Initialize the plugin
    await flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (NotificationResponse response) {
        // Handle notification tap
        final payload = response.payload;
        if (payload != null) {
          try {
            final data = Map<String, dynamic>.from(jsonDecode(payload));
            handleNotificationOpen(data);
          } catch (e) {
            debugPrint('Error parsing notification payload: $e');
          }
        }
      },
    );

    // Create notification channel for Android
    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      'high_importance_channel',
      'High Importance Notifications',
      description: 'This channel is used for important notifications.',
      importance: Importance.max,
      playSound: true,
      enableVibration: true,
    );

    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);
  }

  final Map<String, int> _processedMessageIds = <String, int>{};
  int _deduplicationWindow = 60000; // 1 minute

  void _setupFirebaseMessagingHandlers() {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
      debugPrint('--->Foreground message: ${message.messageId}');
      debugPrint('--->From: ${message.from}');
      debugPrint('--->Title: ${message.notification?.title}');
      debugPrint('--->Body: ${message.notification?.body}');
      debugPrint('--->Data: ${message.data}');

      // IMPORTANT: Check if the message is empty or invalid
      if (message.data.isEmpty && message.notification == null) {
        debugPrint("⚠️ Received empty FCM message in foreground, ignoring");
        return; // Skip processing empty messages
      }

      // Implement the same deduplication logic as in the background handler
      final String notificationId = message.data['notificationId'] ?? '';

      // Create a unique key for this notification
      final String dedupeKey;
      if (notificationId.isNotEmpty) {
        dedupeKey = notificationId;
      } else if (message.messageId != null && message.messageId!.isNotEmpty) {
        dedupeKey = message.messageId!;
      } else {
        // Create a stable key from the content if no IDs are available
        final String content = message.data['content'] ?? '';
        final String title = message.notification?.title ?? '';
        final String body = message.notification?.body ?? '';

        // Combine available data to create a more stable hash
        final String combinedData = "$title-$body-$content";

        if (combinedData.trim().isEmpty) {
          debugPrint("⚠️ Message has no identifiable content, using timestamp");
          dedupeKey = "empty-${DateTime.now().millisecondsSinceEpoch}";
        } else {
          dedupeKey = combinedData.hashCode.toString();
        }
      }

      // Get current time
      final int now = DateTime.now().millisecondsSinceEpoch;

      // Check if we've seen this notification recently
      if (_processedMessageIds.containsKey(dedupeKey)) {
        final int lastProcessed = _processedMessageIds[dedupeKey]!;
        if (now - lastProcessed < _deduplicationWindow) {
          debugPrint(
              "🔄 Skipping duplicate notification: $dedupeKey (received ${now - lastProcessed}ms after previous)");
          return;
        }
      }

      // Update the processed time for this notification
      _processedMessageIds[dedupeKey] = now;

      // Clean up old entries from the deduplication map
      _processedMessageIds.removeWhere(
          (key, timestamp) => now - timestamp > _deduplicationWindow);

      // Extract notification details
      String title = message.notification?.title ?? 'BeeKids';
      String body = message.notification?.body ??
          message.data['content'] ??
          message.data['messageContent'] ??
          'Nouvelle notification';

      // If both title and body are default values and we have no data,
      // this is likely a malformed message - skip it
      if (title == 'BeeKids' &&
          body == 'Nouvelle notification' &&
          message.data.isEmpty) {
        debugPrint("⚠️ Skipping notification with default values and no data");
        return;
      }

      // Use a consistent notification ID
      int notificationIdInt;
      try {
        notificationIdInt = int.parse(notificationId);
      } catch (e) {
        notificationIdInt = dedupeKey.hashCode;
      }

      // Show local notification instead of system notification
      await flutterLocalNotificationsPlugin.show(
        notificationIdInt, // Use consistent ID
        title,
        body,
        NotificationDetails(
          android: AndroidNotificationDetails(
            'high_importance_channel',
            'High Importance Notifications',
            channelDescription:
                'This channel is used for important notifications.',
            importance: Importance.max,
            priority: Priority.high,
            playSound: true,
            enableVibration: true,
            icon: '@mipmap/ic_launcher',
          ),
          iOS: DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),
        payload: jsonEncode(message.data),
      );

      // Add this log after showing the notification
      debugPrint(
          "📲 FIREBASE_SERVICE: Local notification displayed - ID: $notificationIdInt, Title: $title");
    });

    // Add specific iOS token debugging
    if (Platform.isIOS) {
      this.messaging.getAPNSToken().then((token) {
        print("[iOS] APNS Token: $token");
      });

      this.messaging.getToken().then((token) {
        print("[iOS] FCM Token: $token");
      });
    }

    // Handle background/terminated messages
    // This is registered in main.dart: FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

    // Handle when user taps on notification (app in background)
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      print("[iOS] onMessageOpenedApp: ${message.messageId}");

      // Skip empty messages
      if (message.data.isEmpty && message.notification == null) {
        debugPrint("⚠️ Received empty FCM message on tap, ignoring");
        return;
      }

      handleNotificationOpen(message.data);
    });

    // Handle initial message (app was terminated)
    messaging.getInitialMessage().then((RemoteMessage? message) {
      if (message != null) {
        print(
            "[iOS] Initial message received when app was terminated: ${message.messageId}");

        // Skip empty messages
        if (message.data.isEmpty && message.notification == null) {
          debugPrint("⚠️ Received empty initial FCM message, ignoring");
          return;
        }

        handleNotificationOpen(message.data);
      }
    });
  }

  void handleNotificationOpen(Map<String, dynamic> data) {
    try {
      debugPrint('📩 Tapped notification data: $data');

      // Try to parse as NotificationModel if possible
      NotificationModel? notification;
      try {
        notification = NotificationModel.fromJson(data);
        debugPrint('📩 Parsed NotificationModel: ${notification.toJson()}');
      } catch (e) {
        debugPrint('Error parsing notification model: $e');
      }

      // Determine notification type
      String type = _resolveNotificationType(data, notification);

      // Navigate based on type
      _navigateToDestination(type, data, notification);
    } catch (e) {
      debugPrint('Error in handleNotificationOpen: $e');
    }
  }

  String _resolveNotificationType(
      Map<String, dynamic> data, NotificationModel? notification) {
    // First try to get type from notification model
    if (notification != null) {
      if (notification.isEvent == true) return 'evenement';
      if (notification.isSuivie == true) return 'suivi';
      if (notification.isNewPub == true) return 'publication';
      if (notification.isMenu == true) return 'menu';
      if (notification.is1to1Message == true ||
          notification.isGroupMessage == true) return 'message';
    }

    // Fallback to checking raw data
    if (data.containsKey('isEvent') &&
        (data['isEvent'] == true || data['isEvent'] == 'true')) {
      return 'evenement';
    } else if (data.containsKey('isSuivie') &&
        (data['isSuivie'] == true || data['isSuivie'] == 'true')) {
      return 'suivi';
    } else if (data.containsKey('isNewPub') &&
        (data['isNewPub'] == true || data['isNewPub'] == 'true')) {
      return 'publication';
    } else if (data.containsKey('isNewPubReq') &&
        (data['isNewPubReq'] == true || data['isNewPubReq'] == 'true')) {
      return 'publication_acceptee';
    } else if (data.containsKey('isMenu') &&
        (data['isMenu'] == true || data['isMenu'] == 'true')) {
      return 'menu';
    } else if ((data.containsKey('is1to1Message') &&
            (data['is1to1Message'] == true ||
                data['is1to1Message'] == 'true')) ||
        (data.containsKey('isGroupMessage') &&
            (data['isGroupMessage'] == true ||
                data['isGroupMessage'] == 'true'))) {
      return 'message';
    }

    return 'general';
  }

  void _navigateToDestination(String type, Map<String, dynamic> data,
      NotificationModel? notification) async {
    final role = await tokenService.getRole();
    if (role == null) return;

    Future.delayed(const Duration(milliseconds: 300), () async {
      final context = navigatorKey.currentContext;
      if (context == null) return;

      switch (type) {
        case 'evenement':
          _navigateToEvent(context, role, data);
          break;
        case 'suivi':
          _navigateToSuivi(context, role, data, notification);
          break;
        case 'publication':
          _navigateToPublication(context, role);
          break;
        case 'publication_acceptee':
          _navigateToPublicationAcceptee(context, role, data, notification);
          break;
        case 'menu':
          _navigateToMenu(context, role);
          break;
        case 'message':
          _navigateToMessage(context, role, data);
          break;
        default:
          _navigateToHome(context, role);
          break;
      }
    });
  }

  void _navigateToEvent(
      BuildContext context, String role, Map<String, dynamic> data) {
    final date = data['dateEvent'];
    final route = role == 'Parent'
        ? '/parent/Evennements'
        : role == 'Directeur'
            ? '/directrice/Evennements'
            : '/educateur/Evennements';

    Navigator.of(context).pushNamed(route, arguments: {'selectedDate': date});
  }

  Future<void> _navigateToSuivi(BuildContext context, String role,
      Map<String, dynamic> data, NotificationModel? notification) async {
    int? eleveId = notification?.eleveId ?? _parseInt(data['eleveId']);

    if (eleveId != null) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) => const Center(child: CircularProgressIndicator()),
      );

      try {
        final eleve = await _eleveService.getEleveById(eleveId);
        Navigator.pop(context);

        final route = role == 'Parent'
            ? '/parent/suivie_enfant'
            : role == 'Directeur'
                ? '/directrice/suivie_enfant'
                : '/educateur/suivie_enfant';

        Navigator.pushNamed(context, route, arguments: {
          'id': eleve.id,
          'dateDeNaissance': eleve.dateDeNaissance,
          'nom': eleve.nom,
          'prenom': eleve.prenom,
        });
      } catch (_) {
        if (Navigator.canPop(context)) Navigator.pop(context);
        _navigateToSuiviList(context, role);
      }
    } else {
      _navigateToSuiviList(context, role);
    }
  }

  void _navigateToSuiviList(BuildContext context, String role) {
    final route = role == 'Parent'
        ? '/parent/suivie_enfants'
        : role == 'Directeur'
            ? '/directrice/suivie_enfants'
            : '/educateur/suivie_enfants';

    Navigator.of(context).pushNamed(route);
  }

  void _navigateToPublication(BuildContext context, String role) {
    if (role == 'Directeur') {
      Navigator.of(context).pushNamed('/directrice/enAttente');
    }
  }

  void _navigateToPublicationAcceptee(BuildContext context, String role,
      Map<String, dynamic> data, NotificationModel? notification) {
    final pubId = notification?.pubId ?? _parseInt(data['pubId']);

    final route = role == 'Parent'
        ? '/parent/accueil'
        : role == 'Directeur'
            ? '/directrice/acceuil'
            : '/educateur/accueil';

    Navigator.of(context).pushNamed(route, arguments: {'postId': pubId});
  }

  void _navigateToMenu(BuildContext context, String role) {
    final route = role == 'Parent'
        ? '/parent/cantine'
        : role == 'Directeur'
            ? '/directrice/cantine'
            : '/educateur/cantine';

    Navigator.of(context).pushNamed(route);
  }

  Future<void> _navigateToMessage(
      BuildContext context, String role, Map<String, dynamic> data) async {
    final conversationId = data['conversationId']?.toString();
    final senderId = _parseInt(data['userId']) ?? 0;

    if (senderId == 0) {
      _navigateToDiscussions(context, role);
      return;
    }

    try {
      final currentUserId = await tokenService.getId();
      if (currentUserId == null) return;

      final sender = await _userService.getUsersByUserId(senderId);
      final senderName = '${sender.prenom} ${sender.nom}';
      final photoUrl = data['senderPhoto']?.toString() ?? '';

      Widget screen = role == 'Parent'
          ? ConversationScreenparent(
              currentUserId: currentUserId.toString(),
              conversationId: conversationId ?? '',
              recipientUserId: senderId.toString(),
              recipientUserName: senderName,
              recipientPhotoUrl: photoUrl,
            )
          : role == 'Formateur'
              ? ConversationScreenEducateur(
                  currentUserId: currentUserId.toString(),
                  conversationId: conversationId ?? '',
                  recipientUserId: senderId.toString(),
                  recipientUserName: senderName,
                  recipientPhotoUrl: photoUrl,
                )
              : ConversationScreen(
                  currentUserId: currentUserId.toString(),
                  conversationId: conversationId ?? '',
                  recipientUserId: senderId.toString(),
                  recipientUserName: senderName,
                  recipientPhotoUrl: photoUrl,
                );

      Navigator.of(context).push(MaterialPageRoute(builder: (_) => screen));
    } catch (e) {
      debugPrint('❌ Error in _navigateToMessage: $e');
      _navigateToDiscussions(context, role);
    }
  }

  void _navigateToDiscussions(BuildContext context, String role) {
    final route = role == 'Parent'
        ? '/parent/Discussions'
        : role == 'Directeur'
            ? '/directrice/Discussions'
            : '/educateur/Discussions';

    Navigator.of(context).pushNamed(route);
  }

  void _navigateToHome(BuildContext context, String role) {
    final route = role == 'Parent'
        ? '/parent/accueil'
        : role == 'Directeur'
            ? '/directrice/acceuil'
            : '/educateur/accueil';

    Navigator.of(context).pushNamed(route);
  }

  int? _parseInt(dynamic val) {
    if (val == null) return null;
    if (val is int) return val;
    return int.tryParse(val.toString());
  }

  Future<bool> registerFCMTokenWithBackend(String fcmToken) async {
    try {
      final userId = await tokenService.getId();
      String deviceType = Platform.isIOS ? 'ios' : 'android';
      final url = Uri.parse(
        '${ApiUrl.baseUrl}api/firebase/register?userId=$userId&token=$fcmToken&deviceType=$deviceType',
      );

      final headers = await _getAuthHeaders();

      final response = await http.post(url, headers: headers);

      debugPrint('Saving fcmResponse status: ${response.statusCode}');
      debugPrint('Saving fcm Response body: ${response.body}');

      // Ensure response is valid JSON
      try {
        if (response.statusCode == 200 || response.statusCode == 201) {
          return response.statusCode == 200;
        } else {
          debugPrint(
              '❌ Failed to register FCM token. Status: ${response.statusCode}');
          return false;
        }
      } catch (e) {
        debugPrint('❌ Failed to register FCM token: $e');
        return false;
      }
    } catch (e) {
      debugPrint('Error registering FCM token: $e');
      return false;
    }
  }

  Future<Map<String, String>> _getAuthHeaders() async {
    final token = await tokenService.getToken();
    return {
      'Authorization': token != null ? 'Bearer $token' : '',
      'Content-Type': 'application/json',
    };
  }

  Future<void> subscribeToAllTopics() async {
  if (ApiUrl.baseUrl == 'https://beekids.back.dpc.com.tn/') {
    await messaging.subscribeToTopic('dev');
    debugPrint("📱 Subscribing to dev topic: /topics/dev");
  } else if (ApiUrl.baseUrl == 'https://maternalkids.back.beekids.tn/') {
    await messaging.subscribeToTopic('all');
    debugPrint("📱 Subscribing to prod topic: /topics/all");
  } 
  else if (ApiUrl.baseUrl == 'https://test.firebase.beekids.tn/') {
    await messaging.subscribeToTopic('test_firebase');
    debugPrint("📱 Subscribing to test_firebase topic: /topics/test_firebase");
  } else {
    await messaging.subscribeToTopic('local');
    debugPrint("⚠️ Unknown environment, Subscribing to local topic: /topics/local");
  }
}


  Future<bool> deleteFirebaseToken(int userId) async {
    final url = Uri.parse('${baseUrl}api/firebase/deleteToken?userId=$userId');
    print(' deleteFirebaseToken URL: $url');

    final response = await http.delete(
      url,
      headers: {
        'Accept': '*/*',
      },
    );

    if (response.statusCode == 200) {
      // Success
      print(
          ' deleteFirebaseToken Token deleted successfully for userId $userId');
      return true;
    } else {
      // Failure
      print(
          ' deleteFirebaseToken Failed to delete token: ${response.statusCode} - ${response.body}');
      return false;
    }
  }
}
