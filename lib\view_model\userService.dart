import 'dart:convert';
import 'dart:io';
import 'package:bee_kids_mobile/model/user.dart';
import 'package:bee_kids_mobile/model/eleve.dart';
import 'package:bee_kids_mobile/model/userDTO.dart';
import 'package:bee_kids_mobile/resources/environnement/apiUrl.dart';
import 'package:bee_kids_mobile/view_model/tokenService.dart';
import 'package:bee_kids_mobile/view_model/eleveService.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';

class UserService {
  final baseUrl = ApiUrl.baseUrl;
  final TokenService tokenService = TokenService();
  final eleveService eleveServiceInstance = eleveService();

  Future<Map<String, String>> _getAuthHeaders() async {
    final token = await tokenService.getToken();
    if (token != null) {
      return {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };
    }
    return {
      'Content-Type': 'application/json',
    };
  }



Future<String> fetchUserRole() async {
  final headers = await _getAuthHeaders();
  int? id = await tokenService.getId();

  try {
    final response = await http.get(
      Uri.parse('${baseUrl}users/$id'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      print('Response data role =  ${data['role']}');
      return data['role'];
    } else {
      throw Exception('Failed to load user role: ${response.reasonPhrase}');
    }
  } catch (e) {
    print('Error fetching user role: $e');
    rethrow;
  }
}

Future<int> getTotalUsersCountSimple() async {
  try {
    // Fetch parents, users by roles, and all eleves concurrently
    final results = await Future.wait([
      fetchAllParents(),
      fetchUsersByRoles(),
      eleveServiceInstance.getAllEleves(),
    ]);
    
    final List<User> parents = results[0] as List<User>;
    final List<User> usersByRoles = results[1] as List<User>;
    final List<Eleve> eleves = results[2] as List<Eleve>;
    
    // Return the sum of all counts
    return parents.length + usersByRoles.length + eleves.length;
    
  } catch (e) {
    print('Error getting total users count: $e');
    throw Exception('Failed to get total users count');
  }
}



 Future<List<User>> fetchAllParents() async {
    final url = Uri.parse('${baseUrl}users/allParents');
    final headers = await _getAuthHeaders();

    try {
      final response = await http.get(url, headers: headers);

      print('URL: $url');
      print('Response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => User.fromMap(json)).toList();
      } else {
        throw Exception('Failed to load parents: ${response.reasonPhrase}');
      }
    } catch (e) {
      print('Error during HTTP request: $e');
      throw Exception('Failed to load parents');
    }
  }
  Future<List<User>> fetchUsersByRoles() async {
    final url = Uri.parse('${baseUrl}users/users/roles');
    final headers = await _getAuthHeaders();

    try {
      final response = await http.get(url, headers: headers);

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => User.fromMap(json)).toList();
      } else {
        throw Exception('Failed to load users: ${response.reasonPhrase}');
      }
    } catch (e) {
      print('Error during HTTP request: $e');
      throw Exception('Failed to load users');
    }
  }


  Future<User> fetchUsersById() async {
    final headers = await _getAuthHeaders();
    int? id = await tokenService.getId();

    try {
      final response = await http.get(
        Uri.parse('${baseUrl}users/$id'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data is Map<String, dynamic>) {
          return User.fromMap(data);
        } else {
          throw Exception('Unexpected response format');
        }
      } else {
        throw Exception('Failed to load user data: ${response.reasonPhrase}');
      }
    } catch (e) {
      print('Error fetching user data: $e');
      rethrow;
    }
  }

    Future<User> getUsersByUserId(id) async {
    final headers = await _getAuthHeaders();
    

    try {
      final response = await http.get(
        Uri.parse('${baseUrl}users/$id'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data is Map<String, dynamic>) {
          return User.fromMap(data);
        } else {
          throw Exception('Unexpected response format');
        }
      } else {
        throw Exception('Failed to load user data: ${response.reasonPhrase}');
      }
    } catch (e) {
      print('Error fetching user data: $e');
      rethrow;
    }
  }
Future<String> fetchPhotoUsersByUserId(id) async {
    final headers = await _getAuthHeaders();
    

    try {
      final response = await http.get(
        Uri.parse('${baseUrl}users/get-photo/$id'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        return response.body;
      } else {
        return 'https://placehold.jp/3d4070/ffffff/150x150.png?text=photo%20introuvable';
        throw Exception('Failed to load user photo: ${response.reasonPhrase}');
      }
    } catch (e) {
      print('Error fetching user photo: $e');
      return ('https://placehold.jp/3d4070/ffffff/150x150.png?text=photo%20introuvable');
    }
  }


  Future<String> fetchPhotoUsersId() async {
    final headers = await _getAuthHeaders();
    int? id = await tokenService.getId();

    try {
      final response = await http.get(
        Uri.parse('${baseUrl}users/get-photo/$id'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        return response.body;
      } else {
        return 'https://placehold.jp/3d4070/ffffff/150x150.png?text=photo%20introuvable';
        throw Exception('Failed to load user photo: ${response.reasonPhrase}');
      }
    } catch (e) {
      print('Error fetching user photo: $e');
      return ('https://placehold.jp/3d4070/ffffff/150x150.png?text=photo%20introuvable');
    }
  }

  Future<void> updateUserPhoto(String photoUrl) async {
    final headers = await _getAuthHeaders();
    int? id = await tokenService.getId();

    try {
      final file = File(photoUrl);
      final uri = Uri.parse('${baseUrl}users/$id/update-photo');
      var request = http.MultipartRequest('PUT', uri);

      var photo = await http.MultipartFile.fromPath(
        'photo',
        file.path,
        contentType: MediaType('image', 'jpeg'),
      );
      request.files.add(photo);

      request.headers.addAll(headers);

      final response = await request.send();

      if (response.statusCode == 200) {
        print('Photo updated successfully');
      } else {
        throw Exception('Failed to update photo: ${response.reasonPhrase}');
      }
    } catch (e) {
      print('Error updating photo: $e');
      rethrow;
    }
  }

  Future<bool> updateUser(User user) async {
    final headers = await _getAuthHeaders();
    int? id = await tokenService.getId();
    final url = Uri.parse('${baseUrl}users/$id');

    // try {

    final response = await http.put(
      url,
      headers: headers,
      body: jsonEncode(user.toJson()),
    );

    print('Response Status update user: ${response.statusCode}');
    print('Response Body update user: ${response.body}');

    // if (response.statusCode == 200) {
    return true;
    // } else {
    //  throw Exception('Failed to update user: ${response.body}');
//    }
    //} catch (e) {
    //  print('Error updating user: $e');
    //  rethrow;
  }

  // Fetch directeurs and formateurs excluding a specific user ID
  Future<List<UserDTO>> fetchDirecteursAndFormateurs(
      String excludedUserId) async {
    final url = Uri.parse(
        '${baseUrl}users/users-to-send?excludedUserId=$excludedUserId');
    final headers = await _getAuthHeaders();

    try {
      final response = await http.get(url, headers: headers);

      print('URL: $url');
      print('Headers: $headers');
      print('Response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => UserDTO.fromMap(json)).toList();
      } else {
        throw Exception('Failed to load users: ${response.reasonPhrase}');
      }
    } catch (e) {
      print('Error during HTTP request: $e');
      throw Exception('Failed to load users');
    }
  }


////////////// Tableau de bord ////////////
Future<int> getNombresUtilisateurs() async {
  final url = Uri.parse('https://beekids.back.dpc.com.tn/users/NombresUtilisateurs');
  final headers = {
    'accept': '*/*',
  };
  final response = await http.get(url, headers: headers);
  print('🔁getNombresUtilisateurs Response: ${response.body}'); // Pour debug
  if (response.statusCode == 200) {
    final intValue = int.tryParse(response.body.trim());
    if (intValue != null) {
      return intValue;
    } else {
      throw Exception("getNombresUtilisateurs Réponse inattendue : ${response.body}");
    }
  } else {
    throw Exception('getNombresUtilisateurs Erreur API: ${response.statusCode} - ${response.body}');
  }
}


Future<int> getNombresParents() async {
  final url = Uri.parse('${baseUrl}users/NombresUtilisateurs/parents');
  final headers = await _getAuthHeaders();

  final response = await http.get(url, headers: headers);
  print('🔁getNombresParents Response: ${response.body}');

  if (response.statusCode == 200) {
    final intValue = int.tryParse(response.body.trim());
    if (intValue != null) {
      return intValue;
    } else {
      throw Exception("getNombresParents Réponse inattendue : ${response.body}");
    }
  } else {
    throw Exception('getNombresParents Erreur API: ${response.statusCode} - ${response.body}');
  }
}

Future<int> getNombresFormateurs() async {
  final url = Uri.parse('${baseUrl}users/NombresUtilisateurs/formateurs');
  final headers = await _getAuthHeaders();

  final response = await http.get(url, headers: headers);
  print('🔁getNombresFormateurs Response: ${response.body}');

  if (response.statusCode == 200) {
    final intValue = int.tryParse(response.body.trim());
    if (intValue != null) {
      return intValue;
    } else {
      throw Exception("getNombresFormateurs Réponse inattendue : ${response.body}");
    }
  } else {
    throw Exception('getNombresFormateurs Erreur API: ${response.statusCode} - ${response.body}');
  }
}


}