{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d4200212c11a68182eb806483d46240c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986f60ad41630d9e7ebc6257f2b7c9771a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ebc8012e88915a8ce6f247bfb3ba79a7", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9808ebb61cc9b6bf2730a4627e98ee10ff", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ebc8012e88915a8ce6f247bfb3ba79a7", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984072357f32a9f8fc95b3c02424bde0a8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9864bad8d230c62fdd8631c0aa174abb3e", "guid": "bfdfe7dc352907fc980b868725387e98aa995f4fd8832b70d35a793e2c58b035", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822881bc9be7b6477e2b864823e353f99", "guid": "bfdfe7dc352907fc980b868725387e98ee1ee74b5ae99302daf5646bfbbf13dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989af39f2e21310efc2ee9bc8f346973bd", "guid": "bfdfe7dc352907fc980b868725387e98df5ec594fe9f0f2c64141a5d8f598177", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982874cc89d40cf9a3c657a18ac2e51845", "guid": "bfdfe7dc352907fc980b868725387e989b1e8a3aa0b6c16c265fb97a40f98f68", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8e158d3bbc00e8fb472f3cb618202be", "guid": "bfdfe7dc352907fc980b868725387e98b1a1b60d4175bd6b7c3c3ea6a4bcd7c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b057f93783e83ee65da293e722e0e60", "guid": "bfdfe7dc352907fc980b868725387e987b251cf83d7f700dc01b209e5ca4e0ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898fb8240eaf4088feb18dc2156fe4f65", "guid": "bfdfe7dc352907fc980b868725387e98eceba8b54ce3435e34a32ffdcea54e8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d17b0c246c007d632069f7b43aad21f", "guid": "bfdfe7dc352907fc980b868725387e9886d253775a080807baaf6cea7449caf9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e902b666bc4bd74e6a07ded907a1785", "guid": "bfdfe7dc352907fc980b868725387e98e2110e14936fb87644318315bebd22b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ac15f4f54c0cd3477ff58eb8d5f7dc3", "guid": "bfdfe7dc352907fc980b868725387e98938409ebd6e8ad13541a0f88a19c8173", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f326af80830b3b8d59732f7fc0045ee", "guid": "bfdfe7dc352907fc980b868725387e98b42e42472dfa234449e2b3d79e02ba09", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817426ebf683c917487c25d988dc641ab", "guid": "bfdfe7dc352907fc980b868725387e98e9b2392395d6b4e86b2fd3c0e0c8bcde", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adcafd676e854cc476b3a6d9089dc311", "guid": "bfdfe7dc352907fc980b868725387e98ec2d2843ca622efe666dc0ca842c297d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827d1a2ee59826c0b7c0942082b3c1fef", "guid": "bfdfe7dc352907fc980b868725387e9828367f5c11f77572eaeab40f6dc9c0f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e58d0f424764deca452667c62e644740", "guid": "bfdfe7dc352907fc980b868725387e98fe3cd4f136d50e46e284c4d651014fcb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b56cd66e9bfbfa7d58bffdd9dcbdcc06", "guid": "bfdfe7dc352907fc980b868725387e982737b8e7bd5033241691d1bd812a6981", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809ca05020e5313e849e92087e573c684", "guid": "bfdfe7dc352907fc980b868725387e98653e4ee82198d0e4cfd42d1d837a324e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2bec7b244a96907237f3c5aa67dd5c1", "guid": "bfdfe7dc352907fc980b868725387e9816c3e44d9b08aacfb6164ad4d181c8a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982060de3811955cd0f3bcead8f5f341e1", "guid": "bfdfe7dc352907fc980b868725387e9898b5280df58d62a83dd146f3e8cf1ac2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808b1830b52b4e379390f648b8b590383", "guid": "bfdfe7dc352907fc980b868725387e98d769b6663f9fcbddf8fccbd85b0b847a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7d0bd35e8909ebe05d7a0dd23190510", "guid": "bfdfe7dc352907fc980b868725387e9865a1a73c011fb2ac68471d69621fe98f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98893830191ea338f02e2c1dc91b910130", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c880a25bf73113d0ffc8d765c6d19e62", "guid": "bfdfe7dc352907fc980b868725387e98ca2af142dd2f4c1cbd4bc96acee7cb57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa446cb104d671193e7db8236263f20f", "guid": "bfdfe7dc352907fc980b868725387e984336bdc47c8ca564386a1c8576fdee89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa4f3d595f4a8cdc2d92ae4c660948be", "guid": "bfdfe7dc352907fc980b868725387e9885fee017dfc51a0164954df2d2087e2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989520597be5bfb15897efb4e4eb204009", "guid": "bfdfe7dc352907fc980b868725387e98bb6789412ed4f8711ed5e92cdef71866"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c0d2390e9e01c51baaaa03f46c972b8", "guid": "bfdfe7dc352907fc980b868725387e982a4147db67673ce801420a61bce475c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7de54e75fdc0ce289150402d1785040", "guid": "bfdfe7dc352907fc980b868725387e98e8c39d4ea7dc850bbb6960b6bb224394"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985accba3d174bd3c56a8c9bf3b33fc623", "guid": "bfdfe7dc352907fc980b868725387e984b664a5ceb878c93dab7de6024491322"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98769b0eee0225f07b146ea9c110e1e3ff", "guid": "bfdfe7dc352907fc980b868725387e98024c3ada7b69046640ed62e41899e267"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876135b6deb231c3809df0de816617fa4", "guid": "bfdfe7dc352907fc980b868725387e985645a4a191527152741404cb24b7deb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988450c46a68a3ebeaa32829d4727e234d", "guid": "bfdfe7dc352907fc980b868725387e98b60927a0bbb8e7efb8b16f05a412b2ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2a1b06d93ed131213cbe3fb77ace685", "guid": "bfdfe7dc352907fc980b868725387e98f7f3d8af76f1642f368a6513747712f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98672d5e9cfbb500977b1df51b560d8b2e", "guid": "bfdfe7dc352907fc980b868725387e981510b6b648bd6425e81f7ad46d4e86ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c798b9ba533331287abd6365f3235295", "guid": "bfdfe7dc352907fc980b868725387e986ac94c446800990c7a0cf862178f8553"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e744fc66fc7d7f818267306e4d1a7606", "guid": "bfdfe7dc352907fc980b868725387e9892c478f28911b23c5a2dbd03f1e90651"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e22f6c13b47dbec5ab7fb3593dd3074a", "guid": "bfdfe7dc352907fc980b868725387e9815bf16158f513215373e12a4bd7f6448"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f267a4b3d68d44f1309111c76d630ea", "guid": "bfdfe7dc352907fc980b868725387e98e3d0fb33cab988ea53ddbedb4fc8ea18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986908325ef41f5a271069faf19f591e9f", "guid": "bfdfe7dc352907fc980b868725387e98a9ec8eaffb68fa7a2a61ab748c486489"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98052be365436385d295cf23e014e3fd2b", "guid": "bfdfe7dc352907fc980b868725387e989224d114eb82cfec7cc86cdf970c8b42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987590c040dd46af66435f09ddca2b6395", "guid": "bfdfe7dc352907fc980b868725387e98d311d2d36b3697824b1aec4e21fa1a51"}], "guid": "bfdfe7dc352907fc980b868725387e988f2159a3fa518201e99f45201240c014", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d56474ef5850c087d24d24a69093581f", "guid": "bfdfe7dc352907fc980b868725387e98c55b2fd41ec59641b36bba517fd96ffa"}], "guid": "bfdfe7dc352907fc980b868725387e98f59d14b41d6065eb13a4af8fcfae4a69", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e983e9e224ef10dec5e1925539f36c732b7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}