import 'dart:convert';
import 'lib/view_model/suivieService.dart';

void main() {
  // Exemple de réponse du backend que vous avez fournie
  String jsonResponse = '''
  [{"id":32,"note":"az","repas":"faible","sommeil":"moyen","humeur":"faible","jeuxExterieur":"non","interaction":"faible","participation":"faible","jour":"2025-07-28","eleveNom":"ali","formateurNom":"makramEd"}]
  ''';

  // Test de parsing avec le nouveau modèle MauvaisSuivi
  try {
    final List<dynamic> jsonData = json.decode(jsonResponse);
    
    final List<MauvaisSuivi> mauvaisSuivisList = jsonData.map((item) {
      return MauvaisSuivi.fromMap(item);
    }).toList();

    print("✅ Test réussi ! Nombre de mauvais suivis parsés: ${mauvaisSuivisList.length}");
    
    // Affichage des détails du premier élément
    if (mauvaisSuivisList.isNotEmpty) {
      final premier = mauvaisSuivisList.first;
      print("📋 Détails du premier suivi:");
      print("   - ID: ${premier.id}");
      print("   - Élève: ${premier.eleveNom}");
      print("   - Formateur: ${premier.formateurNom}");
      print("   - Date: ${premier.jour}");
      print("   - Note: ${premier.note}");
      print("   - Repas: ${premier.repas}");
      print("   - Sommeil: ${premier.sommeil}");
      print("   - Humeur: ${premier.humeur}");
      print("   - Jeux Extérieur: ${premier.jeuxExterieur}");
      print("   - Interaction: ${premier.interaction}");
      print("   - Participation: ${premier.participation}");
      
      // Test des champs faibles
      final faibles = <String, String>{
        'Repas': premier.repas,
        'Sommeil': premier.sommeil,
        'Participation': premier.participation,
        'Jeux Ext.': premier.jeuxExterieur,
        'Interaction': premier.interaction,
        'Humeur': premier.humeur,
      };
      
      final champsFaibles = faibles.entries.where((e) => e.value.toLowerCase() == 'faible').toList();
      
      print("\n⚠️ Champs faibles détectés:");
      for (var champ in champsFaibles) {
        print("   - ${champ.key}: ${champ.value}");
      }
    }
    
  } catch (e) {
    print("❌ Erreur lors du parsing: $e");
  }
}
