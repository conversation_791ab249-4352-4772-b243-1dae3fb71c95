import 'dart:io';
import 'package:flutter/material.dart';
import 'package:bee_kids_mobile/model/event.dart';
import 'package:bee_kids_mobile/view/directrice/footer.dart';
import 'package:bee_kids_mobile/view_model/evennementService.dart';
import 'package:bee_kids_mobile/view_model/tokenService.dart';
import 'package:flutter/rendering.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'dart:convert';
import 'package:intl/date_symbol_data_local.dart';
import 'package:flutter/material.dart' show ScrollDirection;

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Evennements',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: CalendarApp(),
    );
  }
}

class CalendarApp extends StatefulWidget {
  final DateTime? initialDate;

  CalendarApp({this.initialDate});
  @override
  _CalendarScreenState createState() => _CalendarScreenState();
}

class _CalendarScreenState extends State<CalendarApp> {
  final EventService _eventService = EventService();
  late Future<List<Event>> _eventsFuture;
  DateTime _selectedDay = DateTime.now();
  DateTime _focusedDay = DateTime.now();
  TextEditingController _dateController = TextEditingController();
  ScrollController _scrollController = ScrollController();
  bool _isCalendarExpanded = true;

  Offset _fabPosition = Offset(20, 20);
  Set<DateTime> _eventDays = {}; // Store the days with events
  Map<DateTime, List<Event>> _allEvents =
      {}; // Store events for all dates persistently

  @override
  void initState() {
    super.initState();
    initializeDateFormatting('fr_FR', null);
    if (widget.initialDate != null) {
      _selectedDay = widget.initialDate!;
      _focusedDay = widget.initialDate!;
    }

    // Initialize _eventsFuture immediately to prevent LateInitializationError
    _eventsFuture = _loadEventsForSelectedDateAsync(_selectedDay);

    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  // Function to remove emojis and special characters from text
  String _removeEmojis(String text) {
    // Regular expression to detect emojis, flags, and special Unicode characters
    final emojiRegex = RegExp(
      r'[\u{1F600}-\u{1F64F}]|' // Emoticons
      r'[\u{1F300}-\u{1F5FF}]|' // Misc Symbols and Pictographs
      r'[\u{1F680}-\u{1F6FF}]|' // Transport and Map
      r'[\u{1F1E0}-\u{1F1FF}]|' // Regional indicator symbols (flags)
      r'[\u{2600}-\u{26FF}]|' // Misc symbols
      r'[\u{2700}-\u{27BF}]|' // Dingbats
      r'[\u{1F900}-\u{1F9FF}]|' // Supplemental Symbols and Pictographs
      r'[\u{1F018}-\u{1F270}]|' // Various symbols
      r'[\u{238C}-\u{2454}]|' // Various symbols
      r'[\u{20D0}-\u{20FF}]|' // Combining Diacritical Marks for Symbols
      r'[\u{FE00}-\u{FE0F}]', // Variation Selectors
      unicode: true,
    );

    return text.replaceAll(emojiRegex, '').trim();
  }

  // Function to clean events data
  List<Event> _cleanEventsData(List<Event> events) {
    return events.map((event) {
      // Create a new event with cleaned data
      return Event(
        id: event.id,
        titre: _removeEmojis(event.titre),
        description: _removeEmojis(event.description),
        dateEvent: event.dateEvent,
        userId: event.userId,
        photoUrl: event.photoUrl,
        userNom: event.userNom,
        userPrenom: event.userPrenom,
        createdAt: DateTime.now(),
        photoType: '',
        updatedAt: DateTime.now(),
      );
    }).toList();
  } // Validation function to check for emojis and flags

  String? _validateInput(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName ne peut pas être vide';
    }

    // Regular expression to detect emojis, flags, and special Unicode characters
    final emojiRegex = RegExp(
      r'[\u{1F600}-\u{1F64F}]|' // Emoticons
      r'[\u{1F300}-\u{1F5FF}]|' // Misc Symbols and Pictographs
      r'[\u{1F680}-\u{1F6FF}]|' // Transport and Map
      r'[\u{1F1E0}-\u{1F1FF}]|' // Regional indicator symbols (flags)
      r'[\u{2600}-\u{26FF}]|' // Misc symbols
      r'[\u{2700}-\u{27BF}]|' // Dingbats
      r'[\u{1F900}-\u{1F9FF}]|' // Supplemental Symbols and Pictographs
      r'[\u{1F018}-\u{1F270}]|' // Various symbols
      r'[\u{238C}-\u{2454}]|' // Various symbols
      r'[\u{20D0}-\u{20FF}]|' // Combining Diacritical Marks for Symbols
      r'[\u{FE00}-\u{FE0F}]', // Variation Selectors
      unicode: true,
    );

    if (emojiRegex.hasMatch(value)) {
      return '$fieldName ne peut pas contenir d\'emojis, de drapeaux ou de symboles spéciaux';
    }

    return null;
  }

  void _scrollListener() {
    if (_scrollController.position.userScrollDirection ==
        ScrollDirection.reverse) {
      if (_isCalendarExpanded) {
        setState(() {
          _isCalendarExpanded = false;
        });
      }
    } else if (_scrollController.position.userScrollDirection ==
        ScrollDirection.forward) {
      if (!_isCalendarExpanded) {
        setState(() {
          _isCalendarExpanded = true;
        });
      }
    }
  }

  // New async method that returns Future<List<Event>>
  Future<List<Event>> _loadEventsForSelectedDateAsync(DateTime date) async {
    List<Event> events;

    if (DateFormat('yyyy-MM-dd').format(date) ==
        DateFormat('yyyy-MM-dd').format(DateTime.now())) {
      events = await _eventService.getEventsAfterToday();
    } else {
      events = await _eventService.getEvenementsByDate(date);
    }

    // Clean the events data by removing emojis
    final cleanedEvents = _cleanEventsData(events);

    // Update state after loading
    setState(() {
      _allEvents[date] = cleanedEvents;
      _eventDays = _allEvents.values
          .expand((e) => e)
          .map((event) => DateTime.utc(
              event.dateEvent.year, event.dateEvent.month, event.dateEvent.day))
          .toSet();
    });

    return cleanedEvents;
  }

  // Modified method that updates _eventsFuture
  void _loadEventsForSelectedDate(DateTime date) {
    setState(() {
      _eventsFuture = _loadEventsForSelectedDateAsync(date);
    });
  }

  void _goToDate() {
    final inputDate = _dateController.text;
    try {
      DateTime parsedDate = DateFormat('yyyy-MM-dd').parseStrict(inputDate);
      setState(() {
        _focusedDay = parsedDate;
        _selectedDay = parsedDate;
      });
      _loadEventsForSelectedDate(parsedDate);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
        content: Text('Format de date invalide ! Veuillez utiliser AAAA-MM-JJ'),
        backgroundColor: Colors.orange,
        behavior: SnackBarBehavior.floating,
      ));
    }
  }

  void _goToToday() {
    setState(() {
      _focusedDay = DateTime.now();
      _selectedDay = DateTime.now();
    });
    _loadEventsForSelectedDate(DateTime.now());
  }

  Future<void> _deleteEvent(int eventId) async {
    try {
      await _eventService.deleteEvenement(eventId);
      Navigator.pushNamed(context, '/directrice/Evennements');
      _loadEventsForSelectedDate(_selectedDay);
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
        content: Text('Événement supprimé avec succès.'),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ));
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Erreur lors de la suppression: $e'),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ));
    }
  }

  void _showDeleteConfirmationDialog(int eventId) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirmer la suppression'),
          content:
              const Text('Êtes-vous sûr de vouloir supprimer cet événement ?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child:
                  const Text('Annuler', style: TextStyle(color: Colors.blue)),
            ),
            TextButton(
              onPressed: () {
                _deleteEvent(eventId);
                Navigator.pushNamed(context, '/directrice/Evennements');
              },
              child:
                  const Text('Supprimer', style: TextStyle(color: Colors.pink)),
            ),
          ],
        );
      },
    );
  }

  void _showCreateEventDialog(DateTime selectedDate, {Event? existingEvent}) {
    final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
    final TextEditingController titleController = TextEditingController(
        text: existingEvent?.titre != null
            ? utf8.decode(latin1.encode(existingEvent!.titre))
            : '');
    final TextEditingController descriptionController = TextEditingController(
        text: existingEvent?.description != null
            ? utf8.decode(latin1.encode(existingEvent!.description))
            : '');
    File? selectedPhoto;
    bool isLoading = false;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setDialogState) {
            Future<void> pickImage() async {
              final picker = ImagePicker();
              final pickedFile =
                  await picker.pickImage(source: ImageSource.gallery);
              if (pickedFile != null) {
                setDialogState(() {
                  selectedPhoto = File(pickedFile.path);
                });
              }
            }

            return AlertDialog(
  titlePadding: const EdgeInsets.only(left: 24, top: 24, right: 8),
  title: Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      Text(
        existingEvent == null ? 'Créer un événement' : 'Modifier un événement',
        style: const TextStyle(color: Colors.green),
      ),
      IconButton(
        icon: const Icon(Icons.close, color: Colors.red),
        onPressed: () => Navigator.of(context).pop(),
        tooltip: 'Fermer',
      ),
    ],
  ),
    
              content: SingleChildScrollView(
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextFormField(
                        controller: titleController,
                        decoration: const InputDecoration(
                          labelText: 'Titre',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) => _validateInput(value, 'Le titre'),
                        autovalidateMode: AutovalidateMode.onUserInteraction,
                        onChanged: (value) {
                          // Trigger validation on each change
                          _formKey.currentState?.validate();
                        },
                      ),
                      const SizedBox(height: 10),
                      TextFormField(
                        controller: descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'Description',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) => _validateInput(value, 'La description'),
                        autovalidateMode: AutovalidateMode.onUserInteraction,
                        maxLines: 3,
                        onChanged: (value) {
                          // Trigger validation on each change
                          _formKey.currentState?.validate();
                        },
                      ),
                      const SizedBox(height: 10),
                      ListTile(
                        title: Text(
                          "Date de l'événement : ${DateFormat('yyyy-MM-dd').format(selectedDate)}",
                        ),
                      ),
                      ElevatedButton(
                        onPressed: pickImage,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Sélectionner une photo'),
                      ),
                      const SizedBox(height: 10),
                      if (selectedPhoto != null)
                        Container(
                          height: 200,
                          width: double.infinity,
                          child: Image.file(
                            selectedPhoto!,
                            fit: BoxFit.cover,
                          ),
                        ),
                    ],
                  ),
                ),
              ),
              actions: [
                if (isLoading)
                  const LinearProgressIndicator(color: Colors.green),
                const SizedBox(height: 10),
                TextButton(
                  onPressed:
                      isLoading ? null : () => Navigator.of(context).pop(),
                  child: isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                              color: Colors.white, strokeWidth: 2),
                        )
                      : const Text('Annuler'),
                ),
                TextButton(
                  onPressed: isLoading
                      ? null
                      : () async {
                          // Validate the form before proceeding
                          if (!_formKey.currentState!.validate()) {
                            return;
                          }

                          final String titre = titleController.text.trim();
                          final String description =
                              descriptionController.text.trim();

                          if (titre.isEmpty || description.isEmpty) {
                            ScaffoldMessenger.of(context)
                                .showSnackBar(const SnackBar(
                              content:
                                  Text('Veuillez remplir tous les champs.'),
                              backgroundColor: Colors.orange,
                              behavior: SnackBarBehavior.floating,
                            ));
                            return;
                          }

                          // Double vérification pour les emojis avant l'envoi
                          if (_validateInput(titre, 'Le titre') != null ||
                              _validateInput(description, 'La description') != null) {
                            ScaffoldMessenger.of(context)
                                .showSnackBar(const SnackBar(
                              content: Text(
                                  'Le titre et la description ne peuvent pas contenir d\'emojis ou de symboles spéciaux.'),
                              backgroundColor: Colors.red,
                              behavior: SnackBarBehavior.floating,
                            ));
                            return;
                          }

                          setDialogState(() => isLoading = true);

                          try {
                            if (existingEvent == null) {
                              final userId = await TokenService().getId();
                              await _eventService.createEvenement(
                                titre: titre,
                                description: description,
                                dateEvent: selectedDate,
                                userId: userId,
                                photo: selectedPhoto,
                              );
                            } else {
                              await _eventService.updateEvenement(
                                id: existingEvent.id,
                                titre: titre,
                                description: description,
                                dateEvent: selectedDate,
                                photo: selectedPhoto,
                              );
                            }

                            _loadEventsForSelectedDate(_selectedDay);

                            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                              content: Text(
                                existingEvent == null
                                    ? 'Événement créé avec succès.'
                                    : 'Événement modifié avec succès.',
                              ),
                              backgroundColor: Colors.green,
                              behavior: SnackBarBehavior.floating,
                            ));

                            Navigator.of(context).pop();
                          } catch (e) {
                            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                              content: Text('Erreur: $e'),
                              backgroundColor: Colors.red,
                              behavior: SnackBarBehavior.floating,
                            ));
                          } finally {
                            setDialogState(() => isLoading = false);
                          }
                        },
                  child: isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                              color: Colors.white, strokeWidth: 2),
                        )
                      : Text(existingEvent == null ? 'Créer' : 'Modifier'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget buildEventCard(Event event) {
    String encodedTitle = utf8.decode(latin1.encode(event.titre));
    String encodedDescription = utf8.decode(latin1.encode(event.description));
    String EncodedUserName = utf8.decode(latin1.encode(event.userNom!));
    String EncodedUserPrenom = utf8.decode(latin1.encode(event.userPrenom!));
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
      child: ListTile(
        leading: event.photoUrl != null &&
                Uri.tryParse(event.photoUrl!)?.isAbsolute == true
            ? Image.network(
                event.photoUrl!,
                width: 50,
                height: 50,
                fit: BoxFit.cover,
              )
            : const Icon(Icons.event, size: 50),
        title: Text(
          encodedTitle,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
            '${encodedDescription}\nDate: ${DateFormat('yyyy-MM-dd').format(event.dateEvent)}\n Créé par: ${EncodedUserName} ${EncodedUserPrenom}'),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit, color: Colors.orange),
              onPressed: () =>
                  _showCreateEventDialog(event.dateEvent, existingEvent: event),
            ),
            IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              onPressed: () => _showDeleteConfirmationDialog(event.id),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    _fabPosition = Offset(screenSize.width - 70, screenSize.height - 230);

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pushNamed(context, '/directrice/menu'),
        ),
        title: const Text(
          'Calendrier des événements',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.green[800],
        actions: [
          IconButton(
            icon: const Icon(Icons.today),
            onPressed: _goToToday,
          ),
          IconButton(
            icon: Icon(_isCalendarExpanded
                ? Icons.keyboard_arrow_up
                : Icons.keyboard_arrow_down),
            onPressed: () {
              setState(() {
                _isCalendarExpanded = !_isCalendarExpanded;
              });
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          setState(() {
            _loadEventsForSelectedDate(_selectedDay);
          });
        },
        child: Stack(
          children: [
            Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: TextField(
                    controller: _dateController,
                    decoration: const InputDecoration(
                      labelText: 'Entrez une date (AAAA-MM-JJ)',
                      border: OutlineInputBorder(),
                    ),
                    onSubmitted: (value) => _goToDate(),
                  ),
                ),
                AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  height: _isCalendarExpanded ? null : 0,
                  child: _isCalendarExpanded
                      ? TableCalendar(
                          firstDay: DateTime.utc(1000, 1, 1),
                          lastDay: DateTime.utc(9999, 1, 1),
                          focusedDay: _focusedDay,
                          selectedDayPredicate: (day) =>
                              isSameDay(_selectedDay, day),
                          locale: 'fr_FR',
                          headerStyle: HeaderStyle(
                            formatButtonVisible: false,
                            titleCentered: true,
                          ),
                          daysOfWeekStyle: DaysOfWeekStyle(
                            weekdayStyle: TextStyle(color: Colors.black),
                            weekendStyle: TextStyle(color: Colors.red),
                          ),
                          calendarStyle: CalendarStyle(
                            weekendTextStyle: TextStyle(color: Colors.red),
                            selectedDecoration: BoxDecoration(
                              color: Colors.green,
                              shape: BoxShape.circle,
                            ),
                            todayDecoration: BoxDecoration(
                              color: Colors.green.withOpacity(0.5),
                              shape: BoxShape.circle,
                            ),
                          ),
                          onDaySelected: (selectedDay, focusedDay) {
                            setState(() {
                              _selectedDay = selectedDay;
                              _focusedDay = focusedDay;
                            });
                            _loadEventsForSelectedDate(selectedDay);
                          },
                          calendarBuilders: CalendarBuilders(
                            markerBuilder: (context, date, events) {
                              if (_eventDays.contains(date)) {
                                return Positioned(
                                  bottom: 1,
                                  right: 1,
                                  child: Icon(
                                    Icons.circle,
                                    color: Colors.green,
                                    size: 8,
                                  ),
                                );
                              }
                              return SizedBox.shrink();
                            },
                          ),
                        )
                      : SizedBox.shrink(),
                ),
                Expanded(
                  child: FutureBuilder<List<Event>>(
                    future: _eventsFuture,
                    builder: (BuildContext context,
                        AsyncSnapshot<List<Event>> snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return const Center(child: CircularProgressIndicator());
                      } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                        return const Center(
                          child: Text('Aucun événement trouvé pour cette date'),
                        );
                      } else {
                        return ListView.builder(
                          controller: _scrollController,
                          itemCount: snapshot.data!.length,
                          itemBuilder: (context, index) =>
                              buildEventCard(snapshot.data![index]),
                        );
                      }
                    },
                  ),
                )
              ],
            ),
            Positioned(
              left: _fabPosition.dx,
              top: _fabPosition.dy,
              child: _selectedDay.isBefore(DateTime(
                DateTime.now().year,
                DateTime.now().month,
                DateTime.now().day,
              ))
                  ? const SizedBox.shrink() // Hide button for past dates
                  : FloatingActionButton(
                      onPressed: () => _showCreateEventDialog(_selectedDay),
                      backgroundColor: Colors.green,
                      child: const Icon(Icons.add),
                    ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: const MyFooter(currentRoute: '/directrice/menu'),
    );
  }
}
