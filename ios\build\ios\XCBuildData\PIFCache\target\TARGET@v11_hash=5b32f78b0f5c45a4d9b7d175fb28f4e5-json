{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9899efd78a98bad45eb5d77c466368c1ea", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98402b102000224a23e5f204b16a770629", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9842a194ca0e429b4447ab716381127f5b", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98659cc9da24839b0e948619326b3e63c4", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9842a194ca0e429b4447ab716381127f5b", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e4a5d79e4af461664bbf5f9f9fe742c9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989ee558338a5f89155a1982312cecb394", "guid": "bfdfe7dc352907fc980b868725387e982e76db223babac7b1fd89abac5fc4ae0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98625c8ff9a4e834c5df779f4436e3f822", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98577d46ea4035dc867595113e9539f4bc", "guid": "bfdfe7dc352907fc980b868725387e982fc5f2effb67cc62d10cfc9b858fc2df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826d984f0d8c0f8f45d88dd8dadcc4a18", "guid": "bfdfe7dc352907fc980b868725387e985d8011bc696b0aa6cba7a3fd4c3276a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828c14ed8ac764a1093f4ffa005ece07b", "guid": "bfdfe7dc352907fc980b868725387e9876c6bf01ef58208d68ab0a0ee635d335"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882158c7dc41922d5b7436355c4bf4020", "guid": "bfdfe7dc352907fc980b868725387e983f23118f7f6831b28570daad2a4d5bef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989881337585a5af16b657ed36fd207933", "guid": "bfdfe7dc352907fc980b868725387e98ff56ddc95e9de441b9c0b4f2862b130c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811a99547732891b246d72b645cb91bc4", "guid": "bfdfe7dc352907fc980b868725387e98686ece793deff377907aaa4494081a39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfa8be08b353a451858ff0a290a7d449", "guid": "bfdfe7dc352907fc980b868725387e9883cfc210d4e96cfd52fb5e580992ffb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881ae8f8e1a4042bc59520942d0fb4f45", "guid": "bfdfe7dc352907fc980b868725387e98110bc6cf6364cb032c5baafe77b2dd71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801830033681bf4e72f78f84992737709", "guid": "bfdfe7dc352907fc980b868725387e981e34d346e2a98ba59101cf6a78c940b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7d9abeb882e31436917dac56cb45e48", "guid": "bfdfe7dc352907fc980b868725387e98962e9d1272fd5052850ffdacedf7629f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868349f990bcb27fd519a354aec7a4866", "guid": "bfdfe7dc352907fc980b868725387e9823c7d6da0ebd9514fd8c15c055b0de26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8d5269e7f9c5e224840b7852409ef7b", "guid": "bfdfe7dc352907fc980b868725387e98a785f1b4b310bb0b5973fb27fbdc24cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e43e08971b1ce62a0fa180ba130f194a", "guid": "bfdfe7dc352907fc980b868725387e98bb3cefba185a4745f4840c8e526e573c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3bc6b9dc66754a71a572c2facb46edc", "guid": "bfdfe7dc352907fc980b868725387e98db3c821391aab1dece83a712f1d02fff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5fd30affc8200e45c1fee5be6e6e127", "guid": "bfdfe7dc352907fc980b868725387e98a5626cb6b38f500722d45426efdfc4c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804723ec06aac58df43649d58340e18a1", "guid": "bfdfe7dc352907fc980b868725387e981aa0de24bd6a5dd1875e74feeab55633"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ef1bf2c37fd55f8a97c21a2c3a9d5bc", "guid": "bfdfe7dc352907fc980b868725387e988305696f1c4b035633eedbc754f2c1a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cdbcac2958e6b7d201361ef82029a74", "guid": "bfdfe7dc352907fc980b868725387e982fa9a5c1425f006a4783bdcd8c853f1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98341a714879d84baba5cb1ec46f6d482e", "guid": "bfdfe7dc352907fc980b868725387e98da1ff3fdbe7c4fce38e401db964eace5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab3c87d336979de4cddc6570a91e02e3", "guid": "bfdfe7dc352907fc980b868725387e98571b51342975daac155c8685990eb0cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820fb4501b5086c69c64204dff746ad9a", "guid": "bfdfe7dc352907fc980b868725387e988fb3246646040525ccaeb77b081c19f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf0a7d7b6a8f76b5af0abcf7abfd8f45", "guid": "bfdfe7dc352907fc980b868725387e984629e2b7e070d1330cce74db6ee43080"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854f9620385ef1310c616e0e148727629", "guid": "bfdfe7dc352907fc980b868725387e98d0168df7252dee0f79ea189d0952e4bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ac1395601aa53da488ffeb51535d5df", "guid": "bfdfe7dc352907fc980b868725387e98021eba2abeb56ab768dddac4e6130063"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e1d9254c28a0547a01153c5c121ccf0", "guid": "bfdfe7dc352907fc980b868725387e98bd84ff7e2d20448409adcb8e0ad07468"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984364bd58f8cc42524c19528938b80ec7", "guid": "bfdfe7dc352907fc980b868725387e98cc6049af2aae0f84b02a7e378b2f3500"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ddc0769b860f81dfaae66cd0c4aef76", "guid": "bfdfe7dc352907fc980b868725387e988e221d08aee1819f90d7193739d6c2a6"}], "guid": "bfdfe7dc352907fc980b868725387e983dd9ecf514a8a7357d998ef175b2f275", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98709db05cbcba8a38b5fff878acd3dc96", "guid": "bfdfe7dc352907fc980b868725387e983fe27036fd6365756c76949d858b8e9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986296ed536311979929249249546219ab", "guid": "bfdfe7dc352907fc980b868725387e989a748a345c1474079262693cf146781e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e98dcf7808112b032c42e5da5aac97c04d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cee0e87b873ad96ad0a06a562ccbc5f5", "guid": "bfdfe7dc352907fc980b868725387e98de181a566dcb03c3cb74e2bbc6fcce9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f04dcb6e648519c233ae8161727f1d09", "guid": "bfdfe7dc352907fc980b868725387e98b227e23318e085d41b2628413887a94c"}], "guid": "bfdfe7dc352907fc980b868725387e98f61fcf39da37817abbea7120651334c6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d9f5ffc4dd9844546434e02bc9cebada", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e98401c721eb5128ee0590bc98893fd51b4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}