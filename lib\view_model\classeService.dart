// ignore_for_file: file_names

import 'dart:convert';

import 'package:bee_kids_mobile/model/classe.dart';
import 'package:bee_kids_mobile/model/eleve.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../resources/environnement/apiUrl.dart';
import '../view_model/tokenService.dart';

class classeService {
  final baseUrl = ApiUrl.baseUrl;
  late final TokenService tokenService = TokenService();
  
  // Get headers with Authorization token
  Future<Map<String, String>> _getAuthHeaders() async {
    final token = await tokenService.getToken();
    if (token != null) {
      return {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };
    }
    return {
      'Content-Type': 'application/json',
    };
  }


    Future<List<Classe>> getClassesByEducateurId() async {
    final headers = await _getAuthHeaders();
    int? educateurId = await tokenService.getId();
     try {
      final response = await http.get(Uri.parse('${baseUrl}api/classes/formateur/$educateurId'),headers: headers,);
      final decodedData = utf8.decode(response.bodyBytes);
      final responseStatusCode = response.statusCode ;
      if (response.statusCode == 200) {
        // Parse the JSON response into a list of Eleve objects
        List<dynamic> data = json.decode(decodedData);

        List<Classe> classes = data.map((item) => Classe.fromJson(item)).toList();
       // print ('LISTE DES ELEVES getAllClasses getAllClasses : $data');
        return classes;
    } else {
      throw Exception('Failed to load students getAllClasses $responseStatusCode');
    }
  } catch (e) {
    //print('Error fetching students getAllClasses: $e'); 
    throw Exception('Error fetching students getAllClasses: $e');
  }
}

  Future<List<Classe>> getAllClasses() async {
    final headers = await _getAuthHeaders();
     try {
      final response = await http.get(Uri.parse('${baseUrl}api/classes'),headers: headers,);
      final decodedData = utf8.decode(response.bodyBytes);
      final responseStatusCode = response.statusCode ;
      if (response.statusCode == 200) {
        // Parse the JSON response into a list of Eleve objects
        List<dynamic> data = json.decode(decodedData);

        List<Classe> classes = data.map((item) => Classe.fromJson(item)).toList();
       // print ('LISTE DES ELEVES getAllClasses getAllClasses : $data');
        return classes;
    } else {
      throw Exception('Failed to load students getAllClasses $responseStatusCode');
    }
  } catch (e) {
    //print('Error fetching students getAllClasses: $e'); 
    throw Exception('Error fetching students getAllClasses: $e');
  }
}

// Fetch Eleves by ClasseId
  Future<List<Eleve>> getElevesByClasseId(int classeId) async {
    final headers = await _getAuthHeaders();
    try {
      final url = Uri.parse('${baseUrl}api/classes/$classeId/eleves');
      final response = await http.get((url),headers: headers);
     // print ('Classe id :=====> $response');
      if (response.statusCode == 200) {
        List<dynamic> data = json.decode(response.body);
        List<Eleve> eleves = data.map((item) => Eleve.fromMap(item)).toList();
       // print('classData : ===> $data');
 
        return eleves;
      } else {
        throw Exception('Failed to load eleves for classeId: $classeId');
      }
    } catch (e) {
     // print('Error fetching eleves for classeId $classeId: $e');
      throw Exception('Error fetching eleves: $e');
    }
  }

 Future<List<Classe>> getClassesByEleveId(int eleveId) async {
    final headers = await _getAuthHeaders();
    try {
        final url = Uri.parse('${baseUrl}api/classes/eleve/$eleveId');
        final response = await http.get(url, headers: headers);

        debugPrint('Réponse de l\'API : ${response.statusCode}');

        if (response.statusCode == 200) {
            List<dynamic> data = json.decode(response.body);
            List<Classe> classes = data.map((item) => Classe.fromJson(item)).toList();
            return classes;
        } else {
            throw Exception('Failed to load classes for eleveId: $eleveId. Status: ${response.statusCode}');
        }
    } catch (e) {
        throw Exception('Error fetching classes: $e');
    }
}



Future<void> postEmploiByClasseId(
    
      int id, String name, String filePath) async {
            final headers = await _getAuthHeaders();

    // Encoder l'ID de la classe pour éviter les erreurs d'encodage
//    final encodedClassId = Uri.encodeComponent(classId);
    // int? id = classId as int;

    // print('id string : $classId');
    print('id int : $id');
    final url = Uri.parse('${baseUrl}api/emplois/create/$id');

    try {
      // Préparer la requête multipart
      final request = http.MultipartRequest('POST', url)
        ..headers.addAll(headers)
        ..fields['name'] = name;

      // Ajouter le fichier
      if (filePath.isNotEmpty) {
        request.files.add(await http.MultipartFile.fromPath('file', filePath));
      }

      final response = await request.send();

      // Vérification de la réponse
      if (response.statusCode != 200) {
        final responseBody = await response.stream.bytesToString();
        throw Exception('Erreur API : $responseBody');
      }
    } catch (e) {
      print('Erreur dans postEmploiByClasseId : $e');
      rethrow;
    }
  }

      ////////////// Tableau de bord ////////////

  Future<int> getNbTotalesClasse() async {
  final url = Uri.parse('${baseUrl}api/classes/NbTotalesClasse');
  final headers = await _getAuthHeaders();

  final response = await http.get(url, headers: headers);

  if (response.statusCode == 200) {
    final responseBody = response.body;
    print('get Nombre total classe: $responseBody');

    final intValue = int.tryParse(responseBody.trim());
    if (intValue != null) {
      return intValue;
    } else {
      throw Exception("Réponse inattendue : $responseBody");
    }
  } else {
    print('get Nombre total classe Error: ${response.statusCode}');
    print('get Nombre total classe Error: ${response.body}');
    throw Exception('Erreur API: ${response.statusCode}');
  }
}

}

