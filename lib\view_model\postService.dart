import 'dart:convert';
import 'dart:math';
import 'dart:io';
import 'package:bee_kids_mobile/model/Comment.dart';
import 'package:bee_kids_mobile/model/Like.dart'; // Import ajouté pour UserLikeDTO
import 'package:bee_kids_mobile/model/Post.dart';
import 'package:bee_kids_mobile/model/userInteraction.dart';
import 'package:bee_kids_mobile/view_model/tokenService.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as p; // Ajout de ce package
import 'package:http_parser/http_parser.dart';
import '../resources/environnement/apiUrl.dart';

// Modèle pour les posts ayant le plus d'interactions
class PostAvecInteractions {
  final int id;
  final String content;
  final List<String> photoUrl;
  final int userId;
  final int likeCount;
  final bool approved;
  final String authorName;
  final String? userPhotoUrl;
  final String createdAt;
  final int totalLikes;
  final int totalCommentaires;
  final int totalInteractions;

  PostAvecInteractions({
    required this.id,
    required this.content,
    required this.photoUrl,
    required this.userId,
    required this.likeCount,
    required this.approved,
    required this.authorName,
    this.userPhotoUrl,
    required this.createdAt,
    required this.totalLikes,
    required this.totalCommentaires,
    required this.totalInteractions,
  });

  factory PostAvecInteractions.fromMap(Map<String, dynamic> map) {
    return PostAvecInteractions(
      id: map['id'] ?? 0,
      content: map['content'] ?? '',
      photoUrl: map['photoUrl'] != null
          ? (map['photoUrl'] is List
              ? List<String>.from(map['photoUrl'])
              : [map['photoUrl'].toString()])
          : [],
      userId: map['userId'] ?? 0,
      likeCount: map['likeCount'] ?? map['totalLikes'] ?? 0,
      approved: map['approved'] ?? false,
      authorName: map['authorName'] ?? '',
      userPhotoUrl: map['userPhotoUrl'] ?? map['authorPhotoUrl'],
      createdAt: map['createdAt'] ?? '',
      totalLikes: map['totalLikes'] ?? 0,
      totalCommentaires: map['totalCommentaires'] ?? 0,
      totalInteractions: map['totalInteractions'] ?? 0,
    );
  }

  @override
  String toString() {
    return 'PostAvecInteractions{id: $id, authorName: $authorName, totalInteractions: $totalInteractions}';
  }
}

// Fonction pour récupérer le type MIME en fonction de l'extension de fichier
Map<String, String> getMimeType(String filePath) {
  final extension = p.extension(filePath).toLowerCase();
  switch (extension) {
    case '.jpg':
    case '.jpeg':
      return {'type': 'image', 'subtype': 'jpeg'};
    case '.png':
      return {'type': 'image', 'subtype': 'png'};
    case '.gif':
      return {'type': 'image', 'subtype': 'gif'};
    case '.mp4':
      return {'type': 'video', 'subtype': 'mp4'};
    case '.pdf':
      return {'type': 'application', 'subtype': 'pdf'};
    default:
      return {
        'type': 'application',
        'subtype': 'octet-stream'
      }; // Par défaut, fichier binaire
  }
}

class PostService {
  Future<Map<String, String>> _getAuthHeaders() async {
    final token = await tokenService.getToken();
    return token != null
        ? {'Authorization': 'Bearer $token', 'Content-Type': 'application/json'}
        : {'Content-Type': 'application/json'};
  }

  final baseUrl = ApiUrl.baseUrl;
  final TokenService tokenService = TokenService();

// Fonction pour ajouter une publication
  Future<void> addPost(
      int userId, String content, List<http.MultipartFile> files) async {
    final headers = await _getAuthHeaders();
    final request = http.MultipartRequest(
        'POST', Uri.parse('${baseUrl}api/posts/create/$userId'));
    request.headers.addAll(headers);
    request.fields['user_id'] = userId.toString();
    request.fields['content'] = content;

    // Ajoute tous les fichiers à la requête
    request.files.addAll(files);

    var response = await request.send();

    if (response.statusCode == 200) {
      print('Publication réussie');
    } else {
      print('Erreur lors de la publication : ${response.statusCode}');
    }
  }

// Method to delete a post
  Future<void> deletePost(int postId) async {
    final headers = await _getAuthHeaders();
    final response = await http.delete(
      Uri.parse('${baseUrl}api/posts/$postId'),
      headers: headers,
    );

    if (response.statusCode == 200 || response.statusCode == 204) {
      print('Post deleted successfully');
    } else {
      throw Exception('Failed to delete post: ${response.statusCode}');
    }
  }

// Method to update a post
  Future<void> updatePost(
      int postId, String content, List<http.MultipartFile> files) async {
    final headers = await _getAuthHeaders();
    final request = http.MultipartRequest(
      'PUT',
      Uri.parse('${baseUrl}api/posts/posts/$postId/update'),
    );
    request.headers.addAll(headers);
    request.fields['content'] = content;

    // Add the files to the request
    request.files.addAll(files);

    var response = await request.send();

    if (response.statusCode == 200) {
      print('Post updated successfully');
    } else {
      throw Exception('Failed to update post: ${response.statusCode}');
    }
  }

// Function to fetch all approoved posts (élèves)
  Future<List<Post>> getAllApprovedPost() async {
    final headers = await _getAuthHeaders();
    final response = await http.get(Uri.parse('${baseUrl}api/posts/approved'),
        headers: headers);
    final decodedData = utf8.decode(response.bodyBytes);

    if (response.statusCode == 200) {
      List<dynamic> data = json.decode(decodedData);
      return data.map((json) => Post.fromJson(json)).toList();
    } else {
      throw Exception('Failed to load posts');
    }
  }

  Future<bool> isPostLiked(int postId, int userId) async {
    final headers = await _getAuthHeaders();
    final response = await http.get(
      Uri.parse('${baseUrl}api/likes/post/$postId'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      List<dynamic> likes = jsonDecode(response.body);
      return likes
          .any((like) => like["userId"] == userId && like["liked"] == true);
    } else {
      throw Exception('Failed to check like status');
    }
  }

  /// **Get detailed list of users who liked the post with photos**
  Future<List<UserLikeDTO>> getDetailedLikedUsers(int postId) async {
    final headers = await _getAuthHeaders();
    final response = await http.get(
      Uri.parse('${baseUrl}api/likes/post/$postId/liked-users'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      final decodedData = utf8.decode(response.bodyBytes);
      List<dynamic> data = json.decode(decodedData);
    
      List<UserLikeDTO> users = data.map((json) => UserLikeDTO.fromJson(json)).toList();
    
      // Si les photos ne sont pas dans la réponse, les récupérer individuellement
      for (var user in users) {
        if (user.userPhotoUrl == null || user.userPhotoUrl!.isEmpty) {
          try {
            String photoUrl = await _getUserPhotoById(user.userId);
            // Créer un nouvel objet avec la photo
            int index = users.indexOf(user);
            users[index] = UserLikeDTO(
              userId: user.userId,
              nom: user.nom,
              prenom: user.prenom,
              userEmail: user.userEmail,
              userPhoneNumber: user.userPhoneNumber,
              userPhotoUrl: photoUrl,
            );
          } catch (e) {
            print('Error fetching photo for user ${user.userId}: $e');
          }
        }
      }
    
      return users;
    } else {
      throw Exception('Failed to fetch detailed liked users');
    }
  }

  /// **Get user photo by ID**
  Future<String> _getUserPhotoById(int userId) async {
    final headers = await _getAuthHeaders();
    final response = await http.get(
      Uri.parse('${baseUrl}api/users/$userId/photo'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      final decodedData = utf8.decode(response.bodyBytes);
      final data = json.decode(decodedData);
      return data['photoUrl'] ?? '';
    } else {
      return ''; // Retourner une chaîne vide si pas de photo
    }
  }

  /// **Get detailed list of users who liked the post and fetch their photos**
  Future<List<UserLikeDTO>> getDetailedLikedUsersWithPhotos(int postId) async {
    // D'abord récupérer la liste des utilisateurs
    List<UserLikeDTO> users = await getDetailedLikedUsers(postId);
    
    // Ensuite récupérer les photos pour chaque utilisateur
    for (int i = 0; i < users.length; i++) {
      if (users[i].userPhotoUrl == null || users[i].userPhotoUrl!.isEmpty) {
        try {
          String? photoUrl = await _getUserPhoto(users[i].userId);
          // Créer un nouvel objet avec la photo
          users[i] = UserLikeDTO(
            userId: users[i].userId,
            nom: users[i].nom,
            prenom: users[i].prenom,
            userEmail: users[i].userEmail,
            userPhoneNumber: users[i].userPhoneNumber,
            userPhotoUrl: photoUrl,
          );
        } catch (e) {
          print('Error fetching photo for user ${users[i].userId}: $e');
        }
      }
    }
    
    return users;
  }
  /// **Get user photo by ID**
  Future<String?> _getUserPhoto(int userId) async {
    try {
      final headers = await _getAuthHeaders();
      final response = await http.get(
        Uri.parse('${baseUrl}api/users/$userId/photo'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final decodedData = utf8.decode(response.bodyBytes);
        final data = json.decode(decodedData);
        return data['photoUrl'] ?? data['userPhotoUrl'];
      }
    } catch (e) {
      print('Error fetching user photo: $e');
    }
    return null;
  }

  /// **Get list of users who liked the post (simple version)**
  Future<List<Map<String, dynamic>>> getLikedUsers(int postId) async {
    final headers = await _getAuthHeaders();
    final response = await http.get(
      Uri.parse('${baseUrl}api/likes/post/$postId/liked-users'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      final decodedData = utf8.decode(response.bodyBytes);
      return List<Map<String, dynamic>>.from(json.decode(decodedData));
    } else {
      throw Exception('Failed to fetch liked users');
    }
  }

// New function to fetch like count for a post
  Future<int> getLikeCountForPost(int postId) async {
    final headers = await _getAuthHeaders();
    final response = await http.get(
        Uri.parse('${baseUrl}api/likes/post/$postId/count'),
        headers: headers);
    if (response.statusCode == 200) {
      return int.parse(response.body);
    } else {
      throw Exception('Failed to fetch like count');
    }
  }

// Function to toggle like for a post
  Future<void> toggleLike(int postId, int userId) async {
    final headers = await _getAuthHeaders();
    try {
      final response = await http.post(
        Uri.parse('${baseUrl}api/likes/toggle/$postId/$userId'),
        headers: headers,
      );

      print('Raw response: ${response.body}');

      if (response.statusCode != 200) {
        throw Exception('Failed to toggle like');
      }
    } catch (e) {
      print('Error toggling like: $e');
      throw Exception('Error toggling like: $e');
    }
  }

  /// New Method: Fetch comments for a specific post (Supports Arabic)
  Future<List<Comment>> getCommentsForPost(int postId) async {
    final headers = await _getAuthHeaders();
    final response = await http.get(
      Uri.parse('${baseUrl}api/commentaires/post/$postId'),
      headers: headers,
    );

    // Decode response to support Arabic characters
    final decodedData = utf8.decode(response.bodyBytes);

    if (response.statusCode == 200) {
      List<dynamic> data =
          json.decode(decodedData); // Use decodedData instead of response.body
      return data.map((json) => Comment.fromJson(json)).toList();
    } else {
      throw Exception('Failed to load comments');
    }
  }

  /// Update a comment
  Future<void> updateComment(
      int commentId, String updatedContent, int userId) async {
    final headers = await _getAuthHeaders();

    final response = await http.put(
      Uri.parse(
          '${baseUrl}api/commentaires/comments/$commentId?updatedContent=$updatedContent&userId=$userId'),
      headers: headers,
    );
    // Log the response for debugging
    print('Response status: ${response.statusCode}');
    print('Response body: ${response.body}');
    final decodedData = utf8.decode(response.bodyBytes);
    if (response.statusCode != 200) {
      throw Exception('Failed to update comment');
    }
  }

  /// Delete a comment
  Future<void> deleteComment(int commentId, int userId) async {
    final headers = await _getAuthHeaders();
    final response = await http.delete(
      Uri.parse('${baseUrl}api/commentaires/$commentId?userId=$userId'),
      headers: headers,
    );

    if (response.statusCode != 200 && response.statusCode != 204) {
      throw Exception('Failed to delete comment');
    }
  }

  /// method to add a comment
  Future<Comment> addComment(int postId, int userId, String content) async {
    final headers = await _getAuthHeaders();
    final response = await http.post(
      Uri.parse(
          '${baseUrl}api/commentaires/add?postId=$postId&userId=$userId&content=$content'),
      headers: headers,
    );

    final decodedData =
        utf8.decode(response.bodyBytes); // Ensure UTF-8 decoding

    if (response.statusCode == 200) {
      final Map<String, dynamic> data =
          json.decode(decodedData); // Use decodedData
      return Comment.fromJson(data);
    } else {
      throw Exception(
          'Failed to add comment: $decodedData'); // Include response in error
    }
  }

  /// New function to fetch comment count for a post
  Future<int> getCommentCountForPost(int postId) async {
    final headers = await _getAuthHeaders();
    final response = await http.get(
        Uri.parse('${baseUrl}api/commentaires/count?postId=$postId'),
        headers: headers);

    if (response.statusCode == 200) {
      return int.parse(response.body);
    } else {
      throw Exception('Failed to fetch comment count');
    }
  }

  Future<List<Post>> getPaginatedPosts(int page, int size) async {
    final headers = await _getAuthHeaders();
    final response = await Dio().get(
      '${baseUrl}api/posts/approved/latest',
      queryParameters: {'page': page, 'size': size},
      options: Options(headers: headers),
    );
    if (response.statusCode == 200) {
      final List<dynamic> postList = response.data['content'];
      return postList.map((json) => Post.fromJson(json)).toList();
    } else {
      throw Exception('Failed to load posts');
    }
  }

  Future<Map<String, int>> countPendingPosts() async {
    try {
      List<Post> posts = await getPendingPosts();
      return {'countPendingPosts': posts.length};
    } catch (e) {
      throw Exception("Failed to count PendingPosts: $e");
    }
  }

  Future<List<Post>> getPendingPosts() async {
    final headers = await _getAuthHeaders();
    final response = await Dio().get(
      '${baseUrl}api/posts/pending',
      options: Options(headers: headers),
    );

    if (response.statusCode == 200) {
      final List<dynamic> postList = response.data;
      return postList.map((json) => Post.fromJson(json)).toList();
    } else {
      throw Exception('Failed to fetch pending posts');
    }
  }

  // New method to approve a post
  Future<void> approvePost(int postId) async {
    final headers = await _getAuthHeaders();
    final response = await http.put(
      Uri.parse('${baseUrl}api/posts/approve/$postId'),
      headers: headers,
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to approve post');
    }
  }

  // New method to reject a post
  Future<void> rejectPost(int postId) async {
    final headers = await _getAuthHeaders();
    final response = await http.delete(
      Uri.parse('${baseUrl}api/posts/$postId'),
      headers: headers,
    );

    if (response.statusCode != 204) {
      throw Exception('Failed to reject post');
    }
  }

  Future<Post> getPostById(int postId) async {
    final headers = await _getAuthHeaders();
    final response = await http.get(
      Uri.parse('${baseUrl}api/posts/$postId'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      return Post.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Failed to fetch post');
    }
  }

  // Cache pour les photos des utilisateurs
  Map<int, String> _userPhotosCache = {};

  /// **Get user photo with cache**
  Future<String> getUserPhotoWithCache(int userId) async {
    // Vérifier le cache d'abord
    if (_userPhotosCache.containsKey(userId)) {
      return _userPhotosCache[userId]!;
    }

    try {
      String photoUrl = await _getUserPhotoById(userId);
      _userPhotosCache[userId] = photoUrl; // Mettre en cache
      return photoUrl;
    } catch (e) {
      _userPhotosCache[userId] = ''; // Mettre en cache même si vide
      return '';
    }
  }

  /// New method to fetch paginated comments for a specific post (Supports Arabic)
  Future<List<Comment>> getPaginatedCommentsForPost(int postId, int page, int size) async {
    final headers = await _getAuthHeaders();
    final response = await http.get(
      Uri.parse('${baseUrl}api/commentaires/post/$postId/paginated?page=$page&size=$size'),
      headers: headers,
    );

    // Decode response to support Arabic characters
    final decodedData = utf8.decode(response.bodyBytes);

    if (response.statusCode == 200) {
      List<dynamic> data = json.decode(decodedData);
      return data.map((json) => Comment.fromJson(json)).toList();
    } else {
      throw Exception('Failed to load paginated comments');
    }
  }
    ////////////// Tableau de bord ////////////
    
    Future<List<InteractionUser>> getTopInteractions() async {
  final headers = await _getAuthHeaders();
    final response = await http.get(
      Uri.parse('${baseUrl}api/posts/users/top-interactions'),
      headers: headers,
    );

  if (response.statusCode == 200) {
    final List<dynamic> jsonData = json.decode(response.body);
    print('top interactions: $jsonData');
    return jsonData.map((item) => InteractionUser.fromJson(item)).toList();
  } else {
    throw Exception('Failed to load top interactions');
  }
}

 Future<int> getNombrePostsTotal() async {
  final url = Uri.parse('${baseUrl}api/posts/posts/total');
  final headers = await _getAuthHeaders();

  final response = await http.get(url, headers: headers);

  if (response.statusCode == 200) {
    final responseBody = response.body;
    print('get Nombre Posts total: $responseBody');

    final intValue = int.tryParse(responseBody.trim());
    if (intValue != null) {
      return intValue;
    } else {
      throw Exception("Réponse inattendue : $responseBody");
    }
  } else {
    print('get Nombre Posts total Error: ${response.statusCode}');
    print('get Nombre Posts total Error: ${response.body}');
    throw Exception('Erreur API: ${response.statusCode}');
  }
}

// Méthode pour récupérer les posts ayant le plus d'interactions
// Fonctionne que l'API retourne un seul post ou une liste de posts
Future<List<PostAvecInteractions>> getPostsAvecPlusInteractions() async {
  final url = Uri.parse('${baseUrl}api/posts/top-interaction');
  final headers = await _getAuthHeaders();

  try {
    final response = await http.get(url, headers: headers);

    if (response.statusCode == 200) {
      final decodedData = utf8.decode(response.bodyBytes);
      final dynamic jsonResponse = json.decode(decodedData);

      print('📡 Raw API Response: $jsonResponse');
      print('🔍 Response type: ${jsonResponse.runtimeType}');

      List<PostAvecInteractions> postsAvecInteractionsList = [];

      if (jsonResponse is List) {
        // CAS 1: L'API retourne une liste de posts
        print('📋 API retourne une liste de ${jsonResponse.length} post(s)');

        for (var item in jsonResponse) {
          if (item is Map<String, dynamic>) {
            try {
              final post = PostAvecInteractions.fromMap(item);
              postsAvecInteractionsList.add(post);
              print('✅ Post ajouté: ${post.authorName} (${post.totalInteractions} interactions)');
            } catch (e) {
              print('⚠️ Erreur parsing post: $e');
              print('📄 Données du post problématique: $item');
            }
          }
        }

      } else if (jsonResponse is Map<String, dynamic>) {
        // CAS 2: L'API retourne un seul post
        print('📄 API retourne un seul post');

        try {
          final post = PostAvecInteractions.fromMap(jsonResponse);
          postsAvecInteractionsList.add(post);
          print('✅ Post unique ajouté: ${post.authorName} (${post.totalInteractions} interactions)');
        } catch (e) {
          print('⚠️ Erreur parsing post unique: $e');
          print('📄 Données du post: $jsonResponse');
        }

      } else {
        // CAS 3: Format inattendu
        print('❌ Format de réponse non supporté: ${jsonResponse.runtimeType}');
        print('📄 Contenu: $jsonResponse');
        return [];
      }

      print("🎯 RÉSULTAT: ${postsAvecInteractionsList.length} post(s) avec plus d'interactions récupéré(s)");

      // Trier par nombre d'interactions décroissant
      postsAvecInteractionsList.sort((a, b) => b.totalInteractions.compareTo(a.totalInteractions));

      return postsAvecInteractionsList;

    } else {
      print("❌ Erreur HTTP ${response.statusCode} lors de la récupération des posts avec plus d'interactions");
      print("📄 Réponse: ${response.body}");
      return [];
    }

  } catch (e) {
    print('💥 Erreur lors de la récupération des posts avec plus d\'interactions: $e');
    print('🔍 Détails de l\'erreur: ${e.toString()}');
    if (e is FormatException) {
      print('📄 Erreur de format JSON - vérifiez la réponse de l\'API');
    }
    return [];
  }
}



}