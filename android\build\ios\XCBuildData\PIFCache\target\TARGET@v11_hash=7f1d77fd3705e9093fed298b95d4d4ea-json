{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98037da76e96d2f38aee12c3f5c3695a49", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984056b56ab63187e646b06f3cb56c7908", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983586a3e841deb3cfe6a3d067f8918979", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a9dd23bcdd71d5a828d50d5f4eca95fe", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983586a3e841deb3cfe6a3d067f8918979", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Desktop/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Desktop/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987ff122a591fe378bcab6c1be15b8394a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983ccb14a59b4ae57786ea6a2d93ef95bc", "guid": "bfdfe7dc352907fc980b868725387e984543c3063ad52835ceb2ff24817fdae2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863bf917e45c87910fc6d3ae9fa1c08fc", "guid": "bfdfe7dc352907fc980b868725387e980847f5816b9357381ecbe4c04b754afc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f75c03ce59a23509645cf5b551ce9322", "guid": "bfdfe7dc352907fc980b868725387e98165ee53aa743e4c71b6ad2c8f46e5cc3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98497e49981e08c199f379072787523072", "guid": "bfdfe7dc352907fc980b868725387e98fba4daef4d87ffada22d55cd581d80a9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc92773ecbcf422a101f363516211740", "guid": "bfdfe7dc352907fc980b868725387e98fcf978ca3e48ce08ae9ec6a655b2ff7d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887602a8de5350b2ac24512312b64d97b", "guid": "bfdfe7dc352907fc980b868725387e98de13b88dc42213e089cb971aa1061109", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802b92a5c0d8e78e2fd0c20c96b9d5702", "guid": "bfdfe7dc352907fc980b868725387e983610e8b3659b4062ecaab09de182637f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edfa82c00d6ea671be0d43d3452a156c", "guid": "bfdfe7dc352907fc980b868725387e98c9f5222e1485d46fbcf89299fa7d24e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98740f39d397ecd19d6497b06001685ecd", "guid": "bfdfe7dc352907fc980b868725387e9817af44cd682dd4457b369801f348bf82", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814c477d5e4089ec4d610690aac5a80c5", "guid": "bfdfe7dc352907fc980b868725387e98cad4d40ef398102e1a84157603cf4617", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b9f286e72c7029b53862cbceaefb379f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98488fdf9dda64645610a82e8fa520dfcf", "guid": "bfdfe7dc352907fc980b868725387e98d85c1e47e19a6b2e344f54a8a976a821"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98452d816aa01fd92d64e435f318f02e36", "guid": "bfdfe7dc352907fc980b868725387e9859f671b21e271ba45ffb91e60dfcf3ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f925d2da073ced935c33ef32a04e374", "guid": "bfdfe7dc352907fc980b868725387e98904d6e47be6a0f2358c1d5dfdf1e1e75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f7c8ba7e823d7efc6b4018c8f540b50", "guid": "bfdfe7dc352907fc980b868725387e9885469c8896c5cf1f90f1e8170625066d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989635378269d4c92970622326100d0649", "guid": "bfdfe7dc352907fc980b868725387e9858c4439af7ae96ec068721062e1e7217"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf51e6bb1bcc6adb5248de81fabbbac9", "guid": "bfdfe7dc352907fc980b868725387e98fa6861c4ed378d34183250c672ea2c58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb1117c9f743ca498eb35ef80bd48289", "guid": "bfdfe7dc352907fc980b868725387e9860f0a4e675cfd78b273b17e9688f7bb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98399bd90a8cdff90318186fa6f4a00cd9", "guid": "bfdfe7dc352907fc980b868725387e98a8bf092a35fe7df1d61457b739639bcb"}], "guid": "bfdfe7dc352907fc980b868725387e9835890100e1278d4912a0a960bc1e1798", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d56474ef5850c087d24d24a69093581f", "guid": "bfdfe7dc352907fc980b868725387e989eb0b9349eae5cdf5963230d4af39bac"}], "guid": "bfdfe7dc352907fc980b868725387e98e62b94e67497d0d9d86e7bbf1288e19b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9837f145fa470c584eb77faec656d2013a", "targetReference": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04"}], "guid": "bfdfe7dc352907fc980b868725387e9839f52b65e3be06571da83c9298e2891a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04", "name": "video_player_avfoundation-video_player_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988a0a5b40b007f81bee1472e4d0fb23da", "name": "video_player_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b5f237537920ce49888f6f7f73c80a6c", "name": "video_player_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}