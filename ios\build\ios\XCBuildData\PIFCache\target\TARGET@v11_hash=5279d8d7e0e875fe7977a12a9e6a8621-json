{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f37b8754b26d7e3526825398039580c0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfig", "PRODUCT_NAME": "FirebaseRemoteConfig", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986c1a606e697a772817524213e16ad86c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9894d94d43e451ce6183f1752d90c8c4c8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig.modulemap", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfig", "PRODUCT_NAME": "FirebaseRemoteConfig", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980afee386e4c173132b0194f154074a84", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9894d94d43e451ce6183f1752d90c8c4c8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig.modulemap", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfig", "PRODUCT_NAME": "FirebaseRemoteConfig", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98087dd57b19c4c92b0bf828e3ddacc27e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f4b740b46232b60c3a7a7ea7bc34f395", "guid": "bfdfe7dc352907fc980b868725387e989a044b55b151b014b48633ae7b37d74f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98debb6a62cc247283ef12e87ba3372b9f", "guid": "bfdfe7dc352907fc980b868725387e98c4169a6f5c45469eae66989e88d9fadc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98360c92d12e6e7fa1f14e8e7e2d3152bd", "guid": "bfdfe7dc352907fc980b868725387e98af677cbd92fa70ce996801d2abe2c86b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd45065cb6b116e0f2e2805a99dea138", "guid": "bfdfe7dc352907fc980b868725387e98f92fff577a2fcb328222e4e396779c37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df500182ba9249970093f59495fcc1b2", "guid": "bfdfe7dc352907fc980b868725387e98c6c6b98c97f11bdbf3f1df81449c9258"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebbaab016d2526f161e0b8586661e5a6", "guid": "bfdfe7dc352907fc980b868725387e98586faca00b5fcdf9a4be3ed88d6ddbac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f09a6747c6f3bd3b7c8a602a9a09ea8", "guid": "bfdfe7dc352907fc980b868725387e98b9076df76f3e4d8dc859e25529ab20fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853256bd3e1dbdff0524ca05c34a38c8f", "guid": "bfdfe7dc352907fc980b868725387e98b083b943e0a6217522a48082a7f0b6d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d255a7c5e0e432bc94700edb84604dd8", "guid": "bfdfe7dc352907fc980b868725387e9884d8792c1dbe1b17320885cafc640d16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886d0764849b850123eed8550f7c2c8b5", "guid": "bfdfe7dc352907fc980b868725387e98779346614bc291fb2867ea93a156e37b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98403056714cedb1fd6b9ba4d5aa46884c", "guid": "bfdfe7dc352907fc980b868725387e98b27d2553d4ecd6838dfd8efa66239b48", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8a4ada09dc66ce237628c4726d98bc6", "guid": "bfdfe7dc352907fc980b868725387e981ea0a035838a2402eb3ec9124893852e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f94ae14687872b775a52d51a089161a", "guid": "bfdfe7dc352907fc980b868725387e988333b13659cdd31d42f7ba20f339bf0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c44c7db7bc127c0effb9dd4a19b5bcdd", "guid": "bfdfe7dc352907fc980b868725387e9801eba85f2fb196c223a0c94e132f27bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986741e2318474855ba1477295de8d93c9", "guid": "bfdfe7dc352907fc980b868725387e981b8682a72be840d2886480ef787a7d7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874379b9b8f1e5f2500bf8dd6a764b7ca", "guid": "bfdfe7dc352907fc980b868725387e987349b0591f0458ef8c9f3f9fc53ea7f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f26f73d60483e901da700bf7a066d22", "guid": "bfdfe7dc352907fc980b868725387e98eb1826078aa8c3d701cfc99b62b64e05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb98981f02e0abf59d462b2e32d786b9", "guid": "bfdfe7dc352907fc980b868725387e980e346e2df4c22250f2834c4c13413c2f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcf2c9bebdab08dbe26b68d1bc235ee3", "guid": "bfdfe7dc352907fc980b868725387e98f517baf8cb6859b342ea9681edd26c8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7a107894e8940115e2f01c7eb6c7384", "guid": "bfdfe7dc352907fc980b868725387e982f574639ee1d282b2a414f8dad16a915"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6b1c3a48c413e6955feb524ad245fc0", "guid": "bfdfe7dc352907fc980b868725387e98bd43bc7b45e3a246f45a56695a9d4091"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980540d878ea8bba3aaf95ff7988ecdcf9", "guid": "bfdfe7dc352907fc980b868725387e981da7e4836e32e335d6d070527858aba9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868f29f3db1ae024a0f2350acd1ee8cf2", "guid": "bfdfe7dc352907fc980b868725387e98f9ee4cac1892789f53d3e278ffc57885"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4431da427fd7ccff6e11b26dec2bc1f", "guid": "bfdfe7dc352907fc980b868725387e985d82f35fd066b88ef2f86821a8656514"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d3d1a1f4fef9a75f948affd14ccb527", "guid": "bfdfe7dc352907fc980b868725387e98da5e7013788e7cf44fc8680194569f8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6bd86bcfeabe82a48cbb00ad4ae5bab", "guid": "bfdfe7dc352907fc980b868725387e988ebc7e8f05a6ad265bfc1453f3314213"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9ad7ec862b2c2455b978a1e4973f89b", "guid": "bfdfe7dc352907fc980b868725387e985bd3968d01fa91975b4b4c62e480d76a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884d7b7f13976cdda88a0b50849af4d8d", "guid": "bfdfe7dc352907fc980b868725387e9887dbf45b9dea6d9faa89bff3a016fa1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815a4916b4cd1b0e2e60364f2caf39479", "guid": "bfdfe7dc352907fc980b868725387e981dc075e66e0f79f0128db8c75663f3d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98380a082f39f4826c94b1c181d29ff153", "guid": "bfdfe7dc352907fc980b868725387e98e260b65926ef79f49cbb54e02889032f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fee10e4d689da05f2dbdabb84fc44921", "guid": "bfdfe7dc352907fc980b868725387e98c086333b4b0623c6a07d83569c15ba03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b51ae1a9646c91feeb749861a292718d", "guid": "bfdfe7dc352907fc980b868725387e987cddd148f66d52334a4ce7c11042aff5"}], "guid": "bfdfe7dc352907fc980b868725387e9847bbfcc152be4fa70e8a24d84c37aae8", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98afbc3075c412c95574ec98ab98bc0893", "guid": "bfdfe7dc352907fc980b868725387e98055713001bcc792ddfa171c5f44d402d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c640fc4bde1740cccf3c0c5928fc48f", "guid": "bfdfe7dc352907fc980b868725387e98a9ae78d1c31d3ce6cd1624cfb1478dd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f72efb9007ddec73b218f56673779626", "guid": "bfdfe7dc352907fc980b868725387e988485b1ec8f9e644f5b16ef97ce09ac6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987571d017e6f57f79dfbfc5654ae87048", "guid": "bfdfe7dc352907fc980b868725387e98cc4398854eb9d06be2c357067755b7ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ab8f097ad6ce0eea8a8026458a8bf0c", "guid": "bfdfe7dc352907fc980b868725387e985cbfffbd2e51fd67c2d8d7e2f4546cf0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be8f10a6073deaf60d9e627c231e115f", "guid": "bfdfe7dc352907fc980b868725387e98c539babee762e75f12332616ac57ed76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d764c535b3151418da41f3b9a8784d83", "guid": "bfdfe7dc352907fc980b868725387e988c9e586ef2608abbf6c8c1426bccab93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a79c0a048033c1ef2376dbe2ffac5e0", "guid": "bfdfe7dc352907fc980b868725387e982d6445432a4e0683c99b016d4483f91f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985109ebe25abf6833cc6b824ebf5ef3e8", "guid": "bfdfe7dc352907fc980b868725387e98a509d22ee113a9f80772225aee6eaa0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d911a591aae154c115a6b9cfe9aae49f", "guid": "bfdfe7dc352907fc980b868725387e98e03d5ca79ba0ea8475eb661477e16c32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c1172720d881faa6034f1af4eb81fcd", "guid": "bfdfe7dc352907fc980b868725387e9867c499efa88dd524a91189657f86b4f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c0fe21ab5a4e173fd0e79927c94fbde", "guid": "bfdfe7dc352907fc980b868725387e982b32a7d5078c2e0c5e0b2dfd2cd467c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862a080d5e7f3b5b53cbeef731f637840", "guid": "bfdfe7dc352907fc980b868725387e98c0f85ee42d0a372fbaf5b8bce5c85876"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98faea25daa127271d461d1dea30465eaf", "guid": "bfdfe7dc352907fc980b868725387e98012f9a079cecb06f4f6b5ec9af5807fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981651e76f040bb94b880fde7dd6f32b13", "guid": "bfdfe7dc352907fc980b868725387e98d43d6c5a2c3177d93bd998d31a2b72f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a5b2ded4ffab0673a4616af2be6ada7", "guid": "bfdfe7dc352907fc980b868725387e98b93494a27353645250cef779e00439dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0945867143ded49e987ce017940aebc", "guid": "bfdfe7dc352907fc980b868725387e98f1be6e7655a5e73d1aa528bb39513f2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bc3ed9e39112d8b1ced87003e2c5f61", "guid": "bfdfe7dc352907fc980b868725387e98cb0f238a8df8d4fa226402fe45bb3b74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986697ddd42214ab724fd850b08405b97d", "guid": "bfdfe7dc352907fc980b868725387e982e555b395a407b90b57a0b5086c7025c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b8ef1578d4019a0f78e9ba2e6b7edc5", "guid": "bfdfe7dc352907fc980b868725387e986b0e87605f3a5baa7dbe3d2cce9ba821"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836a6380ede898fc1bf6621bcec583c30", "guid": "bfdfe7dc352907fc980b868725387e988c4e4eba8b80ae12a13ae62fd1adb704"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98485d7456b682ca940c7e649ba7a95178", "guid": "bfdfe7dc352907fc980b868725387e980f9c14d6de7a340ff4a4a9d95e552be7"}], "guid": "bfdfe7dc352907fc980b868725387e98d09597088b45f10903909a48f29a4eb2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e98c9fcf93c35a43eed6c529dc8618e0402"}], "guid": "bfdfe7dc352907fc980b868725387e98f0d945463692df3849796e04fe231a3f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9888ae3ee305956c5c768442ceb901c25d", "targetReference": "bfdfe7dc352907fc980b868725387e98012330f90a37c9d15a390c6b73b6dbca"}], "guid": "bfdfe7dc352907fc980b868725387e984cce92f9da32c5abe752b137479a2ace", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e984d1b80eb520d7ec9828b3cb4e14dcb65", "name": "FirebaseABTesting"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98012330f90a37c9d15a390c6b73b6dbca", "name": "FirebaseRemoteConfig-FirebaseRemoteConfig_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e984b1e8e5f67fa144e5e34058df6e2f50c", "name": "FirebaseRemoteConfigInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98928855ae8620d13300183deed96c33a1", "name": "FirebaseRemoteConfig", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980b80126605cba44506bfa90fbbd69742", "name": "FirebaseRemoteConfig.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}