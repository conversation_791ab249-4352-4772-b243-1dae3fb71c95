{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9847d2ea0969b9e0db3ebcda0a83d06e55", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9827037934d7ce2370d95e1db13727cb1e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984d3d1ed1a4c089864c222713f5752955", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b6de917b294773cc125c35d921eb3cc6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984d3d1ed1a4c089864c222713f5752955", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e49904a5fbcf378479a67a3f4acbb793", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9844fb8ab0d2810f4f6338689ea541ad3b", "guid": "bfdfe7dc352907fc980b868725387e9883d42b25aa810903b9dc835241da2af6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c83d57051cb86880e46ecd91ea1b8ee", "guid": "bfdfe7dc352907fc980b868725387e989864d8596ff82ba57ef1e1a82bddef68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa0fd2cb204b09736202ef454194ec51", "guid": "bfdfe7dc352907fc980b868725387e98a9bac7c8915077cb068b486b946db642"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ff75be2396b332d99e4907b35c02536", "guid": "bfdfe7dc352907fc980b868725387e9847a9646cc83a78dfcbac6cb52aca8e2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98961f4210c562687dc6a7899fa85463b6", "guid": "bfdfe7dc352907fc980b868725387e980d200231724155824d8ddfb6becb50d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be42692155856bbd25bcd76eff546fca", "guid": "bfdfe7dc352907fc980b868725387e989608bab64c0216c815c193bbc10c3d13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981913b6744da51d9e9ede9e6db58d06d9", "guid": "bfdfe7dc352907fc980b868725387e98409c52e89bda0801ada2ee959d124b30", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6032d43f23ca5fa2e3cd0092c0d1df7", "guid": "bfdfe7dc352907fc980b868725387e98be9cd52712b2f4c0263051b24ce1f9d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9848b53f4ed56294590f6a042535f75", "guid": "bfdfe7dc352907fc980b868725387e9844c09b82fab709645cf15015be4a70fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd233597c0ad81fa97ed5101f6a23d8a", "guid": "bfdfe7dc352907fc980b868725387e98ce7e00db3c7272fec0fbb4c58fb47b22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988eb33eaeb650e535b4baccf537a66028", "guid": "bfdfe7dc352907fc980b868725387e9852864ae4aea0f29670eb2e2f0924091a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98785cd7335dfe028d9e485c2abf530410", "guid": "bfdfe7dc352907fc980b868725387e98330fc10650a2e8cfe0865daf98451ecc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fadb5cce6d8ba8080fa777294532e9a", "guid": "bfdfe7dc352907fc980b868725387e98218908c7eeefce33b1e3345b58a98edb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98671830e1be45d590408970cbb7451292", "guid": "bfdfe7dc352907fc980b868725387e98332e0c0f1f91a1d44d7c0f4642319edd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887cb77f7f447679447773178417d8172", "guid": "bfdfe7dc352907fc980b868725387e988408867a560ff67a2ba7f14a194d6589"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980af8fa04b7508f86cb0b26305ccf7f3d", "guid": "bfdfe7dc352907fc980b868725387e98fae8e7b090cbe8d6536377998d72be34", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e6bf2f6fbe0a302ecaf95aebcff71c6", "guid": "bfdfe7dc352907fc980b868725387e987d609f1f1d32e30e4f5b85e0ac782405"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ed4f54a1a5bb807003f16ec42cac989", "guid": "bfdfe7dc352907fc980b868725387e982282095fc80fbc4e82aef59778b91e62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889d10286ee442f63baed0c1cf3de3d13", "guid": "bfdfe7dc352907fc980b868725387e98d401a63dc0585920ba964dd3ba3be91e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98791cf79bcc8b0f5f106c79d18256353c", "guid": "bfdfe7dc352907fc980b868725387e980d4169db55afe401cd7e6dd6ac4f013b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0909c16fe566ac65e4da9dee0269ca0", "guid": "bfdfe7dc352907fc980b868725387e98e902f2f06273a773e917ea0459dce90f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab8fe17224e576944813d40e6cd31174", "guid": "bfdfe7dc352907fc980b868725387e98ebff6979ce744fd4ae7979eabf1c798e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2d1f5922a713fce8758f718ff1aaf96", "guid": "bfdfe7dc352907fc980b868725387e982fc040e8769fe515ac9ad2c7f669ee37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f22cc99e72727f8f15de24f9bbb054bf", "guid": "bfdfe7dc352907fc980b868725387e980b8dfe9e880ed0b0b69bf225c2b12b68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfee82558e44714003a249096e200f4b", "guid": "bfdfe7dc352907fc980b868725387e98e17e75afbff945cf35e6a0fe619d2b03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859f07335c982003722b7aca45e150849", "guid": "bfdfe7dc352907fc980b868725387e980a9e6fefd536c3a09a6db06a0359bbf3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876b65817dafd52b6e8eab18d54701878", "guid": "bfdfe7dc352907fc980b868725387e98c07f487af48be5552ff946d5437e73d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989be1fb1bb8b9c0ce5c4396241095c448", "guid": "bfdfe7dc352907fc980b868725387e9857e4c346f35bea259ede7e83a8f078c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4a10a132324e893d042af1098f4a675", "guid": "bfdfe7dc352907fc980b868725387e98aa8afe756fd85767bf2d9b4def291bd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a70f18920669d3919b201f03bec5912", "guid": "bfdfe7dc352907fc980b868725387e98e1fd92fd445dd2a424d7dbed6bb952f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c6f0d247e90ee2647b7f1c25a71f4fe", "guid": "bfdfe7dc352907fc980b868725387e98f21612d5f812b3add77eae823dacb4e6"}], "guid": "bfdfe7dc352907fc980b868725387e98c700e709f4abc43cb7a4f187c389b1aa", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983cbedd68e0ea209a89d4b4581a5d4e45", "guid": "bfdfe7dc352907fc980b868725387e980fc7266683c42e4deecf5dbbe4a65ae0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865a85b3c156c4ba6df293f431bd8f153", "guid": "bfdfe7dc352907fc980b868725387e9818d85c992a08fc88dcafad26688ea303"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989297de3e3f298d27c47c4891bd94686a", "guid": "bfdfe7dc352907fc980b868725387e98f3a420894742d859094db6f6369e2ccc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e03c9c5f39665ae3f2deb6869c2c5b08", "guid": "bfdfe7dc352907fc980b868725387e98166fd45c9037fceb8433ddd89db743a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988df668293ed38fc537b65bf75ea91f47", "guid": "bfdfe7dc352907fc980b868725387e987e44c1b9ecd6264ac0c899b0b3e8a339"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb4059817895c7a4ad3c1b0172c69b05", "guid": "bfdfe7dc352907fc980b868725387e98438674f85a3c8e42c2f4ae4dfa4d0b70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed0b122b847a13aeb8589b7096584c51", "guid": "bfdfe7dc352907fc980b868725387e9843b82f08083b180feee379cf8fa5efcb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847f06b01bc7dcf43e16bd7369270bef5", "guid": "bfdfe7dc352907fc980b868725387e98f5bcdc65541fd83db27ef631ee79cc65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbe455ebf0bd24014077571d35e6813d", "guid": "bfdfe7dc352907fc980b868725387e98705d2437b0b5d276c076fc4e0e751826"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e352a1a10d17907b97b61d87d3a77b8", "guid": "bfdfe7dc352907fc980b868725387e98f0531ec7317dd26fba54ce46bac8b498"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f037b7dfd7806a730a5e6178e4f9e56b", "guid": "bfdfe7dc352907fc980b868725387e98b216a09da458976b3ce39ff2a7b3fcf4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3c1caf6a802afc1f61cd43de69e53ce", "guid": "bfdfe7dc352907fc980b868725387e98887ffd02048532fc2aeaaba3aad38b69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852a0d65252a2c56a63fdacea8f6359d1", "guid": "bfdfe7dc352907fc980b868725387e988d2ab243f4aa2e3c00a7d6cb8c684e9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c756ae52a2a23b94a19dfbc1ab40c310", "guid": "bfdfe7dc352907fc980b868725387e98f3a102db7e69172b23c9ea8d9e3bdfb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823f73cc7a23c3a74a65a2b83d1b58467", "guid": "bfdfe7dc352907fc980b868725387e9846e32614e3138ada5cdc8f82889ccb4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c362a12ef4d5f11616615dd302aeddcf", "guid": "bfdfe7dc352907fc980b868725387e982fd33016b898c2e13a018e0f321f78f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b14fff6f487eed127591aa2dae99680", "guid": "bfdfe7dc352907fc980b868725387e9863aa2f500b1581e31a0ee80678444b88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e61733b0c0a648d012dfc639e5e5365", "guid": "bfdfe7dc352907fc980b868725387e9801afaa2b09aa421e0bd6ceaf66899baa"}], "guid": "bfdfe7dc352907fc980b868725387e983415e71db5cace604c7096eff20c71a9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e9854e0a61cfa24e6da6502df5ce3c506be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c66b5324530b90fee7ec191645484220", "guid": "bfdfe7dc352907fc980b868725387e988fcaabbd7063de1131cd2221cca2277b"}], "guid": "bfdfe7dc352907fc980b868725387e98c050c4243818cc9fb1ba59d4fc282e95", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b4fa62f2cb83d2f4b68a7634fe4a7f19", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98a916c85512eb4962b141c79d9d81aea4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}