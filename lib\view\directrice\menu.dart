import 'package:bee_kids_mobile/view/directrice/footer.dart';
import 'package:flutter/material.dart';
import 'dart:math' as math;

class MenuScreen extends StatelessWidget {
  const MenuScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Get screen dimensions
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // Define the main colors
    const primaryGreen = Color.fromARGB(255, 37, 135, 44);
    const secondaryPink = Color.fromARGB(255, 241, 128, 153);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: primaryGreen),
          onPressed: () => Navigator.pushNamed(context, '/directrice'),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        title: Row(
          children: [
            Image.asset(
              'lib/resources/icons/logo.png',
              height: screenHeight * 0.05,
            ),
            SizedBox(width: screenWidth * 0.02),
            RichText(
              text: TextSpan(
                text: 'BEE-',
                style: TextStyle(
                  color: primaryGreen,
                  fontWeight: FontWeight.bold,
                  fontSize: screenHeight * 0.03,
                ),
                children: [
                  TextSpan(
                    text: 'KIDS',
                    style: TextStyle(
                      color: secondaryPink,
                      fontWeight: FontWeight.bold,
                      fontSize: screenHeight * 0.03,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.white, Colors.grey.shade50],
          ),
        ),
        child: Stack(
          children: [
            // Background decorative elements
            Positioned(
              top: -screenHeight * 0.05,
              right: -screenWidth * 0.1,
              child: Container(
                width: screenWidth * 0.4,
                height: screenWidth * 0.4,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: primaryGreen.withOpacity(0.1),
                ),
              ),
            ),
            Positioned(
              bottom: screenHeight * 0.1,
              left: -screenWidth * 0.15,
              child: Container(
                width: screenWidth * 0.3,
                height: screenWidth * 0.3,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: secondaryPink.withOpacity(0.1),
                ),
              ),
            ),

            // Main content
            SafeArea(
              child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: screenWidth * 0.05,
                  vertical: screenHeight * 0.02,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Welcome section
                    Container(
                      padding: EdgeInsets.all(screenWidth * 0.05),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            primaryGreen,
                            primaryGreen.withOpacity(0.8),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: primaryGreen.withOpacity(0.3),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Row(
                        children: [
                          CircleAvatar(
                            radius: screenWidth * 0.08,
                            backgroundColor: Colors.white,
                            child: Icon(
                              Icons.school,
                              size: screenWidth * 0.08,
                              color: primaryGreen,
                            ),
                          ),
                          SizedBox(width: screenWidth * 0.04),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Bienvenue",
                                  style: TextStyle(
                                    color: Colors.white.withOpacity(0.9),
                                    fontSize: screenHeight * 0.018,
                                  ),
                                ),
                                SizedBox(height: screenHeight * 0.005),
                                Text(
                                  "Menu Principal",
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: screenHeight * 0.028,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: screenHeight * 0.025),

                    // Menu title
                    Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: screenWidth * 0.02),
                      child: Text(
                        "SERVICES",
                        style: TextStyle(
                          color: Colors.grey.shade700,
                          fontSize: screenHeight * 0.018,
                          fontWeight: FontWeight.w600,
                          letterSpacing: 1.2,
                        ),
                      ),
                    ),

                    SizedBox(height: screenHeight * 0.015),

                    // Menu items
                    Expanded(
                      child: Column(
                        children: [
                          // Tableau de Bord - Full width on its own line
                          Container(
                            width: double.infinity,
                            height: screenHeight * 0.12,
                            margin: EdgeInsets.symmetric(
                              horizontal: screenWidth * 0.01,
                              vertical: screenHeight * 0.01,
                            ),
                            child: _buildMenuCardWithIcon(
                              context,
                              title: "TABLEAU DE BORD",
                              icon: Icons.dashboard,
                              color: Color(0xFF37874C),
                              onTap: () {
                                Navigator.pushNamed(
                                    context, '/directrice/tableau-de-bord');
                              },
                              isFullWidth: true,
                            ),
                          ),

                          SizedBox(height: screenHeight * 0.02),

                          // Other 4 items in 2x2 grid
                          Expanded(
                            child: GridView.count(
                              crossAxisCount: 2,
                              childAspectRatio: 0.85,
                              crossAxisSpacing: screenWidth * 0.04,
                              mainAxisSpacing: screenHeight * 0.02,
                              padding: EdgeInsets.symmetric(
                                horizontal: screenWidth * 0.01,
                              ),
                              children: [
                                _buildMenuCard(
                                  context,
                                  title: "SUIVI DES ENFANTS",
                                  iconPath: "lib/resources/icons/fille.png",
                                  color: Color(0xFFF06292),
                                  onTap: () {
                                    Navigator.pushNamed(
                                        context, '/directrice/suivie_enfants');
                                  },
                                ),
                                _buildMenuCard(
                                  context,
                                  title: "CANTINE",
                                  iconPath: "lib/resources/icons/cantine.png",
                                  color: Color(0xFF4CAF50),
                                  onTap: () {
                                    Navigator.pushNamed(
                                        context, '/directrice/cantine');
                                  },
                                ),
                                _buildMenuCard(
                                  context,
                                  title: "EMPLOI",
                                  iconPath: "lib/resources/icons/activites.png",
                                  color: Color(0xFF42A5F5),
                                  onTap: () {
                                    Navigator.pushNamed(
                                        context, '/directrice/ActivitesEtProgramme');
                                  },
                                ),
                                _buildMenuCard(
                                  context,
                                  title: "EVENNEMENTS",
                                  iconPath: "lib/resources/icons/evennement.png",
                                  color: Color(0xFF9575CD),
                                  onTap: () {
                                    Navigator.pushNamed(
                                        context, '/directrice/Evennements');
                                  },
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: const MyFooter(currentRoute: '/directrice/menu'),
    );
  }

  Widget _buildMenuCard(
    BuildContext context, {
    required String title,
    required String iconPath,
    required Color color,
    required VoidCallback onTap,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon container with gradient
            Container(
              width: screenWidth * 0.18,
              height: screenWidth * 0.18,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    color,
                    color.withOpacity(0.7),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(15),
                boxShadow: [
                  BoxShadow(
                    color: color.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Padding(
                padding: EdgeInsets.all(screenWidth * 0.035),
                child: Image.asset(
                  iconPath,
                  color: Colors.white,
                  errorBuilder: (context, error, stackTrace) =>
                      const Icon(Icons.error, color: Colors.white),
                ),
              ),
            ),

            SizedBox(height: screenHeight * 0.02),

            // Title with custom styling
            Padding(
              padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.02),
              child: Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.grey.shade800,
                  fontSize: screenHeight * 0.016,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.5,
                ),
              ),
            ),

            SizedBox(height: screenHeight * 0.01),

            // Animated indicator
            Container(
              width: screenWidth * 0.1,
              height: 4,
              decoration: BoxDecoration(
                color: color.withOpacity(0.7),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuCardWithIcon(
    BuildContext context, {
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    bool isFullWidth = false,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: isFullWidth
            ? Row(
                children: [
                  // Icon container with gradient
                  Container(
                    width: screenWidth * 0.15,
                    height: screenWidth * 0.15,
                    margin: EdgeInsets.all(screenWidth * 0.04),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          color,
                          color.withOpacity(0.7),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(15),
                      boxShadow: [
                        BoxShadow(
                          color: color.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: Icon(
                      icon,
                      color: Colors.white,
                      size: screenWidth * 0.07,
                    ),
                  ),

                  // Title and indicator
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: TextStyle(
                            color: Colors.grey.shade800,
                            fontSize: screenHeight * 0.022,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 0.5,
                          ),
                        ),
                        SizedBox(height: screenHeight * 0.01),
                        Container(
                          width: screenWidth * 0.15,
                          height: 4,
                          decoration: BoxDecoration(
                            color: color.withOpacity(0.7),
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              )
            : Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Icon container with gradient
                  Container(
                    width: screenWidth * 0.18,
                    height: screenWidth * 0.18,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          color,
                          color.withOpacity(0.7),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(15),
                      boxShadow: [
                        BoxShadow(
                          color: color.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: Icon(
                      icon,
                      color: Colors.white,
                      size: screenWidth * 0.08,
                    ),
                  ),

                  SizedBox(height: screenHeight * 0.02),

                  // Title with custom styling
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.02),
                    child: Text(
                      title,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontSize: screenHeight * 0.016,
                        fontWeight: FontWeight.w600,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),

                  SizedBox(height: screenHeight * 0.01),

                  // Animated indicator
                  Container(
                    width: screenWidth * 0.1,
                    height: 4,
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.7),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
