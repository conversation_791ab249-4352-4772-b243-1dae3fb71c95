import 'dart:io';
import 'package:flutter/material.dart';
import 'package:bee_kids_mobile/model/cantine.dart';
import 'package:bee_kids_mobile/view/parent/footer.dart';
import 'package:bee_kids_mobile/view_model/cantineService.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return const MaterialApp(
      home: CantineScreenParent(),
    );
  }
}

class CantineScreenParent extends StatefulWidget {
  const CantineScreenParent({super.key});

  @override
  State<CantineScreenParent> createState() => _CantineScreenState();
}

class _CantineScreenState extends State<CantineScreenParent> {
  final cantineService _menuService = cantineService();
  late Future<List<cantine>> _menus;
  late Future<Map<String, String>> _currentWeek;

  @override
  void initState() {
    super.initState();
    _menus = _menuService.fetchMenus();
    _currentWeek = _menuService.fetchCurrentWeek();
  }

  void _showPopup(BuildContext context, String day, cantine menu) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          child: Padding(
            padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.03),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    'Menu de $day',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: MediaQuery.of(context).size.width * 0.06,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  SizedBox(height: MediaQuery.of(context).size.height * 0.01),
                  Center(
                    child: menu.photoUrl.isNotEmpty
                        ? Image.network(
                            menu.photoUrl,
                            height: MediaQuery.of(context).size.width * 0.4,
                            width: MediaQuery.of(context).size.width * 0.4,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) =>
                                Image.asset(
                              'lib/resources/images/dish-placeholder-hd-png.png',
                              height: MediaQuery.of(context).size.width * 0.4,
                              width: MediaQuery.of(context).size.width * 0.4,
                              fit: BoxFit.cover,
                            ),
                          )
                        : Image.asset(
                            'lib/resources/images/dish-placeholder-hd-png.png',
                            height: MediaQuery.of(context).size.width * 0.4,
                            width: MediaQuery.of(context).size.width * 0.4,
                            fit: BoxFit.cover,
                          ),
                  ),
                  SizedBox(height: MediaQuery.of(context).size.height * 0.02),
                  TextField(
                    enabled: false,
                    controller: TextEditingController(text: menu.entree),
                    style: const TextStyle(color: Colors.black),
                    decoration: const InputDecoration(
                      labelText: 'Entrée',
                      labelStyle: TextStyle(color: Colors.black),
                      border: OutlineInputBorder(),
                    ),
                  ),
                  SizedBox(height: MediaQuery.of(context).size.height * 0.015),
                  TextField(
                    enabled: false,
                    controller:
                        TextEditingController(text: menu.platPrincipale),
                    style: const TextStyle(color: Colors.black),
                    decoration: const InputDecoration(
                      labelText: 'Plat Principal',
                      labelStyle: TextStyle(color: Colors.black),
                      border: OutlineInputBorder(),
                    ),
                  ),
                  SizedBox(height: MediaQuery.of(context).size.height * 0.015),
                  TextField(
                    enabled: false,
                    controller: TextEditingController(text: menu.dessert),
                    style: const TextStyle(color: Colors.black),
                    decoration: const InputDecoration(
                      labelText: 'Dessert',
                      labelStyle: TextStyle(color: Colors.black),
                      border: OutlineInputBorder(),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMenuCard(BuildContext context, String day, cantine menu) {
    return Card(
      color: Colors.green[800],
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _showPopup(context, day, menu),
        child: Padding(
          padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.02),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                day,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: MediaQuery.of(context).size.width * 0.045,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: MediaQuery.of(context).size.height * 0.015),
              menu.photoUrl.isNotEmpty
                  ? Image.network(
                      menu.photoUrl,
                      height: MediaQuery.of(context).size.width * 0.35,
                      width: MediaQuery.of(context).size.width * 0.4,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Image.asset(
                        'lib/resources/images/dish-placeholder-hd-png.png',
                        height: MediaQuery.of(context).size.width * 0.35,
                        width: MediaQuery.of(context).size.width * 0.4,
                        fit: BoxFit.cover,
                      ),
                    )
                  : Image.asset(
                      'lib/resources/images/dish-placeholder-hd-png.png',
                      height: MediaQuery.of(context).size.width * 0.35,
                      width: MediaQuery.of(context).size.width * 0.4,
                      fit: BoxFit.cover,
                    ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pushNamed(context, '/parent/menu'),
        ),
        title: const Text(
          "Cantine",
          style: TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.green[800],
        elevation: 4,
      ),
      body: Column(
        children: [
          Padding(
            padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.04),
            child: Column(
              children: [
                const Text(
                  'Menu de la semaine',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
                FutureBuilder<Map<String, String>>(
                  future: _currentWeek,
                  builder: (context, snapshot) {
                    if (snapshot.hasData) {
                      return Text(
                        'Du ${snapshot.data!['startOfWeek']} au ${snapshot.data!['endOfWeek']}',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ],
            ),
          ),
          Expanded(
            child: FutureBuilder<List<cantine>>(
              future: _menus,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                } else if (snapshot.hasError) {
                  return Center(child: Text('Erreur: ${snapshot.error}'));
                } else if (snapshot.hasData) {
                  final menus = snapshot.data!
                      .where((menu) => menu.jour.toLowerCase() != "samedi")
                      .toList();

                  return GridView.count(
                    crossAxisCount:
                        MediaQuery.of(context).size.width < 600 ? 2 : 3,
                    childAspectRatio:
                        MediaQuery.of(context).size.width < 600 ? 0.8 : 0.7,
                    children: menus.map((menu) {
                      return _buildMenuCard(context, menu.jour, menu);
                    }).toList(),
                  );
                } else {
                  return const Center(child: Text('Aucun menu disponible'));
                }
              },
            ),
          ),
        ],
      ),
      bottomNavigationBar: const MyFooterParent(currentRoute: '/parent/menu'),
    );
  }
}
