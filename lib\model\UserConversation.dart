class UserConversationDTO {
  int conversationId;
  int?
      otherUserId; // Nullable: if null is acceptable, no need to force a non-null value.
  String fullName;
  String? photoUrl; // Nullable
  String? lastMessage; // Nullable
  String? timestamp; // Nullable

  UserConversationDTO({
    required this.conversationId,
    this.otherUserId, // Remove "required" if the field may be null
    required this.fullName,
    this.photoUrl,
    this.lastMessage,
    this.timestamp,
  });

  // Factory constructor to create an instance from JSON
  factory UserConversationDTO.fromJson(Map<String, dynamic> json) {
    // Check if conversationId is present
    if (json['conversationId'] == null) {
      throw Exception("Missing 'conversationId' in JSON");
    }

    final rawTimestamp = json['timestamp'];
    String? validTimestamp;

    if (rawTimestamp != null) {
      if (rawTimestamp is String && rawTimestamp.isNotEmpty) {
        validTimestamp = rawTimestamp;
      } else if (rawTimestamp is int) {
        validTimestamp = rawTimestamp.toString();
      }
    }

    return UserConversationDTO(
      conversationId: json['conversationId'] as int,
      otherUserId: json['otherUserId'] as int?,
      fullName: (json['fullName'] as String?) ?? 'Utilisateur inconnu',
      photoUrl: json['photoUrl'] as String?,
      lastMessage: json['lastMessage'] as String?,
      timestamp: validTimestamp,
    );
  }

  // Method to convert an instance into a JSON map
  Map<String, dynamic> toJson() {
    return {
      'conversationId': conversationId,
      'otherUserId': otherUserId,
      'fullName': fullName,
      'photoUrl': photoUrl,
      'lastMessage': lastMessage,
      'timestamp': timestamp,
    };
  }
}
