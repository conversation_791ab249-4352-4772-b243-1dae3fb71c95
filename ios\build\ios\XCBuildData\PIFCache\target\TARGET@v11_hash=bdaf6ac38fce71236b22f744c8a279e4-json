{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9836e2fea267f34f68c2adca1077b02dd0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98127326ce4ed06222c34a4c0678109dff", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bf2b0bdbed613d0b889c12c4fd6dc095", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b7dda4b318554fcadd1a2902af76e255", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bf2b0bdbed613d0b889c12c4fd6dc095", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980859f7937af31c199306eb2a31bd6528", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980cd990aeddb9a2b53e3e9e91675c267e", "guid": "bfdfe7dc352907fc980b868725387e98cd4309ec698e17f423cfc392e0f58b49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e19ee2fd0b2bcfa78b35b1837c9350d8", "guid": "bfdfe7dc352907fc980b868725387e9804e05c42f5f34f893267a0b597962831"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6c1cea6892d4f2544dbf646d83b1754", "guid": "bfdfe7dc352907fc980b868725387e98b45b7471b57d2601f7dd8036a3ce0ce1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889a19e34e785a63a1abc6deda0884885", "guid": "bfdfe7dc352907fc980b868725387e98951e734b4bdeeb9dd411da8903324b8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986af7e5ac90879dfcdbf324cad251df7c", "guid": "bfdfe7dc352907fc980b868725387e9825f8182d3596ae8d00ac31d071cf65bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a9347c5bb6557ffc48d1e79f4eb6e42", "guid": "bfdfe7dc352907fc980b868725387e98c88bbb2c42248ddf2fb6f8c82c8cb918"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98064c6d81d65dd5691bdd5eafec5462cc", "guid": "bfdfe7dc352907fc980b868725387e98087db49cff40000c55669926e453bd5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98365a126ef4b4f20bf25f4a4024ff2097", "guid": "bfdfe7dc352907fc980b868725387e98ed835acabb560c0fbaf12c0448647b8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862b44026d72650acdad555caa800d8ea", "guid": "bfdfe7dc352907fc980b868725387e98492177d34e4edb8146f41919dbaeb34e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e242229f02bd956793a4298de3b23b3d", "guid": "bfdfe7dc352907fc980b868725387e98c425b34b83e20aca95d584c9f9cf5432", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc92a12ec60c1beb4f500ac23f5e80eb", "guid": "bfdfe7dc352907fc980b868725387e98aad9bc27edfdee1d149ee520b91c2bae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987acdd08c3962680399db14a13b6ef1c5", "guid": "bfdfe7dc352907fc980b868725387e985175185e7f3c07d685e12ac2d3ae49b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98156214de95aceabbdfb1d22391340991", "guid": "bfdfe7dc352907fc980b868725387e980b62a6491ff6280e5d7deef959bb5da9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4021ae44616b93d6736a4f87594562c", "guid": "bfdfe7dc352907fc980b868725387e984c8635d12f5c1018cdfc6747692bc831"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98652c6c2e89d8106aa41d31c6e5e51b82", "guid": "bfdfe7dc352907fc980b868725387e984111edeb2b35b9b69927f7584b21eccf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98235a4bcb9536992e1de20cd00169b7fe", "guid": "bfdfe7dc352907fc980b868725387e98a6911110dc351f7d9171da3a494a401e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee1d0e6ae4ed2f7270a1087841c594ee", "guid": "bfdfe7dc352907fc980b868725387e98aee33f4ad6037e3a85f87b9db50acc08", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847e49340a3c2ad668da968391a0e1dec", "guid": "bfdfe7dc352907fc980b868725387e982e6b98cdd18592fe0d4b76a922731e7d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ae92c0ca88f076319bdb5b60638ddd4", "guid": "bfdfe7dc352907fc980b868725387e98f94525241acd582c2809764b632e098e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834ae1cfd8f16a51c2fdfca275f503d00", "guid": "bfdfe7dc352907fc980b868725387e98311edce96e60391178834a62cd6be4f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980377e4c965863ad4c456b242ef847af1", "guid": "bfdfe7dc352907fc980b868725387e98480152a8e9e87ccc062627cb2ab44703"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf7649ce5db0f067bf3b65a559e102c0", "guid": "bfdfe7dc352907fc980b868725387e986dd4d7d5ded818bc55d7d8860669de83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98066fbc0d07633d9dbaf97106edaac13a", "guid": "bfdfe7dc352907fc980b868725387e98438cf98ed00d647c1e38ffff51b0062d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a95ef4e33fdc9043ea00a5707616f4a8", "guid": "bfdfe7dc352907fc980b868725387e984295990da919807c6ab127e0e653673d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3ab0c031b4776a3e3efa5562819a33d", "guid": "bfdfe7dc352907fc980b868725387e9827df58410d8d41d8598be7810a81fa1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812e0acafa38356f0d847b7c6536dba26", "guid": "bfdfe7dc352907fc980b868725387e98fff587a2fadc3d8945a4911128115246"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da6ec282f194818ece888600d61248d8", "guid": "bfdfe7dc352907fc980b868725387e9849c8ff1f639d8c2f0881ca8d4701dbfc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814172e89960dde82a295f284ca22dca6", "guid": "bfdfe7dc352907fc980b868725387e98152256f446a52838c41b5043d0a28356"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863ab5de0803e5d0c5e4d82479bc2bd2a", "guid": "bfdfe7dc352907fc980b868725387e981b75e17ab391a804ec1637f11f3964c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a0335a1645b188017401679a97521df", "guid": "bfdfe7dc352907fc980b868725387e984b7643e9b8ab8ad8fbea6649ed2bb526"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988657838eadeaab6279922eed32e1f2ef", "guid": "bfdfe7dc352907fc980b868725387e988d9b5414dd3709393818a1aaf0bfe634"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8fbb8c32504309e9622c196eed696d2", "guid": "bfdfe7dc352907fc980b868725387e98bcceb812fe47d97e702744caae2f1986", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d890ac8aaff360b1be7bbff62f994d1b", "guid": "bfdfe7dc352907fc980b868725387e987eae1bddbade7cf9896e5156476791d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b41b055d0d93023dba1b83f4d6dbafb5", "guid": "bfdfe7dc352907fc980b868725387e98d6af04265d3b7dd18e7a0dac734bc769"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985af5932a6cce94790b47fca4107d9bf1", "guid": "bfdfe7dc352907fc980b868725387e984e06cf9bf031c1ce1fa25e4763655369"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5d908241773c32babcbf8049524d005", "guid": "bfdfe7dc352907fc980b868725387e98ba0deebaffb15b7792c7b02294b02de7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e40bd11bd8255e9caea563c92141acc1", "guid": "bfdfe7dc352907fc980b868725387e986515e0db4b2614e92080fa3c20b54fd5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c30b579f40eb8ac242fa040a1d7df96", "guid": "bfdfe7dc352907fc980b868725387e986391bfc3426c5b65065bbb52aa73b3b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e5526e033e87ece49960186cbfa40a5", "guid": "bfdfe7dc352907fc980b868725387e985f0392ea5872a70dbb6f3649f02aa191"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889b8f100610d1995fd79c64c758f1684", "guid": "bfdfe7dc352907fc980b868725387e98d8d0723a8367a5ade0a4d008a30f47ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830a585ed3af44bd7e3d4a8310638c7ee", "guid": "bfdfe7dc352907fc980b868725387e98118d778c6d906942831a5d7d1d41c078"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c878940146155f05244e82d95763f8a", "guid": "bfdfe7dc352907fc980b868725387e989ed2c5bf042ded95b82fd26ac39b8c87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bef96630ab8f9ca7a6911bcf05e1ae1", "guid": "bfdfe7dc352907fc980b868725387e98fc30a0b3ce18de2fe2c29e88e6fdbaae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98966e9b07655e40a96486a1f5ffc1d07b", "guid": "bfdfe7dc352907fc980b868725387e98b8d59fe3586ed9e8b549090e2d427839"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803d93a6f51b26090c6ab064c6a40854a", "guid": "bfdfe7dc352907fc980b868725387e985178fac28c52185fde8731fc3f2a2f8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980782bd1b55673d4c5391e945bbd9566e", "guid": "bfdfe7dc352907fc980b868725387e9820513ae4458ec8ae1c01a6c8a7fcc0d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98525224e8fa93f02cc4d351d7cf1a650a", "guid": "bfdfe7dc352907fc980b868725387e98972b2d43184680f9e993ead52920a0dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eaec68762ce559fc05b1b1f5b77fa1c9", "guid": "bfdfe7dc352907fc980b868725387e9876968592721d8c9d7fbec42d980418e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e0115c93777d12229619fb01f17f266", "guid": "bfdfe7dc352907fc980b868725387e98625cba17130620f174d1d58a727c09f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816abad79b84d9a3fa902934f222d27e8", "guid": "bfdfe7dc352907fc980b868725387e98f9fb9505730becf45f7ab7a6ddf714e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98638e96f9ba5d49017626ad5d2b32b03b", "guid": "bfdfe7dc352907fc980b868725387e98629f25ded7a4b5f69e769617b58663c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c137d8efc233dd9b448aba46a8b9cc63", "guid": "bfdfe7dc352907fc980b868725387e980b139e1dcc7ada33e026015d04874fb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c384c9c464b8238c2bac4b71916f9d62", "guid": "bfdfe7dc352907fc980b868725387e98cac38957ef83bc591cf53e1380f72639"}], "guid": "bfdfe7dc352907fc980b868725387e98c23abad974000e64a3a99308b55a8100", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aca1e2660fce78638cca7e0590d04748", "guid": "bfdfe7dc352907fc980b868725387e988ce08a4c40c1fc9444e6872348a5fe5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822ba33887212256729751e0e9f231ffe", "guid": "bfdfe7dc352907fc980b868725387e9848e5ba2a04effda1559a62fab5fa45dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986454854bde2bdbb659a49b11b5176904", "guid": "bfdfe7dc352907fc980b868725387e98c4ca88a8c1fe424d375de0ad6f9f939c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984355104fd6c432836bb893f1375b290d", "guid": "bfdfe7dc352907fc980b868725387e98dd1dcdbe404c85f3c662ccadc4a59dc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a5f0d572bc8591c2e94aff5fa4f472f", "guid": "bfdfe7dc352907fc980b868725387e9871f7ad083567d8cb1c842f446a6c01c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0153602c8037b1ba11db069fa43baf1", "guid": "bfdfe7dc352907fc980b868725387e985ccb7c25fcf58914a2ee1c494cf0dde5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981877163b855c7867f98e397df4617fed", "guid": "bfdfe7dc352907fc980b868725387e987336840156cfb73c5efa1f7a2afc3f2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836b84566b654efbb9275668227a053b7", "guid": "bfdfe7dc352907fc980b868725387e985565da94c610616f8c96f7e620f05f86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee91c8bbdde635400b266c591483cd30", "guid": "bfdfe7dc352907fc980b868725387e981a8f3b972eecb5259e7fe503bde0a6bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7db8f43806a4fafb3a56a6be6ab8ffa", "guid": "bfdfe7dc352907fc980b868725387e9891aa521830d4482eff50eab327e9394e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a1df91a5f4ae04112942cac0a3a319c", "guid": "bfdfe7dc352907fc980b868725387e9890786651379b4b8ea17eae2f39cb3161"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e1bfa53e278572f823624637eaddc0f", "guid": "bfdfe7dc352907fc980b868725387e9880b9e08deccaecc7551b1e91e7035965"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98409ffc3fd9172930358bba9c482f2577", "guid": "bfdfe7dc352907fc980b868725387e9809dcad7347e0681bfa307c1705db1d9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b552e821ccce3a937c04b4a4727cc186", "guid": "bfdfe7dc352907fc980b868725387e9864544534a3f18fee9ced7ab92d752de1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986648f53b87017adbc90e6f6b85027dbd", "guid": "bfdfe7dc352907fc980b868725387e98ee1ef57d1b6acb3f271839dc92f56d10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c72f568b6f7cf5fb30f12f03ae14d3a", "guid": "bfdfe7dc352907fc980b868725387e98a8ee02f864b6829106de6bb1451e76d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833442c5e97d538d15ece3d8398a2f673", "guid": "bfdfe7dc352907fc980b868725387e988bc1de908028b164a721072397d3ff23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a58831ff940e72fb6ec509cde3b0a5b0", "guid": "bfdfe7dc352907fc980b868725387e982fb41ea504f801990a1805569439012c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dae7ed1c512150cc230fc1a19ad36695", "guid": "bfdfe7dc352907fc980b868725387e982c566b9dc81fad36da51acb8fb9f8f70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980db04d8ca91b55a1553a282363c79cc9", "guid": "bfdfe7dc352907fc980b868725387e98421e129c523bcd473fbdf4ddcacbb074"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980de8a461f39360b71fe082c6f0bb4068", "guid": "bfdfe7dc352907fc980b868725387e98a968a78fd4b8abf72869a59b3a07069d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861b859ac461efedbeeae7c23e91ccc0a", "guid": "bfdfe7dc352907fc980b868725387e986ec07676e51e9c68791a4269b3208076"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98173afae5f0df44d1e5ea899bb1e56fbc", "guid": "bfdfe7dc352907fc980b868725387e98c5af91e9a5f8b79ef1c1a7715960fb7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fef321b47e9680b45a4ac0b5f8ee26c", "guid": "bfdfe7dc352907fc980b868725387e98a61c8c8f338ecdb87a69cc0b68172dbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbb2ed2baea693a8f97a2d1260270fad", "guid": "bfdfe7dc352907fc980b868725387e98dce02fd02b5d5162e91e70a5762f79cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d245fa70061752e9f1074b2f051ec37", "guid": "bfdfe7dc352907fc980b868725387e98a7c787e4535507343728d462de2f50b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834ffa90aef5d6ff796749267bd845a4c", "guid": "bfdfe7dc352907fc980b868725387e98beb85b5d888c648e5b2d6aeae9cca25b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ace01df287dd5cb07ef960f5ab3a617e", "guid": "bfdfe7dc352907fc980b868725387e986659c40b0f7271d286328c32fc9157e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b1594f8118893e0d7e010147e289670", "guid": "bfdfe7dc352907fc980b868725387e985e044220631fb81709ffc145cb4d97c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f17b71d361c6e29abd86fa215ead7b5e", "guid": "bfdfe7dc352907fc980b868725387e98130520188fb510be6cfdf2dcb38cbffb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98540a1b236f48cf637004cd82aff4d551", "guid": "bfdfe7dc352907fc980b868725387e984c2dd984a610b6e8eebc3df4a031cd96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8cdf8c413e74389745b18f142ec538a", "guid": "bfdfe7dc352907fc980b868725387e98f5b7a10c459cbb7659fbe8977cbaae60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981eadc682c663a638cbd1274d060a2830", "guid": "bfdfe7dc352907fc980b868725387e980b74a3f6e472bdccc95711f12173d1e6"}], "guid": "bfdfe7dc352907fc980b868725387e988a1d9fc2f37fc8e43404008c526fd639", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e98f58f4f0d214d8c8f2f45334e2f1b1dc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e56187f5d1ece87c9a12e28d3c1a5427", "guid": "bfdfe7dc352907fc980b868725387e9865693d8b8eea92e299e92bdd9100bf4d"}], "guid": "bfdfe7dc352907fc980b868725387e988b507864949f1f89412e4d0bdb462f91", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9807c020124b17290c4eca2ea86dfde80a", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e98368b6420161209e0e87d93fedb43314a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}