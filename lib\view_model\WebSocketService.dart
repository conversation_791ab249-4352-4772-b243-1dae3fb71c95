import 'dart:async';
import 'dart:convert';
import 'package:bee_kids_mobile/resources/environnement/apiUrl.dart';
import 'package:bee_kids_mobile/view_model/tokenService.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:stomp_dart_client/stomp.dart';
import 'package:stomp_dart_client/stomp_config.dart';
import 'package:stomp_dart_client/stomp_frame.dart';
import 'package:bee_kids_mobile/view_model/Notification_handler.dart';
import 'package:logger/logger.dart';
import 'package:stomp_dart_client/stomp_handler.dart';
import 'package:bee_kids_mobile/model/Notification.dart' as custom;

class WebSocketService {
  static final WebSocketService _instance = WebSocketService._internal();
  final String baseUrl = ApiWebSocketUrl.baseUrl;
  Notification_Handler get _notificationHandler => Notification_Handler();
  final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 5,
      lineLength: 50,
      colors: true,
      printEmojis: true,
    ),
  );

  // Simplified constructor
  WebSocketService._internal() {
    print("🔄 WEBSOCKET: Service instance created");
  }

  // Factory constructor with auto-initialization
  factory WebSocketService() {
    if (!_instance._initialized) {
      _instance._initialized = true;
      _instance.initialize();
    }
    return _instance;
  }

  // Add a flag to track initialization
  bool _initialized = false;

  StompClient? stompClient;
  bool _isConnected = false;
  int _reconnectAttempts = 0;
  Timer? _reconnectTimer;
  String? _userId;
  bool _isReconnecting = false;

  // Broadcast streams for different message types
  final _conversationUpdatesController = StreamController<String>.broadcast();
  final _notificationUpdatesController = StreamController<String>.broadcast();

  Stream<String> get conversationUpdates =>
      _conversationUpdatesController.stream;
  Stream<String> get notificationUpdates =>
      _notificationUpdatesController.stream;

  // Store subscriptions
  StompUnsubscribe? _notificationSubscription;

  // Add this property
  Timer? _connectionMonitorTimer;
  Timer? _heartbeatTimer;

  // Initialize method with improved connection monitoring
  void initialize() {
    print("🔄 WEBSOCKET: Initializing WebSocket service");
    startConnectionMonitor();
    _setupHeartbeat();

    // Try to auto-connect if user ID is available
    _fetchUserIdAndConnect();
  }

  // Setup heartbeat to keep connection alive
  void _setupHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(Duration(seconds: 15), (timer) {
      if (isConnected()) {
        print("💓 WEBSOCKET: Sending heartbeat");
        stompClient?.send(
            destination: '/app/heartbeat', body: '{"type":"ping"}');
      }
    });
  }

  // Improved connection monitor
  void startConnectionMonitor() {
    // Cancel existing timer if any
    _connectionMonitorTimer?.cancel();

    // Create a new timer that checks connection every 15 seconds
    _connectionMonitorTimer =
        Timer.periodic(Duration(seconds: 15), (timer) async {
      print("🔍 WEBSOCKET: Connection monitor checking status...");

      if (!_isConnected || stompClient?.connected != true) {
        print("🔄 WEBSOCKET: Connection monitor detected disconnection");

        // Try to reconnect with existing userId
        if (_userId != null) {
          print("🔄 WEBSOCKET: Reconnecting with existing userId: $_userId");
          connect(_userId!);
        } else {
          // Try to fetch userId from SharedPreferences
          print(
              "🔄 WEBSOCKET: Fetching userId from SharedPreferences for reconnection");
          _fetchUserIdAndConnect();
        }
      } else {
        print("✅ WEBSOCKET: Connection monitor confirms active connection");
      }
    });
  }

  // Improved connect method with better error handling
  void connect(String? userId) {
    if (userId == null || userId.isEmpty) {
      print("⚠️ WEBSOCKET: Cannot connect with null or empty userId");
      _fetchUserIdAndConnect();
      return;
    }

    _userId = userId;
    if (_isConnected && stompClient?.connected == true) {
      print(
          "🔌 WEBSOCKET: Already connected, skipping connection for user: $userId");
      return;
    }

    // Prevent multiple simultaneous reconnection attempts
    if (_isReconnecting) {
      print(
          "🔄 WEBSOCKET: Reconnection already in progress, skipping duplicate attempt");
      return;
    }

    _isReconnecting = true;

    print(
        "🔌 WEBSOCKET: Attempting to connect to WebSocket server for user: $userId");
    _logger.i('Attempting to connect to WebSocket server...');

    // Disconnect existing client if any
    if (stompClient != null) {
      print("🔌 WEBSOCKET: Disconnecting existing client before reconnecting");
      try {
        stompClient?.deactivate();
      } catch (e) {
        print("⚠️ WEBSOCKET: Error during deactivation: $e");
      }
    }

    try {
      stompClient = StompClient(
        config: StompConfig(
          url: baseUrl,
          onConnect: (StompFrame frame) {
            _isConnected = true;
            _isReconnecting = false;
            _reconnectAttempts = 0; // Reset attempts on success
            print(
                "✅ WEBSOCKET: Successfully connected to server for user: $userId");
            _logger.i('WebSocket Connected.');
            _processQueuedMessages();
            _subscribeToChannels(userId);
          },
          onWebSocketError: (dynamic error) {
            print("❌ WEBSOCKET ERROR: $error");
            _logger.e('WebSocket Error',
                error: error, stackTrace: StackTrace.current);
            _isReconnecting = false;
            _isConnected = false;
            _scheduleReconnect();
          },
          onStompError: (dynamic error) {
            print("❌ STOMP ERROR: $error");
            _logger.e('STOMP Error', error: error);
            _isReconnecting = false;
          },
          onDisconnect: (dynamic frame) {
            print(
                "🔌 WEBSOCKET: Disconnected from server. Reason: ${frame?.headers}");
            _logger.w('Disconnected from WebSocket. Reason: ${frame?.headers}');
            _isConnected = false;
            _isReconnecting = false;
            _scheduleReconnect();
          },
          heartbeatIncoming: Duration(milliseconds: 5000),
          heartbeatOutgoing: Duration(milliseconds: 5000),
          connectionTimeout: Duration(seconds: 10),
          reconnectDelay: Duration(seconds: 3),
          stompConnectHeaders: {'heart-beat': '5000,5000'},
        ),
      );

      print("🔌 WEBSOCKET: Activating STOMP client for user: $userId");
      stompClient?.activate();
    } catch (e) {
      print("❌ WEBSOCKET: Error creating STOMP client: $e");
      _isReconnecting = false;
      _isConnected = false;
      _scheduleReconnect();
    }
  }

  void _subscribeToChannels(String userId) {
    Future.delayed(Duration(milliseconds: 400), () {
      try {
        // Unsubscribe previous subscriptions before resubscribing
        if (_notificationSubscription != null) {
          print(
              "🔌 WEBSOCKET: Unsubscribing from previous notification channel");
          _notificationSubscription?.call();
          _notificationSubscription = null;
        }

        // Subscribe to mobile notifications
        print(
            "📌 WEBSOCKET: Subscribing to mobile notifications: /user/$userId/queue/mobile-notifications");
        _notificationSubscription = stompClient?.subscribe(
          destination: '/user/$userId/queue/mobile-notifications',
          callback: (StompFrame frame) {
            print("📱 WEBSOCKET MOBILE NOTIFICATION RECEIVED: ${frame.body}");
            if (frame.body != null) _handleFrame(frame.body!);
          },
        );

        // Also subscribe to direct messages channel
        print(
            "📌 WEBSOCKET: Subscribing to direct messages: /user/$userId/queue/messages");
        stompClient?.subscribe(
          destination: '/user/$userId/queue/messages',
          callback: (StompFrame frame) {
            print("💬 WEBSOCKET DIRECT MESSAGE RECEIVED: ${frame.body}");
            if (frame.body != null) _handleFrame(frame.body!);
          },
        );

        // Subscribe to broadcast messages
        print(
            "📌 WEBSOCKET: Subscribing to broadcast messages: /topic/messages");
        stompClient?.subscribe(
          destination: '/topic/messages',
          callback: (StompFrame frame) {
            print("📢 WEBSOCKET BROADCAST MESSAGE RECEIVED: ${frame.body}");
            if (frame.body != null) _handleFrame(frame.body!);
          },
        );

        // Add specific subscription for group messages
        print(
            "📌 WEBSOCKET: Subscribing to group messages: /topic/group-messages");
        stompClient?.subscribe(
          destination: '/topic/group-messages',
          callback: (StompFrame frame) {
            print("👥 WEBSOCKET GROUP MESSAGE RECEIVED: ${frame.body}");
            if (frame.body != null) _handleFrame(frame.body!);
          },
        );

        // Add specific subscription for class groups
        print(
            "📌 WEBSOCKET: Subscribing to class group messages: /topic/class-messages");
        stompClient?.subscribe(
          destination: '/topic/class-messages',
          callback: (StompFrame frame) {
            print("🏫 WEBSOCKET CLASS GROUP MESSAGE RECEIVED: ${frame.body}");
            if (frame.body != null) _handleFrame(frame.body!);
          },
        );

        _logger.i('Subscribed to channels.');
      } catch (e) {
        print("❌ WEBSOCKET SUBSCRIPTION ERROR: $e");
        _logger.e('Subscription error', error: e);
        _scheduleReconnect();
      }
    });
  }

  // Improved message deduplication system
  final Map<String, int> _processedMessageTimestamps = {};
  final int _duplicateDetectionWindowMs =
      5000; // 5 seconds window for deduplication

  void _handleFrame(String body) {
    try {
      print("🔍 WEBSOCKET: Processing incoming message: $body");
      var data = json.decode(body);
      print("📊 DEBUG WebSocket data received: $data");

      // Generate a unique message identifier
      String messageId = _generateMessageId(data);
      int currentTime = DateTime.now().millisecondsSinceEpoch;

      // Check if we've seen this message recently
      if (_processedMessageTimestamps.containsKey(messageId)) {
        int lastSeen = _processedMessageTimestamps[messageId]!;
        if (currentTime - lastSeen < _duplicateDetectionWindowMs) {
          print("🔄 WEBSOCKET: Duplicate message detected with ID: $messageId");
          return;
        }
      }

      // Update the timestamp for this message
      _processedMessageTimestamps[messageId] = currentTime;
      print("✅ WEBSOCKET: Processing new message with ID: $messageId");

      // Clean up old entries periodically
      if (_processedMessageTimestamps.length > 100) {
        _cleanupOldMessages();
      }

      // Check for message counter
      if (data.containsKey('messageCounter')) {
        print("📬 Message counter received: ${data['messageCounter']}");
      }

      // Strict check: both senderId and receiverId must be non-null for 1-to-1 messages
      bool isDirectMessage = data['senderId'] != null &&
          (data['receiverId'] != null || data['isGroupMessage'] == true);
      _logger.d('isMessage: $isDirectMessage, data: $data');
      print(
          "🔍 WEBSOCKET: Message type check - isDirectMessage: $isDirectMessage");

      // Convert JSON data using your custom NotificationModel
      try {
        custom.NotificationModel notification =
            custom.NotificationModel.fromJson(data);
        bool isNotification = notification.isMenu ||
            notification.isNewPubReq ||
            notification.isNewPub ||
            notification.isSuivie ||
            notification.isEvent;
        _logger.d('isNotification: $isNotification');
        print(
            "🔍 WEBSOCKET: Notification type check - isNotification: $isNotification");

        if (isNotification) {
          _logger.i('Received notification message: $body');
          print("📣 WEBSOCKET: Broadcasting notification to listeners");
          _notificationUpdatesController.add(body);
          _notificationHandler.handleWebSocketNotification(body);
        } else if (isDirectMessage) {
          _logger.d('Received conversation message: $body');
          print("💬 WEBSOCKET: Broadcasting conversation message to listeners");
          _conversationUpdatesController.add(body);

          // Only handle as notification if it's not from the current user
          if (data['senderId']?.toString() != _userId) {
            _notificationHandler.handleWebSocketNotification(body);
          }
        } else {
          _logger.w('Unrecognized message type: $body');
          print(
              "⚠️ WEBSOCKET: Unrecognized message type, treating as conversation message");
          _conversationUpdatesController.add(body);
        }
      } catch (e) {
        print(
            "⚠️ WEBSOCKET: Error parsing notification model, treating as conversation message: $e");
        _conversationUpdatesController.add(body);
      }
    } catch (e) {
      print("❌ WEBSOCKET: JSON parsing error: $e for message: $body");
      _logger
          .w('JSON decoding failed; treating as conversation message: $body');
      _conversationUpdatesController.add(body);
    }
  }

  // Helper method to generate a unique message identifier
  String _generateMessageId(Map<String, dynamic> data) {
    // Try to use existing IDs first
    if (data['id'] != null) return 'id_${data['id']}';
    if (data['messageId'] != null) return 'msgid_${data['messageId']}';

    // For messages, use a combination of fields
    String senderId = data['senderId']?.toString() ?? '';
    String content =
        data['content']?.toString() ?? data['messageContent']?.toString() ?? '';
    String timestamp =
        data['timestamp']?.toString() ?? data['timeStamp']?.toString() ?? '';
    String conversationId = data['conversationId']?.toString() ?? '';

    if (senderId.isNotEmpty && content.isNotEmpty) {
      // Include conversation ID for group messages
      if (data['isGroupMessage'] == true && conversationId.isNotEmpty) {
        return 'group_${conversationId}_${senderId}_${content.hashCode}';
      }

      // For direct messages
      if (timestamp.isNotEmpty) {
        return 'msg_${senderId}_${content.hashCode}_$timestamp';
      }
      return 'msg_${senderId}_${content.hashCode}';
    }

    // Fallback to a hash of the entire data
    return 'hash_${data.toString().hashCode}';
  }

  // Clean up old message entries to prevent memory leaks
  void _cleanupOldMessages() {
    int currentTime = DateTime.now().millisecondsSinceEpoch;
    List<String> keysToRemove = [];

    _processedMessageTimestamps.forEach((key, timestamp) {
      if (currentTime - timestamp > _duplicateDetectionWindowMs * 2) {
        keysToRemove.add(key);
      }
    });

    for (var key in keysToRemove) {
      _processedMessageTimestamps.remove(key);
    }

    print(
        "🧹 WEBSOCKET: Cleaned up ${keysToRemove.length} old message entries");
  }

  final List<Map<String, String>> _messageQueue = [];

  void sendMessage(String destination, String message) {
    print("📤 WEBSOCKET: Attempting to send message to $destination: $message");

    try {
      // Parse the message to check if it's a group message
      Map<String, dynamic> messageData = json.decode(message);
      bool isGroupMessage = messageData['isGroupMessage'] == true;

      if (isGroupMessage) {
        print("👥 WEBSOCKET: Detected group message, using special handling");

        // For group messages, use a different destination if needed
        String groupDestination = '/app/group-messages';

        if (_isConnected && stompClient?.connected == true) {
          print("📤 WEBSOCKET: Sending group message via active connection");
          stompClient?.send(destination: groupDestination, body: message);
        } else {
          print("📤 WEBSOCKET: Connection not ready, queueing group message");
          _messageQueue
              .add({'destination': groupDestination, 'message': message});

          // Try to reconnect if not connected
          if (!_isConnected && _userId != null) {
            print(
                "🔄 WEBSOCKET: Triggering reconnection attempt for group message");
            connect(_userId!);
          }
        }
        return;
      }
    } catch (e) {
      print("⚠️ WEBSOCKET: Error parsing message JSON: $e");
      // Continue with normal sending if parsing fails
    }

    // Regular message handling
    if (_isConnected && stompClient?.connected == true) {
      print("📤 WEBSOCKET: Sending message via active connection");
      stompClient?.send(destination: destination, body: message);
    } else {
      print("📤 WEBSOCKET: Connection not ready, queueing message");
      _messageQueue.add({'destination': destination, 'message': message});

      // Try to reconnect if not connected
      if (!_isConnected && _userId != null) {
        print("🔄 WEBSOCKET: Triggering reconnection attempt");
        connect(_userId!);
      }
    }
  }

  void disconnect() {
    print("🔌 WEBSOCKET: Disconnecting from server");
    _reconnectTimer?.cancel();
    _heartbeatTimer?.cancel();
    _connectionMonitorTimer?.cancel();
    stompClient?.deactivate();
    _reconnectAttempts = 0; // Reset attempts
    _userId = null;
    _isConnected = false;
    _logger.i('Disconnected from WebSocket server');
  }

  // Add a method to fetch userId from SharedPreferences when needed
  Future<void> _fetchUserIdAndConnect() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getInt('userId')?.toString();

      if (userId != null && userId.isNotEmpty) {
        print("🔍 WEBSOCKET: Retrieved userId from SharedPreferences: $userId");
        connect(userId);
      } else {
        print("⚠️ WEBSOCKET: Could not retrieve userId from SharedPreferences");
      }
    } catch (e) {
      print("❌ WEBSOCKET: Error retrieving userId from SharedPreferences: $e");
    }
  }

  // Improved reconnect scheduler with exponential backoff
  void _scheduleReconnect() async {
    if (_reconnectTimer != null && _reconnectTimer!.isActive) {
      print("🔄 WEBSOCKET: Reconnection already scheduled, skipping");
      return;
    }

    // Add check for valid token
    final tokenService = TokenService();
    final String? token = await tokenService.getToken();

    if (token == null) {
      print("🔒 WEBSOCKET: Skipping reconnection - user not authenticated");
      _logger.i('Skipping reconnection - user not authenticated');
      return;
    }

    _reconnectAttempts++;
    int delay = (2 * _reconnectAttempts).clamp(2, 30);

    print(
        "🔄 WEBSOCKET: Scheduling reconnection attempt #$_reconnectAttempts in $delay seconds");
    _logger.i('Reconnecting in $delay seconds...');
    _reconnectTimer = Timer(Duration(seconds: delay), () {
      if (_userId != null) {
        print(
            "🔄 WEBSOCKET: Executing scheduled reconnection for user: $_userId");
        connect(_userId!);
      } else {
        print(
            "⚠️ WEBSOCKET: Cannot reconnect - userId is null, trying to fetch from SharedPreferences");
        _fetchUserIdAndConnect();
      }
    });
  }

  void _processQueuedMessages() {
    if (_messageQueue.isEmpty) {
      print("📤 WEBSOCKET: No queued messages to process");
      return;
    }

    print("📤 WEBSOCKET: Processing ${_messageQueue.length} queued messages");
    for (var message in _messageQueue) {
      print(
          "📤 WEBSOCKET: Sending queued message to ${message['destination']}: ${message['message']}");
      stompClient?.send(
          destination: message['destination']!, body: message['message']!);
    }
    _messageQueue.clear();
    print("✅ WEBSOCKET: Queue processing complete");
  }

  bool isConnected() {
    bool connected = _isConnected && stompClient?.connected == true;
    print("🔌 WEBSOCKET: Connection status check - isConnected: $connected");
    return connected;
  }

  // Force reconnect method
  void forceReconnect() {
    if (_userId != null) {
      print("🔄 WEBSOCKET: Forcing reconnection for user: $_userId");
      disconnect();
      connect(_userId);
    } else {
      print(
          "⚠️ WEBSOCKET: Cannot force reconnect - userId is null, trying to fetch from SharedPreferences");
      _fetchUserIdAndConnect();
    }
  }

  // Process notification as message method
  void processNotificationAsMessage(Map<String, dynamic> notificationData) {
    try {
      print(
          "🔄 WEBSOCKET: Processing notification as message: $notificationData");

      // Generate a unique message identifier
      String messageId = _generateMessageId(notificationData);
      int currentTime = DateTime.now().millisecondsSinceEpoch;

      // Check if we've seen this message recently
      if (_processedMessageTimestamps.containsKey(messageId)) {
        int lastSeen = _processedMessageTimestamps[messageId]!;
        if (currentTime - lastSeen < _duplicateDetectionWindowMs) {
          print(
              "🔄 WEBSOCKET: Duplicate notification-message detected: $messageId");
          return;
        }
      }

      // Update the timestamp for this message
      _processedMessageTimestamps[messageId] = currentTime;

      // Check if this is a chat message notification
      bool is1to1Message = notificationData['is1to1Message'] == true;
      bool isGroupMessage = notificationData['isGroupMessage'] == true;
      String messageContent = notificationData['messageContent'] ??
          notificationData['content'] ??
          '';

      if ((is1to1Message || isGroupMessage) && messageContent.isNotEmpty) {
        // Create a message format that ConversationScreen can understand
        Map<String, dynamic> messageData = {
          'senderId': notificationData['senderId']?.toString(),
          'receiverId': notificationData['receiverId']?.toString(),
          'content': messageContent,
          'conversationId': notificationData['conversationId']?.toString(),
          'timestamp': notificationData['timeStamp'] != null
              ? _convertTimeStampToMillis(notificationData['timeStamp'])
              : DateTime.now().millisecondsSinceEpoch.toString(),
          'status': 'received',
          'isRead': false,
          'fullName': notificationData['senderName'] ?? '',
          'photoUrl': notificationData['senderPhoto'] ?? '',
          'isGroupMessage': isGroupMessage,
        };

        // Send to conversation stream
        print(
            "📢 WEBSOCKET: Broadcasting notification as message: $messageData");
        _conversationUpdatesController.add(jsonEncode(messageData));
      }
    } catch (e) {
      print("❌ WEBSOCKET: Error processing notification as message: $e");
    }
  }

  // Helper method to convert timestamp formats
  String _convertTimeStampToMillis(dynamic timestamp) {
    try {
      if (timestamp is String && timestamp.contains('T')) {
        // Parse ISO format
        return DateTime.parse(timestamp).millisecondsSinceEpoch.toString();
      } else if (timestamp is int) {
        return timestamp.toString();
      } else {
        return DateTime.now().millisecondsSinceEpoch.toString();
      }
    } catch (e) {
      print("⚠️ WEBSOCKET: Error converting timestamp: $e");
      return DateTime.now().millisecondsSinceEpoch.toString();
    }
  }

  // Method to ensure a user is connected
  void ensureConnected(String userId) {
    if (_userId != userId) {
      print(
          "🔄 WEBSOCKET: User ID changed from $_userId to $userId, reconnecting");
      disconnect();
      connect(userId);
    } else if (!isConnected()) {
      print(
          "🔄 WEBSOCKET: Connection not active, reconnecting for user: $userId");
      connect(userId);
    } else {
      print("✅ WEBSOCKET: Already connected for user: $userId");
    }
  }
}
