/* import 'dart:convert';
import 'package:bee_kids_mobile/model/Notification.dart';
import 'package:bee_kids_mobile/resources/environnement/apiUrl.dart';
import 'package:bee_kids_mobile/view_model/WebSocketService.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';

class NotificationService {
  final baseUrl = ApiUrl.baseUrl;
  final WebSocketService _webSocketService = WebSocketService();

  Stream<String> get notificationUpdates =>
      _webSocketService.notificationUpdates;

  final StreamController<List<NotificationModel>> _notificationsController =
      StreamController<List<NotificationModel>>.broadcast();

  Stream<List<NotificationModel>> get notificationsStream =>
      _notificationsController.stream;
  List<NotificationModel> _notifications = [];

  NotificationService() {
    // Écoute des notifications en temps réel via WebSocket
    _webSocketService.notificationUpdates.listen((notification) {
      _handleIncomingNotification(notification);
    });
  }

  Future<void> _handleIncomingNotification(String notificationJson) async {
    try {
      final parsedNotification = json.decode(notificationJson);
      final newNotification = NotificationModel.fromJson(parsedNotification);

      _notifications.insert(0, newNotification);
      _notificationsController.add(List.from(_notifications));
    } catch (e) {
      print('Erreur de parsing de la notification: $e');
    }
  }

  Future<List<NotificationModel>> fetchNotifications() async {
    final prefs = await SharedPreferences.getInstance();
    final userId = prefs.getInt('userId');

    if (userId == null) {
      throw Exception("User ID not found in SharedPreferences");
    }

    final url = Uri.parse('${baseUrl}notifications/user/$userId');
    final response = await http.get(url);

    if (response.statusCode == 200) {
      final List<dynamic> data = json.decode(response.body);
      _notifications =
          data.map((json) => NotificationModel.fromJson(json)).toList();
      _notifications.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      _notificationsController.add(_notifications);
      return _notifications;
    } else {
      throw Exception("Failed to load notifications: ${response.body}");
    }
  }

  Future<Map<String, int>> countNotificationsByType() async {
    Map<String, int> counts = {
      'notifications': 0,
    };

    try {
      List<NotificationModel> notifications = await fetchNotifications();
      for (var notification in notifications) {
        if (!notification.isRead) {
          counts['notifications'] = counts['notifications']! + 1;
        }
      }
      return counts;
    } catch (e) {
      throw Exception("Failed to count notifications: $e");
    }
  }

  Future<void> deleteNotificationById(int id) async {
    final url = Uri.parse('${baseUrl}notifications/$id');
    final response = await http.delete(url);

    if (response.statusCode == 200) {
      _notifications.removeWhere((notification) => notification.id == id);
      _notificationsController.add(List.from(_notifications));
    } else {
      throw Exception("Failed to delete notification: ${response.body}");
    }
  }

  Future<void> markAsRead(int id) async {
    final url = Uri.parse('${baseUrl}notifications/read/$id');
    final response = await http.put(url);

    if (response.statusCode == 200) {
      _notifications = _notifications.map((notification) {
        if (notification.id == id) {
          return notification.copyWith(isRead: true);
        }
        return notification;
      }).toList();
      _notificationsController.add(List.from(_notifications));
    } else {
      throw Exception("Failed to mark notification as read: ${response.body}");
    }
  }
}
 */
import 'dart:convert';
import 'package:bee_kids_mobile/model/Notification.dart';
import 'package:bee_kids_mobile/resources/environnement/apiUrl.dart';
import 'package:bee_kids_mobile/view_model/WebSocketService.dart';
import 'package:bee_kids_mobile/view_model/tokenService.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';

class NotificationService {
  final baseUrl = ApiUrl.baseUrl;
    final TokenService tokenService = TokenService();

  final WebSocketService _webSocketService = WebSocketService();

  Stream<String> get notificationUpdates =>
      _webSocketService.notificationUpdates;

  final StreamController<List<NotificationModel>> _notificationsController =
      StreamController<List<NotificationModel>>.broadcast();

  Stream<List<NotificationModel>> get notificationsStream =>
      _notificationsController.stream;
  List<NotificationModel> _notifications = [];
  final StreamController<int> _notificationCountController =
      StreamController<int>.broadcast();
  Stream<int> get notificationCountStream =>
      _notificationCountController.stream;
  int _unreadCount = 0;

  final StreamController<String> _notificationUpdatesController =
      StreamController<String>.broadcast();

  NotificationService() {
    // Initialize the count from the server
    _initializeUnreadCount();

    // Listen to WebSocket updates for notifications
    _webSocketService.notificationUpdates.listen((notification) {
      _handleIncomingNotification(notification);
    });
  }
  Future<void> _initializeUnreadCount() async {
  //  final counts = await countNotificationsByType();
   // _unreadCount = counts['notifications']!;
    _notificationCountController.add(_unreadCount);
  }


Future<int> getLatestNotificationCounter() async {
  try {
    // Fetch all notifications
    List<NotificationModel> notifications = await fetchNotifications();
    
    // If there are no notifications, return 0
    if (notifications.isEmpty) {
      return 0;
    }
    
    // Find the notification with the highest ID
    NotificationModel latestNotification = notifications.reduce((current, next) {
      // If either ID is null, prefer the non-null one
      if (current.id == null) return next;
      if (next.id == null) return current;
      
      // Otherwise compare IDs
      return current.id! > next.id! ? current : next;
    });
    
    // Return the notification counter from the latest notification
    return latestNotification.notifcationCounter;
  } catch (e) {
    print('Error getting latest notification counter: $e');
    return 0; // Return 0 in case of error
  }
}

  Future<void> _handleIncomingNotification(String notificationJson) async {
    try {
      final parsedNotification = json.decode(notificationJson);
      final newNotification = NotificationModel.fromJson(parsedNotification);

      _notifications.insert(0, newNotification);
      _notificationsController.add(List.from(_notifications));

      // Update unread count if needed.
      if (!newNotification.isRead) {
        _unreadCount++;
        _notificationCountController.add(_unreadCount);
      }
    } catch (e) {
      print('Erreur de parsing de la notification: $e');
    }
  }

  Future<List<NotificationModel>> fetchNotifications() async {
    final prefs = await SharedPreferences.getInstance();
    final userId = prefs.getInt('userId');

    if (userId == null) {
      throw Exception("User ID not found in SharedPreferences");
    }

    final url = Uri.parse('${baseUrl}notifications/mobile/$userId');
    final response = await http.get(url);

    if (response.statusCode == 200) {
      final List<dynamic> data = json.decode(response.body);
      _notifications =
          data.map((json) => NotificationModel.fromJson(json)).toList();
      _notifications.sort((a, b) {
        final aTimestamp = a.timeStamp ?? '';
        final bTimestamp = b.timeStamp ?? '';
        return bTimestamp.compareTo(aTimestamp);
      });
      _notificationsController.add(_notifications);

      // Sync the unread count
      _unreadCount = _notifications.where((n) => !n.isRead).length;
      _notificationCountController.add(_unreadCount);

      return _notifications;
    } else {
      throw Exception("Failed to load notifications: ${response.body}");
    }
  }

 /*  Future<Map<String, int>> countNotificationsByType() async {
    Map<String, int> counts = {
      'notifications': 0,
    };

    try {
      List<NotificationModel> notifications = await fetchNotifications();
      for (var notification in notifications) {
        if (!notification.isRead) {
          counts['notifications'] = counts['notifications']! + 1;
        }
      }
      return counts;
    } catch (e) {
      throw Exception("Failed to count notifications: $e");
    }
  } */

  Future<void> deleteNotificationById(int id) async {
    final url = Uri.parse('${baseUrl}notifications/$id');
    final response = await http.delete(url);

    if (response.statusCode == 200) {
      // Check if the deleted notification was unread
      bool wasUnread = _notifications
          .any((notification) => notification.id == id && !notification.isRead);

      // Remove the notification from the list
      _notifications.removeWhere((notification) => notification.id == id);
      _notificationsController.add(List.from(_notifications));

      // If it was unread, decrement the count
      if (wasUnread) {
        _unreadCount--;
        _notificationCountController.add(_unreadCount);
      }
    } else {
      throw Exception("Failed to delete notification: ${response.body}");
    }
  }

  Future<void> markAsRead(int id) async {
   var userId =await tokenService.getId();
  
  if (userId == null) {
    throw Exception("User ID not found in SharedPreferences");
  }
  
  final url = Uri.parse('${baseUrl}notifications/$id/read?userId=$userId');
  final response = await http.put(url);

  print('mark notification as read  = ${response.statusCode} ${response.body}');
}

  }

