import 'dart:convert';

class User {
  int userId;
  String nom;
  String prenom;
  String userEmail;
  String? userPhoneNumber;
  String? cinParent;
  String sexe;
  String? travailPere;
  String? travailMere;
  String? resetPasswordToken;
  bool archived;
  String? userPhotoUrl;

  User({
    required this.userId,
    required this.nom,
    required this.prenom,
    required this.userEmail,
    this.userPhoneNumber,
    this.cinParent,
    required this.sexe,
    this.travailPere,
    this.travailMere,
    this.resetPasswordToken,
    required this.archived,
  });

  // Convert a User object to a Map
  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'nom': nom,
      'prenom': prenom,
      'userEmail': userEmail,
      'userPhoneNumber': userPhoneNumber,
      'cinParent': cinParent,
      'sexe': sexe,
      'travailPere': travailPere,
      'travailMere': travailMere,
      'resetPasswordToken': resetPasswordToken,
      'archived': archived,
    };
  }

  // Create a User object from a Map
  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      userId: map['userId'] ?? 0,
      nom: map['nom'] ?? '',
      prenom: map['prenom'] ?? '',
      userEmail: map['userEmail'] ?? '',
      userPhoneNumber: map['userPhoneNumber']?? '',
      cinParent: map['cinParent']?? '',
      sexe: map['sexe'] ?? '',
      travailPere: map['travailPere']?? 'information manquante',
      travailMere: map['travailMere']?? 'information manquante',
      resetPasswordToken: map['resetPasswordToken']?? '',
      archived: map['archived'] ?? 'false',
    );
  }

    // Create a User object from a Map (for database or other storage)


  // Convert a User object to JSON
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'nom': nom,
      'prenom': prenom,
      'userEmail': userEmail,
      'userPhoneNumber': userPhoneNumber,
      'cinParent': cinParent,
      'sexe': sexe,
      'travailPere': travailPere,
      'travailMere': travailMere,
      'resetPasswordToken': resetPasswordToken,
      'archived': archived,
    };
  }

    static Future<User> fromJson(String jsonStr) async {
    final Map<String, dynamic> json = jsonDecode(jsonStr);
    return User.fromMap(json);
  }
}
