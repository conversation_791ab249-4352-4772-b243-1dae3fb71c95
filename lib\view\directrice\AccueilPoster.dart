import 'dart:io';

import 'package:bee_kids_mobile/view_model/postService.dart';
import 'package:bee_kids_mobile/view_model/tokenService.dart';
import 'package:bee_kids_mobile/view_model/userService.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:chewie/chewie.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:http_parser/http_parser.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:http/http.dart' as http;
import 'package:video_player/video_player.dart';

class AcceuilPosterScreen extends StatefulWidget {
  const AcceuilPosterScreen({super.key});

  @override
  _AcceuilPosterScreenState createState() => _AcceuilPosterScreenState();
}

class _AcceuilPosterScreenState extends State<AcceuilPosterScreen>
    with TickerProviderStateMixin {
  final TextEditingController _contentController = TextEditingController();
  List<XFile> _selectedFiles = [];
  final ImagePicker _picker = ImagePicker();
  final UserService userService = UserService();
  String? userPhotoUrl;
  bool isLoading = true;
  XFile? _capturedImage;
  bool _isLoading = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Define responsive variables
  late double screenWidth;
  late double screenHeight;
  late double itemHeight;

  @override
  void initState() {
    super.initState();
    _fetchUserPhoto();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
        parent: _animationController, curve: Curves.easeOutCubic));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _fetchUserPhoto() async {
    try {
      final photoUrl = await userService.fetchPhotoUsersId();
      setState(() {
        userPhotoUrl = photoUrl;
        isLoading = false;
      });
    } catch (e) {
      print('Error fetching user photo: $e');
      setState(() {
        isLoading = false;
      });
    }
  }

  // Function to pick images & videos
  Future<void> _pickMedia() async {
    // Check platform
    if (Platform.isIOS) {
      // For iOS, we'll show an action sheet to let the user choose what they want to pick
      showCupertinoModalPopup(
        context: context,
        builder: (BuildContext context) => CupertinoActionSheet(
          title: const Text('Sélectionner des médias'),
          message: const Text('Choisissez le type de média à ajouter'),
          actions: <CupertinoActionSheetAction>[
            CupertinoActionSheetAction(
              onPressed: () async {
                Navigator.pop(context);
                final List<XFile> pickedImages = await _picker.pickMultiImage();
                if (pickedImages.isNotEmpty) {
                  setState(() {
                    _selectedFiles.addAll(pickedImages);
                  });
                }
              },
              child: const Text('Photos'),
            ),
            CupertinoActionSheetAction(
              onPressed: () async {
                Navigator.pop(context);
                final XFile? videoFile =
                    await _picker.pickVideo(source: ImageSource.gallery);
                if (videoFile != null) {
                  setState(() {
                    _selectedFiles.add(videoFile);
                  });
                }
              },
              child: const Text('Vidéo'),
            ),
            CupertinoActionSheetAction(
              onPressed: () async {
                Navigator.pop(context);
                // First pick images
                final List<XFile> pickedImages = await _picker.pickMultiImage();
                if (pickedImages.isNotEmpty) {
                  setState(() {
                    _selectedFiles.addAll(pickedImages);
                  });
                }

                // Then pick a video
                final XFile? videoFile =
                    await _picker.pickVideo(source: ImageSource.gallery);
                if (videoFile != null) {
                  setState(() {
                    _selectedFiles.add(videoFile);
                  });
                }
              },
              child: const Text('Photos et Vidéo'),
            ),
          ],
          cancelButton: CupertinoActionSheetAction(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('Annuler'),
          ),
        ),
      );
    } else {
      // On Android, FilePicker works well
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['jpg', 'jpeg', 'png', 'mp4'],
        allowMultiple: true,
      );

      if (result != null) {
        setState(() {
          _selectedFiles = result.paths.map((path) => XFile(path!)).toList();
        });
      }
    }
  }

  Widget _buildMediaPreview() {
    if (_selectedFiles.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Stack(
          children: [
            CarouselSlider(
              options: CarouselOptions(
                height: 200,
                aspectRatio: 16 / 9,
                viewportFraction: 1.0,
                autoPlay: false,
                enlargeCenterPage: false,
              ),
              items: _selectedFiles.map((file) {
                final fileExtension = file.path.split('.').last.toLowerCase();
                if (['jpg', 'jpeg', 'png'].contains(fileExtension)) {
                  return Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Image.file(
                      File(file.path),
                      fit: BoxFit.cover,
                    ),
                  );
                } else if (fileExtension == 'mp4') {
                  return Container(
                    width: double.infinity,
                    child: Chewie(
                      controller: ChewieController(
                        videoPlayerController:
                            VideoPlayerController.file(File(file.path))
                              ..initialize(),
                        aspectRatio: 16 / 9,
                        autoPlay: false,
                        looping: false,
                      ),
                    ),
                  );
                } else {
                  return Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.insert_drive_file,
                          size: 40,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          file.path.split('/').last,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  );
                }
              }).toList(),
            ),
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.6),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  icon: const Icon(Icons.close, color: Colors.white, size: 20),
                  onPressed: () {
                    setState(() {
                      _selectedFiles.clear();
                    });
                  },
                ),
              ),
            ),
            if (_selectedFiles.length > 1)
              Positioned(
                bottom: 8,
                right: 8,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.6),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${_selectedFiles.length} fichiers',
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  // Function to take photo using the camera
  Future<void> _takePhoto() async {
    try {
      final XFile? photo = await _picker.pickImage(source: ImageSource.camera);

      if (photo != null) {
        setState(() {
          _capturedImage = photo;
          _selectedFiles = [photo];
        });
      }
    } catch (e) {
      print('Error taking photo: $e');
    }
  }

  Future<void> _submitPost() async {
    // Hide keyboard first
    FocusScope.of(context).unfocus();

    if (_contentController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Le contenu est vide'),
          backgroundColor: Colors.orange,
          behavior: SnackBarBehavior.floating,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      int? userId = await TokenService().getId();

      if (userId == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Erreur: utilisateur non identifié'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          ),
        );
        return;
      }

      List<http.MultipartFile> files = [];
      if (_selectedFiles.isNotEmpty) {
        for (var file in _selectedFiles) {
          final mimeType = getMimeType(file.path);
          files.add(await http.MultipartFile.fromPath(
            'files',
            file.path,
            contentType: MediaType(mimeType['type']!, mimeType['subtype']!),
          ));
        }
      }

      await PostService().addPost(userId, _contentController.text, files);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Publication créée avec succès'),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );

      Navigator.pushNamed(context, '/directrice/acceuil');
    } catch (e) {
      print('Erreur lors de la création de la publication: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur: $e'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Widget _buildActionButton({
    required IconData icon,
    required String title,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, color: color, size: 22),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[800],
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey[400],
                  size: 14,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Helper function to determine MIME type
  Map<String, String> getMimeType(String path) {
    final ext = path.split('.').last.toLowerCase();
    switch (ext) {
      case 'jpg':
      case 'jpeg':
        return {'type': 'image', 'subtype': 'jpeg'};
      case 'png':
        return {'type': 'image', 'subtype': 'png'};
      case 'mp4':
        return {'type': 'video', 'subtype': 'mp4'};
      case 'pdf':
        return {'type': 'application', 'subtype': 'pdf'};
      case 'doc':
        return {'type': 'application', 'subtype': 'msword'};
      case 'docx':
        return {
          'type': 'application',
          'subtype':
              'vnd.openxmlformats-officedocument.wordprocessingml.document'
        };
      default:
        return {'type': 'application', 'subtype': 'octet-stream'};
    }
  }

  @override
  Widget build(BuildContext context) {
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;
    itemHeight = screenHeight * 0.3;

    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFB),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
          ),
          child: IconButton(
            icon: const Icon(Icons.arrow_back_ios_new,
                color: Colors.grey, size: 20),
            onPressed: () {
              Navigator.pushNamed(context, '/directrice/acceuil');
            },
          ),
        ),
        title: const Text(
          'Créer une publication',
          style: TextStyle(
            color: Color(0xFF2D3748),
            fontSize: 20,
            fontWeight: FontWeight.w700,
          ),
        ),
        centerTitle: true,
        actions: [
          Container(
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              gradient: _isLoading
                  ? null
                  : const LinearGradient(
                      colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
              color: _isLoading ? Colors.grey[300] : null,
              borderRadius: BorderRadius.circular(12),
              boxShadow: _isLoading
                  ? null
                  : [
                      BoxShadow(
                        color: const Color(0xFF667EEA).withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(12),
                onTap: _isLoading ? null : _submitPost,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Icon(
                          Icons.send_rounded,
                          color: Colors.white,
                          size: 20,
                        ),
                ),
              ),
            ),
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: Column(
            children: [
              // Scrollable content area
              Expanded(
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Column(
                    children: [
                      // User Profile Section
                      Container(
                        margin: const EdgeInsets.all(16),
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              blurRadius: 10,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Row(
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(20),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: CircleAvatar(
                                radius: 25,
                                backgroundColor: Colors.grey[200],
                                backgroundImage: isLoading
                                    ? const AssetImage(
                                            'lib/resources/images/avatar_girl.png')
                                        as ImageProvider
                                    : (userPhotoUrl != null
                                        ? NetworkImage(userPhotoUrl!)
                                        : const AssetImage(
                                            'lib/resources/images/avatar_girl.png')),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Créer une publication',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.grey[800],
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    'Partagez vos moments avec votre équipe',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[500],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Content Input Section
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 16),
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              blurRadius: 10,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: TextField(
                          controller: _contentController,
                          decoration: InputDecoration(
                            hintText: 'Quoi de neuf ? Partagez vos pensées...',
                            hintStyle: TextStyle(
                              color: Colors.grey[400],
                              fontSize: 16,
                            ),
                            border: InputBorder.none,
                            enabledBorder: InputBorder.none,
                            focusedBorder: InputBorder.none,
                          ),
                          style: const TextStyle(
                            fontSize: 16,
                            height: 1.5,
                          ),
                          maxLines: 4,
                          minLines: 2,
                        ),
                      ),

                      // Media Preview
                      _buildMediaPreview(),

                      // Add some bottom padding to ensure content doesn't get cut off
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),

              // Fixed Action Buttons Section at bottom
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(24),
                    topRight: Radius.circular(24),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 20,
                      offset: const Offset(0, -5),
                    ),
                  ],
                ),
                child: SafeArea(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        margin: const EdgeInsets.only(top: 12),
                        width: 40,
                        height: 4,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildActionButton(
                        icon: Icons.photo_library_rounded,
                        title: 'Photo / Vidéo',
                        color: const Color(0xFF10B981),
                        onTap: _pickMedia,
                      ),
                      _buildActionButton(
                        icon: Icons.videocam_rounded,
                        title: 'Live',
                        color: const Color(0xFFEF4444),
                        onTap: () {
                          Navigator.pushNamed(context, '/directrice/live');
                        },
                      ),
                      _buildActionButton(
                        icon: Icons.attach_file_rounded,
                        title: 'Ajouter un fichier',
                        color: const Color(0xFF8B5CF6),
                        onTap: () async {
                          if (Platform.isIOS) {
                            // For documents on iOS, we still need FilePicker
                            FilePickerResult? result =
                                await FilePicker.platform.pickFiles(
                              type: FileType.custom,
                              allowedExtensions: ['pdf', 'doc', 'docx'],
                            );

                            if (result != null) {
                              String filePath = result.files.single.path!;
                              setState(() {
                                _selectedFiles.add(XFile(filePath));
                              });
                            }
                          } else {
                            // Android behavior remains the same
                            FilePickerResult? result =
                                await FilePicker.platform.pickFiles(
                              type: FileType.custom,
                              allowedExtensions: ['pdf', 'mp4', 'doc', 'docx'],
                            );

                            if (result != null) {
                              String filePath = result.files.single.path!;
                              setState(() {
                                _selectedFiles.add(XFile(filePath));
                              });
                            }
                          }
                        },
                      ),
                      _buildActionButton(
                        icon: Icons.camera_alt_rounded,
                        title: 'Appareil photo',
                        color: const Color(0xFFF59E0B),
                        onTap: _takePhoto,
                      ),
                      const SizedBox(height: 16),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
