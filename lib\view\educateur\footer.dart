import 'package:flutter/material.dart';
import 'dart:async';
import 'package:bee_kids_mobile/view_model/WebSocketService.dart';
import 'package:bee_kids_mobile/view_model/notificationService.dart';
import 'package:bee_kids_mobile/view_model/messagerieService.dart'; // Add this import
import 'package:shared_preferences/shared_preferences.dart';
import 'package:bee_kids_mobile/model/Notification.dart' as custom;
import 'dart:convert';

class MyFooterEducateur extends StatefulWidget {
  final String currentRoute;

  const MyFooterEducateur(
      {super.key, this.currentRoute = '/educateur/accueil'});

  @override
  State<MyFooterEducateur> createState() => _MyFooterEducateurState();
}

class _MyFooterEducateurState extends State<MyFooterEducateur>
    with SingleTickerProviderStateMixin {
  final WebSocketService _webSocketService = WebSocketService();
  final NotificationService _notificationService = NotificationService();
  final MessagerieService _messagerieService = MessagerieService(); // Add this
  late StreamSubscription<String> _notificationSubscription;
  late StreamSubscription<int> _messageCounterSubscription; // Add this
  String? userId;
  late String _currentRoute;

  // Add counter for notifications
  int _notificationCount = 0;
  int _messageCount = 0; // Add message counter

  // Animation controller for the sliding effect
  late AnimationController _controller;
  late Animation<double> _animation;

  // Track the current and previous active index
  int _activeIndex = 0;
  int _previousIndex = 0;

  // Map routes to indices
  final Map<String, int> _routeIndices = {
    '/educateur/enAttente': 0,
    '/educateur/Discussions': 1,
    '/educateur/accueil': 2, // Home is special case
    '/educateur/menu': 3,
    '/educateur/profile': 4,
  };

  @override
  void initState() {
    super.initState();
    _currentRoute = widget.currentRoute;

    // Initialize animation controller
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Set initial active index based on current route
    _activeIndex = _routeIndices[_currentRoute] ?? 2; // Default to home

    // Initialize notification count from service
    _initializeNotificationCount();
    _initializeMessageCount(); // Add this

    _loadUserIdAndConnectWebSocket();

    // Subscribe to message counter updates
    _messageCounterSubscription =
        _messagerieService.messageCounterUpdates.listen((count) {
      print("📬 Footer received message count update: $count");
      if (mounted) {
        setState(() {
          _messageCount = count;
        });
      }
    });

    // Reset message counter if we're on the Discussions page
    if (_currentRoute == '/educateur/Discussions') {
      _messagerieService.resetMessageCounter();
    }
  }

  // Add this method to initialize the message count
  Future<void> _initializeMessageCount() async {
    try {
      // Get the message counter from shared preferences
      final prefs = await SharedPreferences.getInstance();
      int count = prefs.getInt('messageCounter') ?? 0;
      print("📬 Footer initialized message count: $count");

      if (mounted) {
        setState(() {
          _messageCount = count;
        });
      }
    } catch (e) {
      print('Error initializing message count: $e');
    }
  }

  // Add this method to initialize the notification count
  Future<void> _initializeNotificationCount() async {
    try {
      // Get the latest notification counter from the service
      int latestCounter =
          await _notificationService.getLatestNotificationCounter();

      // Update state with the counter value
      if (mounted) {
        setState(() {
          _notificationCount = latestCounter;
        });
      }
    } catch (e) {
      print('Error initializing notification count: $e');
    }
  }

  @override
  void didUpdateWidget(MyFooterEducateur oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentRoute != widget.currentRoute) {
      setState(() {
        _previousIndex = _activeIndex;
        _currentRoute = widget.currentRoute;
        _activeIndex = _routeIndices[_currentRoute] ?? 2;
      });

      // Reset message counter if navigating to Discussions
      if (_currentRoute == '/educateur/Discussions') {
        _messagerieService.resetMessageCounter();
      }

      // Animate the transition
      _animation = Tween<double>(
        begin: _previousIndex.toDouble(),
        end: _activeIndex.toDouble(),
      ).animate(CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ));

      _controller.forward(from: 0.0);
    }
  }

  Future<void> _loadUserIdAndConnectWebSocket() async {
    final prefs = await SharedPreferences.getInstance();
    var storedUserId = prefs.get('userId');
    userId = storedUserId?.toString();

    if (userId != null) {
      print('🟢 Connecting WebSocket with userId: $userId');
      _webSocketService.connect(userId!);

      _notificationSubscription =
          _webSocketService.notificationUpdates.listen((message) {
        print('🔔 New notification received: $message');

        // Parse the notification and update the counter
        try {
          var data = json.decode(message);
          custom.NotificationModel notification =
              custom.NotificationModel.fromJson(data);

          setState(() {
            // Update notification counter
            _notificationCount = notification.notifcationCounter;
          });
        } catch (e) {
          print('Error parsing notification: $e');
        }
      });

      // Listen for conversation updates to update message counter
      _webSocketService.conversationUpdates.listen((message) {
        print('💬 New message received: $message');

        try {
          var data = json.decode(message);
          if (data.containsKey('messageCounter')) {
            setState(() {
              _messageCount = data['messageCounter'] ?? 0;
            });
          }
        } catch (e) {
          print('Error parsing message: $e');
        }
      });
    } else {
      print('⚠️ No userId found in shared storage!');
    }
  }

  @override
  void dispose() {
    _notificationSubscription.cancel();
    _messageCounterSubscription.cancel(); // Cancel the subscription
    _controller.dispose();
    _webSocketService.disconnect();
    super.dispose();
  }

  void _navigateTo(String route) {
    if (_currentRoute != route) {
      setState(() {
        _previousIndex = _activeIndex;
        _currentRoute = route;
        _activeIndex = _routeIndices[route] ?? 2;
      });

      // Reset message counter if navigating to Discussions
      if (route == '/educateur/Discussions') {
        _messagerieService.resetMessageCounter();
      }

      // Animate the transition
      _animation = Tween<double>(
        begin: _previousIndex.toDouble(),
        end: _activeIndex.toDouble(),
      ).animate(CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ));

      _controller.forward(from: 0.0);

      // Navigate to the new route
      Navigator.pushReplacementNamed(context, route);
    }
  }

  Widget _buildIconButton({
    required IconData icon,
    required String route,
    required int index,
    int badgeCount = 0,
  }) {
    final bool isActive = _currentRoute == route;

    return Stack(
      clipBehavior: Clip.none,
      children: [
        GestureDetector(
          onTap: () => _navigateTo(route),
          child: Container(
            width: 50,
            height: 50,
            decoration: isActive
                ? BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.green.shade400, Colors.green.shade800],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.green.withOpacity(0.3),
                        spreadRadius: 1,
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  )
                : null,
            child: IconButton(
              color: isActive ? Colors.white : Colors.green[800],
              icon: Icon(icon),
              onPressed: () => _navigateTo(route),
            ),
          ),
        ),

        // Badge for notification count
        if (badgeCount > 0)
          Positioned(
            right: 0,
            top: 0,
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(10),
              ),
              constraints: const BoxConstraints(
                minWidth: 16,
                minHeight: 16,
              ),
              child: Text(
                badgeCount > 99 ? '99+' : badgeCount.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildHomeButton() {
    final bool isActive = _currentRoute == '/educateur/accueil';

    return Transform.translate(
      offset: const Offset(0, -20), // Move up by 20 pixels
      child: GestureDetector(
        onTap: () => _navigateTo('/educateur/accueil'),
        child: Container(
          height: 60,
          width: 60,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isActive
                  ? [Colors.green.shade400, Colors.green.shade800]
                  : [Colors.white, Colors.white],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.green.withOpacity(0.3),
                spreadRadius: 2,
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
            border: Border.all(
              color: Colors.white,
              width: 3,
            ),
          ),
          child: IconButton(
            icon: Icon(
              Icons.home,
              color: isActive ? Colors.white : Colors.green[800],
              size: 30,
            ),
            onPressed: () => _navigateTo('/educateur/accueil'),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        Container(
          color: Colors.white,
          height: 60,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              // Left side icons with badges
              _buildIconButton(
                icon: Icons.notifications,
                route: '/educateur/enAttente',
                index: 0,
                badgeCount: _notificationCount, // Notification count badge
              ),
              _buildIconButton(
                icon: Icons.chat_bubble_outline,
                route: '/educateur/Discussions',
                index: 1,
                badgeCount: _messageCount, // Message count badge
              ),

              // Empty space for the centered home button
              const SizedBox(width: 60),

              // Right side icons
              _buildIconButton(
                icon: Icons.menu,
                route: '/educateur/menu',
                index: 3,
              ),
              _buildIconButton(
                icon: Icons.settings,
                route: '/educateur/profile',
                index: 4,
              ),
            ],
          ),
        ),
        // Centered elevated home button
        _buildHomeButton(),
      ],
    );
  }
}
