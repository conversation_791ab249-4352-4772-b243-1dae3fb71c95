class LikeDTO {
  final int userId;
  final String userName;
  final bool liked;

  LikeDTO({
    required this.userId,
    required this.userName,
    required this.liked,
  });

  factory LikeDTO.fromJson(Map<String, dynamic> json) {
    return LikeDTO(
      userId: json['userId'],
      userName: json['userName'],
      liked: json['liked'],
    );
  }
}

class UserLikeDTO {
  final int userId;
  final String nom;
  final String prenom;
  final String userEmail;
  final String? userPhoneNumber;
  final String? userPhotoUrl;

  UserLikeDTO({
    required this.userId,
    required this.nom,
    required this.prenom,
    required this.userEmail,
    this.userPhoneNumber,
    this.userPhotoUrl,
  });

  factory UserLikeDTO.fromJson(Map<String, dynamic> json) {
    return UserLikeDTO(
      userId: json['userId'] ?? 0,
      nom: json['nom'] ?? '',
      prenom: json['prenom'] ?? '',
      userEmail: json['userEmail'] ?? '',
      userPhoneNumber: json['userPhoneNumber'],
      userPhotoUrl: json['userPhotoUrl'] ?? json['photoUrl'], // Essayer les deux noms
    );
  }

  String get fullName => '$prenom $nom';
  
  // Getter pour avoir une URL par défaut si pas de photo
  String get photoUrlOrDefault => userPhotoUrl ?? 'https://via.placeholder.com/150';
}