import 'package:bee_kids_mobile/view/login.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';


void main() {
  group('LoginScreen Widget Tests', () {
    testWidgets('Renders login form elements', (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(home: LoginScreen()));

      expect(find.text('Connexion'), findsOneWidget);
      expect(find.byIcon(Icons.person), findsOneWidget);
      expect(find.byIcon(Icons.lock), findsOneWidget);
      expect(find.text('Se connecter'), findsOneWidget);
      expect(find.text('Mot de passe oublié?'), findsOneWidget);
    });

    testWidgets('Shows error on empty fields', (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(home: LoginScreen()));

      await tester.tap(find.text('Se connecter'));
      await tester.pumpAndSettle();

      expect(find.text('Tous les champs sont requis'), findsOneWidget);
    });

    testWidgets('Shows error on invalid email', (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(home: LoginScreen()));

      await tester.enterText(find.widgetWithText(TextField, 'E-mail'), 'invalid-email');
      await tester.enterText(find.widgetWithText(TextField, 'Mot de passe'), 'password123');
      await tester.tap(find.text('Se connecter'));
      await tester.pumpAndSettle();

      expect(find.text('Veuillez entrer une adresse email valide'), findsOneWidget);
    });

    testWidgets('Shows error on short password', (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(home: LoginScreen()));

      await tester.enterText(find.widgetWithText(TextField, 'E-mail'), '<EMAIL>');
      await tester.enterText(find.widgetWithText(TextField, 'Mot de passe'), '12345');
      await tester.tap(find.text('Se connecter'));
      await tester.pumpAndSettle();

      expect(find.text('Le mot de passe doit contenir au moins 6 caractères'), findsOneWidget);
    });

    testWidgets('Password visibility toggle works', (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(home: LoginScreen()));

      expect(find.byIcon(Icons.visibility_off), findsOneWidget);
      
      await tester.tap(find.byIcon(Icons.visibility_off));
      await tester.pumpAndSettle();
      
      expect(find.byIcon(Icons.visibility), findsOneWidget);
    });
  });
}
