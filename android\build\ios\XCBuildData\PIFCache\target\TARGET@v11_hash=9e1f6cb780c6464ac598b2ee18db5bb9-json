{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ae529f76a1aeb84aed676cbc438f2337", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98aed4621d10f1f4d79a6422fd12349d5b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987c0ad1a68339e983c25e4de21965f4a8", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9814c051d99b8e2de9c37087a27b16234e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987c0ad1a68339e983c25e4de21965f4a8", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981c408f462becf19dda40d82fe7374f0f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e61c3706c4c5522b7ecd10e962efb922", "guid": "bfdfe7dc352907fc980b868725387e9851470e87a52ea663f4d68b5170ea4cca", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9879113b12e3100c80546749e3fee57aaa", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986cd8b63f1efadf20d9ea1ed8f5a55c59", "guid": "bfdfe7dc352907fc980b868725387e98acc3c26a16be9c9451ce50c6aca8e560"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d637eecdfef95bbdf30369120352bd46", "guid": "bfdfe7dc352907fc980b868725387e980bd266c0b3fff74b79702686f1867044"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834be2bf990d387cbce4913d90fc0866d", "guid": "bfdfe7dc352907fc980b868725387e984c6ad483f54fd33c82bf7a16d457e5c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984767f7ebe824e56e6af065bd8c127fb1", "guid": "bfdfe7dc352907fc980b868725387e98a9e0e96fee9ade1e4dcf86654aa97349"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98051e74ac2dfb008bbd683e30491aa7be", "guid": "bfdfe7dc352907fc980b868725387e98bbbc231619aeeb331ac8989fe44a5b47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982675e10da08f11651d5e552f26200541", "guid": "bfdfe7dc352907fc980b868725387e983b1a653d65c5831d0cb75b526d169764"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834f1d1fb0dd3d6538989f7502ec1644c", "guid": "bfdfe7dc352907fc980b868725387e98b0b2c68667ed2adab12420384836c58f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825db5c0b0de22f7488309691c8541b75", "guid": "bfdfe7dc352907fc980b868725387e982938007a3df62a68c415b19ab01fcec6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982aa0b02535812fd0e603a89bcfb30ef5", "guid": "bfdfe7dc352907fc980b868725387e986e63a7119826904382f84cf9adc2f94e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b13723b07b108638ef45e70068037a0f", "guid": "bfdfe7dc352907fc980b868725387e985184bc06c848af49ab46baf38f4cb505"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983aecacec943b3b7fb13ca4f783aeb124", "guid": "bfdfe7dc352907fc980b868725387e980f40773b074723393fb79ede59bf9625"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982683856913254da4512b6e02f6e4f14f", "guid": "bfdfe7dc352907fc980b868725387e98870b7a65425d2f5fd1ba82b00385e09d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9552c70edc7387c0167f655a2f9cab9", "guid": "bfdfe7dc352907fc980b868725387e988645d334ef7f4f211a4487b925f21eff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0333415fb47d981a93980d7783a25bc", "guid": "bfdfe7dc352907fc980b868725387e98453ddd184715ff8e294f872822b9ce92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864c787672805f0b55134cb199dc71c0e", "guid": "bfdfe7dc352907fc980b868725387e9811736060b65c30a9842f2ff582124d0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98850d9e8cfb22b221e8e383807ea38a07", "guid": "bfdfe7dc352907fc980b868725387e989383cfeae5b1a52e4bbd37fddb397082"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5b79d94ff93ec63566064eb357dbe2c", "guid": "bfdfe7dc352907fc980b868725387e98ca6bb229733bb2d53e9def8330d5ce0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830b4739ebde111a52547b4d18a503410", "guid": "bfdfe7dc352907fc980b868725387e98be7bd84f5bfe2349d290225b8f80ed01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d2693191bcef035b93392dabd29e915", "guid": "bfdfe7dc352907fc980b868725387e98356c78fe37ed11035a482a72b60295ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7133de307e021954fb1ea816636ae53", "guid": "bfdfe7dc352907fc980b868725387e98e6ce5801fbacc1de4db34fd6ec211499"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817c885c72c438eb48d3c624d21c32879", "guid": "bfdfe7dc352907fc980b868725387e9809ecd997684ac384b4235bf0cfad052d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986504caedde90e4b7bbf90bc3c93b56f5", "guid": "bfdfe7dc352907fc980b868725387e983c85b09f80577ec93aed1216e5a21d0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98119755cf3c3ab367d61b1f6a2b919fbf", "guid": "bfdfe7dc352907fc980b868725387e9810b885198365afba76e72f431b48180d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcde9c1fdc32784294392718f0d45ce0", "guid": "bfdfe7dc352907fc980b868725387e982754e67b2153860665fc0df6cb4736ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e630bb3219120e5c9a7785f505fc0eba", "guid": "bfdfe7dc352907fc980b868725387e981e04534c849dc573ed24934788f6e0cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3eb35d57bc96b724d16fb6a815089bd", "guid": "bfdfe7dc352907fc980b868725387e9884681563bff501a87948aa0b56399476"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98807401bfb1caf2d2d87935ce9df53b6e", "guid": "bfdfe7dc352907fc980b868725387e98ed6a9120fa6318eac004cc4657c76bca"}], "guid": "bfdfe7dc352907fc980b868725387e984c4b920ea5eb881623573447b0ed9d97", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987245d663eb126d58dc3bffa3f993fb1d", "guid": "bfdfe7dc352907fc980b868725387e98971eed4d8d23d01edb4037a07b2f6587"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813d077fbf01bac8382743594b271c07f", "guid": "bfdfe7dc352907fc980b868725387e9824235afe2ddf881f7c8ea8eb207225a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d56474ef5850c087d24d24a69093581f", "guid": "bfdfe7dc352907fc980b868725387e98b74687495ed7dea665d09966783847c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834f426257763bc4b24489402369e6806", "guid": "bfdfe7dc352907fc980b868725387e982fb83d3a7a71996db63d4ba04ed82e56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0372a857a824418dfb3d5a853ed269f", "guid": "bfdfe7dc352907fc980b868725387e98ce7c714ccd1d98adbd6fe6608603ba3a"}], "guid": "bfdfe7dc352907fc980b868725387e9859c10890194d5387e9ced22caf08a3c2", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98be810be1b4be756ff392b9e6441c615c", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e98b38efb39d29b846b5b4e7049666bd4b1", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}