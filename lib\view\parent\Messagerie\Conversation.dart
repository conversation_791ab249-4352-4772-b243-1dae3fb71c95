import 'package:bee_kids_mobile/model/messagerie.dart';
import 'package:flutter/material.dart';
import 'package:bee_kids_mobile/view_model/messagerieService.dart';
import 'package:bee_kids_mobile/model/FullConversationDTO.dart';
import 'package:bee_kids_mobile/view/parent/footer.dart';
import 'package:intl/intl.dart';
import 'package:bee_kids_mobile/view_model/WebSocketService.dart';
import 'dart:convert';

class ConversationScreenparent extends StatefulWidget {
  final String currentUserId;
  final String conversationId;
  final String recipientUserName;
  final String recipientUserId;
  final String recipientPhotoUrl;

  const ConversationScreenparent({
    Key? key,
    required this.currentUserId,
    required this.conversationId,
    required this.recipientUserName,
    required this.recipientUserId,
    required this.recipientPhotoUrl,
  }) : super(key: key);

  @override
  _ConversationScreenState createState() => _ConversationScreenState();
}

class _ConversationScreenState extends State<ConversationScreenparent> {
  final MessagerieService _messagerieService = MessagerieService();
  final WebSocketService _webSocketService =
      WebSocketService(); // Instantiate WebSocketService
  final TextEditingController _messageController = TextEditingController();
  late ScrollController _scrollController;
  List<FullConversationDTO> _messages = [];
  bool _isLoading = false;
  bool _loadingConversation = true; // Loader pour l'historique
  bool _isLoadingMore = false;
  bool _hasMoreMessages = true;
  int _currentPage = 0;
  final int _pageSize = 20;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_scrollListener);
    _loadConversationPaginated();
    _initializeWebSocket();
    _messagerieService.resetMessageCounter();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  // Initialise le WebSocket et configure l'écoute des nouveaux messages
  void _initializeWebSocket() {
    _webSocketService.conversationUpdates.listen((message) {
      print("Nouveau message reçu: $message");

      // Convertir le message JSON en FullConversationDTO
      final Map<String, dynamic> messageData = jsonDecode(message);

      // Handle the timestamp format difference
      if (messageData.containsKey('timeStamp') &&
          messageData['timeStamp'] is String) {
        // Convert ISO 8601 timestamp to Unix timestamp in milliseconds
        final DateTime dateTime = DateTime.parse(messageData['timeStamp']);
        messageData['timestamp'] = dateTime.millisecondsSinceEpoch.toString();
      }

      final newMessage = FullConversationDTO.fromJson(messageData);

      // Vérifier si le message existe déjà pour éviter les doublons
      bool exists = _messages.any((m) =>
          m.timestamp == newMessage.timestamp &&
          m.content == newMessage.content);
      if (!exists) {
        setState(() {
          _messages.add(newMessage); // Ajout en bas (pour reverse: false)
        });
      }

      // Auto-scroll vers le bas pour voir le nouveau message
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    });

    _webSocketService.connect(widget.currentUserId);
  }

  // Ajoutez cette méthode pour détecter quand charger plus de messages
  void _scrollListener() {
    // Charger plus de messages quand on approche du haut de la liste
    if (_scrollController.position.pixels < 100) {
      if (!_isLoadingMore && _hasMoreMessages) {
        _loadMoreMessages();
      }
    }
  }

  // Remplacez la méthode _loadConversation() par celle-ci
  void _loadConversationPaginated() async {
    if (widget.conversationId.isEmpty || widget.conversationId == 'null') {
      print('Invalid conversationId: ${widget.conversationId}');
      setState(() {
        _messages = [];
        _loadingConversation = false;
      });
      return;
    }

    setState(() {
      _loadingConversation = true;
      _currentPage = 0;
    });

    try {
      // Récupérer la conversation paginée via l'API
      final result = await _messagerieService.getConversationPaginated(
        userConnectedId: int.parse(widget.currentUserId),
        conversationId: int.parse(widget.conversationId),
        page: _currentPage,
        size: _pageSize,
      );

      // Marquer tous les messages non lus comme lus
      await _messagerieService.markAsRead(int.parse(widget.conversationId));

      setState(() {
        // Inverser l'ordre des messages pour afficher les plus anciens en haut
        List<FullConversationDTO> sortedMessages = result['messages'];
        sortedMessages.sort(
            (a, b) => int.parse(a.timestamp).compareTo(int.parse(b.timestamp)));
        _messages = sortedMessages;
        _hasMoreMessages = result['hasNext'];
        _loadingConversation = false;
      });

      // Auto-scroll vers le bas pour voir les messages les plus récents
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
        }
      });
    } catch (e) {
      print('Error loading conversation: $e');
      setState(() {
        _loadingConversation = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load messages: ${e.toString()}')),
      );
    }
  }

  // Ajoutez cette méthode pour charger plus de messages
  void _loadMoreMessages() async {
    if (!_hasMoreMessages || _isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      final nextPage = _currentPage + 1;
      final result = await _messagerieService.getConversationPaginated(
        userConnectedId: int.parse(widget.currentUserId),
        conversationId: int.parse(widget.conversationId),
        page: nextPage,
        size: _pageSize,
      );

      List<FullConversationDTO> newMessages = result['messages'];
      // Trier les nouveaux messages par ordre chronologique
      newMessages.sort(
          (a, b) => int.parse(a.timestamp).compareTo(int.parse(b.timestamp)));

      // Déterminer où insérer les nouveaux messages
      // Pour l'ordre chronologique, les nouveaux messages plus anciens vont au début
      setState(() {
        _messages.insertAll(0, newMessages);
        _currentPage = nextPage;
        _hasMoreMessages = result['hasNext'];
        _isLoadingMore = false;
      });
    } catch (e) {
      print('Error loading more messages: $e');
      setState(() {
        _isLoadingMore = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text('Failed to load more messages: ${e.toString()}')),
      );
    }
  }

  // Check if messages are from different days
  bool _isNewDay(int timestamp1, int timestamp2) {
    final DateTime date1 = DateTime.fromMillisecondsSinceEpoch(timestamp1);
    final DateTime date2 = DateTime.fromMillisecondsSinceEpoch(timestamp2);

    return date1.year != date2.year ||
        date1.month != date2.month ||
        date1.day != date2.day;
  }

  // Build date separator with French formatting
  Widget _buildDateSeparator(int timestamp) {
    final DateTime messageDate = DateTime.fromMillisecondsSinceEpoch(timestamp);
    final DateTime now = DateTime.now();
    final DateTime yesterday = DateTime.now().subtract(const Duration(days: 1));

    String dateText;
    if (messageDate.year == now.year &&
        messageDate.month == now.month &&
        messageDate.day == now.day) {
      dateText = "Aujourd'hui";
    } else if (messageDate.year == yesterday.year &&
        messageDate.month == yesterday.month &&
        messageDate.day == yesterday.day) {
      dateText = "Hier";
    } else {
      // Format français: jour de la semaine, jour mois
      final String dayName = _getFrenchDayName(messageDate.weekday);
      final String monthName = _getFrenchMonthName(messageDate.month);
      dateText = "$dayName ${messageDate.day} $monthName";
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Expanded(child: Divider(color: Colors.grey[300])),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12.0),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                dateText,
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          Expanded(child: Divider(color: Colors.grey[300])),
        ],
      ),
    );
  }

  // Get French day name
  String _getFrenchDayName(int weekday) {
    switch (weekday) {
      case 1:
        return "Lundi";
      case 2:
        return "Mardi";
      case 3:
        return "Mercredi";
      case 4:
        return "Jeudi";
      case 5:
        return "Vendredi";
      case 6:
        return "Samedi";
      case 7:
        return "Dimanche";
      default:
        return "";
    }
  }

  // Get French month name
  String _getFrenchMonthName(int month) {
    switch (month) {
      case 1:
        return "janvier";
      case 2:
        return "février";
      case 3:
        return "mars";
      case 4:
        return "avril";
      case 5:
        return "mai";
      case 6:
        return "juin";
      case 7:
        return "juillet";
      case 8:
        return "août";
      case 9:
        return "septembre";
      case 10:
        return "octobre";
      case 11:
        return "novembre";
      case 12:
        return "décembre";
      default:
        return "";
    }
  }

  // Envoi d'un message via WebSocket et via l'API
  void _sendMessage() async {
    if (_messageController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Message cannot be empty')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // Check if conversationId is valid
    String? conversationId;
    if (widget.conversationId.isNotEmpty &&
        widget.conversationId != 'null' &&
        widget.conversationId != '0') {
      conversationId = widget.conversationId;
    }

    final message = Message(
      senderId: widget.currentUserId,
      receiverId: widget.recipientUserId,
      content: _messageController.text.trim(),
      status: 'sent',
      timestamp: DateTime.now().millisecondsSinceEpoch.toString(),
      read: false,
      conversationId: conversationId != null ? int.parse(conversationId) : null,
    );

    print('Sending message: ${message.toJson()}');

    final jsonMessage = jsonEncode(message.toJson());

    try {
      // Envoi via WebSocket pour une mise à jour instantanée
      _webSocketService.sendMessage('/app/messages', jsonMessage);

      // Envoi via l'API également
      await _messagerieService.sendMessage(message);

      setState(() {
        _messageController.clear();
        // Ajouter le message envoyé à la fin de la liste
        bool exists = _messages.any((m) =>
            m.timestamp == message.timestamp && m.content == message.content);
        if (!exists) {
          _messages.add(
            FullConversationDTO(
              content: message.content ?? '',
              status: message.status ?? '',
              timestamp: message.timestamp ?? '0',
              isRead: false,
              fullName: widget.recipientUserName,
              photoUrl: widget.recipientPhotoUrl,
            ),
          );
        }
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to send message: ${e.toString()}')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });

      // Auto-scroll vers le bas pour afficher le message le plus récent
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.green,
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pushNamed(context, '/parent/Discussions'),
        ),
        backgroundColor: Colors.green,
        elevation: 0,
        title: const Text(
          "Messagerie",
          style: TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: Stack(
        children: [
          Center(
            child: Container(
              margin: const EdgeInsets.all(16.0),
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Column(
                children: [
                  // Profile photo and name
                  Column(
                    children: [
                      CircleAvatar(
                        radius: 30,
                        backgroundImage: NetworkImage(widget.recipientPhotoUrl),
                        backgroundColor: Colors.grey[200],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        widget.recipientUserName,
                        style: const TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  // Message list with loading indicator
                  Expanded(
                    child: _loadingConversation
                        ? const Center(child: CircularProgressIndicator())
                        : _messages.isEmpty
                            ? const Center(
                                child: Text(
                                  'Écrivez un nouveau message pour commencer la conversation.',
                                  style: TextStyle(
                                      fontSize: 16, color: Colors.black54),
                                ),
                              )
                            : ListView.builder(
                                controller: _scrollController,
                                reverse:
                                    false, // Afficher les anciens messages en haut
                                itemCount: _hasMoreMessages
                                    ? _messages.length + 1
                                    : _messages.length,
                                itemBuilder: (context, index) {
                                  // Show loading indicator at the beginning of the list
                                  if (_hasMoreMessages && index == 0) {
                                    return const Center(
                                      child: Padding(
                                        padding: EdgeInsets.all(8.0),
                                        child: CircularProgressIndicator(
                                            strokeWidth: 2),
                                      ),
                                    );
                                  }

                                  // Ajuster l'index pour tenir compte de l'indicateur de chargement
                                  final messageIndex =
                                      _hasMoreMessages ? index - 1 : index;
                                  if (messageIndex < 0 ||
                                      messageIndex >= _messages.length) {
                                    return const SizedBox.shrink();
                                  }

                                  final message = _messages[messageIndex];
                                  final isSentByUser = message.status == "sent";

                                  // Déterminer si on doit afficher le profil
                                  final bool showProfile = messageIndex == 0 ||
                                      (messageIndex > 0 &&
                                          message.fullName !=
                                              _messages[messageIndex - 1]
                                                  .fullName);

                                  final bool hasPhotoAndName =
                                      message.photoUrl != null &&
                                          message.photoUrl!.isNotEmpty &&
                                          message.fullName != null &&
                                          message.fullName!.isNotEmpty;

                                  return Padding(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 2.0),
                                    child: Column(
                                      children: [
                                        // Séparateur de date en français
                                        if (messageIndex == 0 ||
                                            (messageIndex > 0 &&
                                                _isNewDay(
                                                    int.parse(
                                                        message.timestamp),
                                                    int.parse(_messages[
                                                            messageIndex - 1]
                                                        .timestamp))))
                                          _buildDateSeparator(
                                              int.parse(message.timestamp)),

                                        // Message row with profile and content
                                        Row(
                                          mainAxisAlignment: isSentByUser
                                              ? MainAxisAlignment.end
                                              : MainAxisAlignment.start,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.end,
                                          children: [
                                            // Photo de profil plus petite (style Messenger)
                                            if (!isSentByUser &&
                                                showProfile &&
                                                hasPhotoAndName)
                                              Padding(
                                                padding: const EdgeInsets.only(
                                                    right: 4.0, bottom: 4.0),
                                                child: Column(
                                                  children: [
                                                    Container(
                                                      width: 28, // Plus petit
                                                      height: 28, // Plus petit
                                                      decoration: BoxDecoration(
                                                        shape: BoxShape.circle,
                                                        border: Border.all(
                                                            color: Colors.white,
                                                            width: 1),
                                                        boxShadow: [
                                                          BoxShadow(
                                                            color: Colors.black
                                                                .withOpacity(
                                                                    0.1),
                                                            spreadRadius: 1,
                                                            blurRadius: 2,
                                                            offset:
                                                                const Offset(
                                                                    0, 1),
                                                          ),
                                                        ],
                                                      ),
                                                      child: ClipRRect(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(14),
                                                        child: Image.network(
                                                          message.photoUrl!,
                                                          fit: BoxFit.cover,
                                                          errorBuilder: (context,
                                                                  error,
                                                                  stackTrace) =>
                                                              const Icon(
                                                                  Icons.person,
                                                                  size: 16,
                                                                  color: Colors
                                                                      .grey),
                                                        ),
                                                      ),
                                                    ),
                                                    // Nom sous la photo (optionnel, style Messenger)
                                                    if (showProfile &&
                                                        hasPhotoAndName)
                                                      Container(
                                                        margin: const EdgeInsets
                                                            .only(top: 2),
                                                        constraints:
                                                            const BoxConstraints(
                                                                maxWidth: 60),
                                                        child: Text(
                                                          message.fullName!,
                                                          style:
                                                              const TextStyle(
                                                            fontSize:
                                                                9, // Plus petit
                                                            fontWeight:
                                                                FontWeight.w500,
                                                            color:
                                                                Colors.black54,
                                                          ),
                                                          overflow: TextOverflow
                                                              .ellipsis,
                                                          textAlign:
                                                              TextAlign.center,
                                                        ),
                                                      ),
                                                  ],
                                                ),
                                              ),

                                            // Bulle de message style Messenger
                                            Flexible(
                                              child: Container(
                                                constraints: BoxConstraints(
                                                  maxWidth:
                                                      MediaQuery.of(context)
                                                              .size
                                                              .width *
                                                          0.65,
                                                ),
                                                margin: EdgeInsets.only(
                                                  bottom: 2,
                                                  top: 2,
                                                  left: isSentByUser
                                                      ? 50.0
                                                      : (!showProfile ||
                                                              !hasPhotoAndName)
                                                          ? 32.0
                                                          : 0.0,
                                                  right: isSentByUser
                                                      ? 0.0
                                                      : 110.0,
                                                ),
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 12,
                                                        vertical: 8),
                                                decoration: BoxDecoration(
                                                  color: isSentByUser
                                                      ? Colors.green[
                                                          400] // Green for sent messages (matching app theme)
                                                      : const Color(
                                                          0xFFF1F0F0), // Light gray for received messages
                                                  borderRadius:
                                                      BorderRadius.circular(18),
                                                ),
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    // Contenu du message
                                                    Text(
                                                      message.content,
                                                      style: TextStyle(
                                                        fontSize: 15,
                                                        color: isSentByUser
                                                            ? Colors.white
                                                            : Colors.black87,
                                                        height: 1.3,
                                                      ),
                                                    ),
                                                    const SizedBox(height: 3),

                                                    // Heure et statut de lecture
                                                    Align(
                                                      alignment:
                                                          Alignment.bottomRight,
                                                      child: Row(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        children: [
                                                          Text(
                                                            DateFormat('HH:mm')
                                                                .format(
                                                              DateTime.fromMillisecondsSinceEpoch(
                                                                  int.parse(message
                                                                      .timestamp)),
                                                            ),
                                                            style: TextStyle(
                                                              fontSize: 10,
                                                              color: isSentByUser
                                                                  ? Colors
                                                                      .white70
                                                                  : Colors
                                                                      .black45,
                                                            ),
                                                          ),
                                                          const SizedBox(
                                                              width: 3),
                                                          if (isSentByUser)
                                                            Icon(
                                                              message.isRead
                                                                  ? Icons
                                                                      .done_all
                                                                  : Icons.done,
                                                              color: message
                                                                      .isRead
                                                                  ? Colors.white
                                                                  : Colors
                                                                      .white70,
                                                              size: 12,
                                                            ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar:
          const MyFooterParent(currentRoute: '/parent/Discussions'),
    );
  }
}
