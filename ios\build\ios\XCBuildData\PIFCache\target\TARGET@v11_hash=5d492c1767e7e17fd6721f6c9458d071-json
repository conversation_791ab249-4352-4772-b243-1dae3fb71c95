{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9879b8845e46062a60f3494c7d69731451", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984dde6edd28def30f0161757e52bcd849", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e0efb80fbab0638fb969840cb9750591", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982884516c17b000e1398d1c53a025c4fd", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e0efb80fbab0638fb969840cb9750591", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98adc9cc4dd52770ec0135999397ce5cb3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e334d8e5287c72fdfcb55e27d37681ba", "guid": "bfdfe7dc352907fc980b868725387e98ea311d90804f61a9fd7c1e84df8491a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cc980b0382792609dbc1dce2001d424", "guid": "bfdfe7dc352907fc980b868725387e981ee635f24163f9472be0463d7b3f3c50", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb5f6d3c83a82467c222c1dda4c1cf5f", "guid": "bfdfe7dc352907fc980b868725387e986e119764b99e21e49695fd38084cee6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986364061e5d3bd8287427a48d1a0fdc5f", "guid": "bfdfe7dc352907fc980b868725387e9805354fa487d075bdd23af235774808de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98112adafc9c6c2415e8903c376a4b1e74", "guid": "bfdfe7dc352907fc980b868725387e9878b3c73e401303c43dc18f50810da3ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803f967ba663ce2d1622643e13dd4ca3a", "guid": "bfdfe7dc352907fc980b868725387e9823e7fc9cd32eb7c34653e381c7b9fb45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98133006e5caeb5acf32f01f616b722464", "guid": "bfdfe7dc352907fc980b868725387e98ec590b82d6c44fde497397ae8728e007"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98299529717ab9435d5d7d0f2b2b690ae2", "guid": "bfdfe7dc352907fc980b868725387e988a2bdd2ab618188604f1ce56c5c54e7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98968b127a7edade1fb5adbdfc5eb7ffd0", "guid": "bfdfe7dc352907fc980b868725387e98c6888a357cf216429e352c122e6652cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e592d7b9d5f2aba425165b30572cca9d", "guid": "bfdfe7dc352907fc980b868725387e98879b5b3a6f3e47847220d4897d4aa143"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983019888cacd1f266707124d9fdbc7c65", "guid": "bfdfe7dc352907fc980b868725387e989c928765347545eb7fa2d9c101b19ec4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877148553fa23c830f50791cfcc60e6ec", "guid": "bfdfe7dc352907fc980b868725387e98d06914377d1d8ac7f5e1325f06e2dbe9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c82ca3ad6d114455369a69d52ff6238", "guid": "bfdfe7dc352907fc980b868725387e9895f5c497fcf851c9787c3aa22024c688"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0eaed26fa851409de695b81cc5c64b6", "guid": "bfdfe7dc352907fc980b868725387e98ab2779fd9a8ae912ea0981be12cdc2c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de7dbb6a2598ca79f158afa11fe636f2", "guid": "bfdfe7dc352907fc980b868725387e982f3ce6f9c4fc01ddd4a18219137aa0a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985064813fbe78e3ef9336a7dc2ad47496", "guid": "bfdfe7dc352907fc980b868725387e9839d11df105bac4cfd1568ee6c8273fc7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804c489f8f030806563c0289dedb272f8", "guid": "bfdfe7dc352907fc980b868725387e98ab351be848ab1f48d3d5bb11a33605c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98152dbc0883d84853bbe9f7b07a0a96d8", "guid": "bfdfe7dc352907fc980b868725387e98a57084f1c4cbe08ec21370be9416df27", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da98a6fa7aefb4276caea98c5228de81", "guid": "bfdfe7dc352907fc980b868725387e98720205d3d388d8cbf180f1c2f0300779", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845c6f4022377ffed91fe5e2d764e285c", "guid": "bfdfe7dc352907fc980b868725387e987b555cff766e918afef890cadf47ebcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e86c2f78ddca75c358af24c25ab45cc4", "guid": "bfdfe7dc352907fc980b868725387e98162b776d6c4675bf939e2ffba86564e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873d277a8fa2122dcbe14381e496c4132", "guid": "bfdfe7dc352907fc980b868725387e9878486f0044cf626ec2285b0f8aa4d5b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d7bac6f79e2361afd48c557ceb48fed", "guid": "bfdfe7dc352907fc980b868725387e9854e489ae5a2bb3a25eab22ab290bf1c2", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98ecdd95a04e490ecbb6bb98c8b898e61d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9874b31374122c8beccdce45bf71b2ab68", "guid": "bfdfe7dc352907fc980b868725387e983e99976efdd0f552c414f94556285f5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98992c3f3cad9ed7178291472da6b227af", "guid": "bfdfe7dc352907fc980b868725387e982259f268d949561fdb943528cc3f113f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c710a1c5416e897fea59b217da168b1", "guid": "bfdfe7dc352907fc980b868725387e981f2b88152775c2fc7fbe6648044f96c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffd41136f87cfcb42bfe50ed1100f64d", "guid": "bfdfe7dc352907fc980b868725387e98c947a565fa8d3da8ec0e5603cb308ed3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835e15908514a79357fcb83efe25431b9", "guid": "bfdfe7dc352907fc980b868725387e981a63c212bc9879b92b6b743cc9e1f55a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b83f7ec12801c64de2b6ed909ab5af1", "guid": "bfdfe7dc352907fc980b868725387e98300e8040ce8bb97cb11c4e9cc36d1d25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982093116664f0989acab410a8909eaced", "guid": "bfdfe7dc352907fc980b868725387e98de737a973ebc8af439d0cde953df3d7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d24966406f4b496bc2631a7c264d6671", "guid": "bfdfe7dc352907fc980b868725387e98d30ee3cf7d65a25d174be839fd6a25c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d729c4ed6776894e0b27b985a09154af", "guid": "bfdfe7dc352907fc980b868725387e9809e08af80a2fea9e3b9a355a51a3b14a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4fc0809db82993048e975cd24fb04c9", "guid": "bfdfe7dc352907fc980b868725387e9819180a0090d5757c776b29d147d766cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816e43b89268dddcf332f6ffa913cacf3", "guid": "bfdfe7dc352907fc980b868725387e98b6a65515f8f5f2bc3ecf3fdfaaabe4bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98558b42dba858b22aebf077c54c40e434", "guid": "bfdfe7dc352907fc980b868725387e982f1ccecdcffbe085d4cbb1b5f53dc19b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c09ec55049ef44da8ee57d93e1863fbb", "guid": "bfdfe7dc352907fc980b868725387e985f60e0822956e6afc7853f7adc92d5d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988177857292abc876eaefeb3834708fba", "guid": "bfdfe7dc352907fc980b868725387e9838943bd6cb3e5388bf20dd418f7ff0ae"}], "guid": "bfdfe7dc352907fc980b868725387e98e00ac5e37fe70eb114d7f69aa8d612f7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e98f27ff7faef4c674b44747975d38c5538"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f04dcb6e648519c233ae8161727f1d09", "guid": "bfdfe7dc352907fc980b868725387e981b4d03b17a22088f81cbca56b23be39f"}], "guid": "bfdfe7dc352907fc980b868725387e98b5675a440df961acfaf47f5aca3192fb", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984238e55b165120b061043dad14f9f821", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98484c2e36d324c1dae3e1764320ccb0ee", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}