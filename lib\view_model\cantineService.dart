import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:bee_kids_mobile/model/cantine.dart';
import 'package:path_provider/path_provider.dart';
import '../view_model/tokenService.dart';
import '../resources/environnement/apiUrl.dart';

class cantineService {
  final String baseUrl = ApiUrl.baseUrl;
  TokenService tokenService;
  final http.Client httpClient;

  cantineService({
    TokenService? tokenService,
    http.Client? httpClient,
  })  : this.tokenService = tokenService ?? TokenService(),
        this.httpClient = httpClient ?? http.Client();

  Future<Map<String, String>> getAuthHeaders() async {
    final token = await tokenService.getToken();
    return {
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json; charset=UTF-8',
    };
  }

  Future<Map<String, String>> fetchCurrentWeek() async {
    try {
      final headers = await getAuthHeaders();
      final response = await httpClient
          .get(Uri.parse('${baseUrl}api/week/current'), headers: headers);

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = jsonDecode(response.body);
        return {
          'startOfWeek': data['startOfWeek'],
          'endOfWeek': data['endOfWeek'],
        };
      } else {
        throw Exception('Failed to load current week: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching current week: $e');
    }
  }

  Future<List<cantine>> fetchMenus() async {
    final headers = await getAuthHeaders();
    final response =
        await httpClient.get(Uri.parse('${baseUrl}menus'), headers: headers);
    if (response.statusCode == 200) {
      final List<dynamic> data =
          jsonDecode(utf8.decode(response.bodyBytes)); // Décodage UTF-8
      return data.map((json) => cantine.fromJson(json)).toList();
    } else {
      throw Exception('Failed to load menus: ${response.statusCode}');
    }
  }

  Future<void> createMenu({
    required String jour,
    required String entree,
    required String platPrincipale,
    required String dessert,
    required File photo,
  }) async {
    try {
      final token = await tokenService.getToken();
      var uri = Uri.parse('${baseUrl}menus');
      var request = http.MultipartRequest('POST', uri)
        ..headers['Authorization'] = 'Bearer $token'
        ..fields['jour'] = jour
        ..fields['entree'] = entree
        ..fields['platPrincipale'] = platPrincipale
        ..fields['dessert'] = dessert
        ..files.add(await http.MultipartFile.fromPath('photo', photo.path));

      final response = await request.send();

      if (response.statusCode != 201) {
        final errorMessage = await response.stream.bytesToString();
        throw Exception('Failed to create menu: $errorMessage');
      }
    } catch (e) {
      throw Exception('Error creating menu: $e');
    }
  }

  Future<File> getImageFileFromUrl(String imageUrl) async {
    final response = await http.get(Uri.parse(imageUrl));
    final documentDirectory = await getApplicationDocumentsDirectory();
    final filePath =
        '${documentDirectory.path}/${DateTime.now().millisecondsSinceEpoch}.jpg';
    final file = File(filePath);
    await file.writeAsBytes(response.bodyBytes);
    return file;
  }
}
