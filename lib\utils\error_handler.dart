import 'dart:io';
import 'package:flutter/material.dart';
import 'logger.dart';

/// Custom exception classes for better error handling
class NetworkException implements Exception {
  final String message;
  final int? statusCode;
  
  NetworkException(this.message, {this.statusCode});
  
  @override
  String toString() => 'NetworkException: $message${statusCode != null ? ' (Status: $statusCode)' : ''}';
}

class ValidationException implements Exception {
  final String message;
  final Map<String, String>? fieldErrors;
  
  ValidationException(this.message, {this.fieldErrors});
  
  @override
  String toString() => 'ValidationException: $message';
}

class ServiceException implements Exception {
  final String message;
  final String? service;
  
  ServiceException(this.message, {this.service});
  
  @override
  String toString() => 'ServiceException${service != null ? ' ($service)' : ''}: $message';
}

/// Global error handler utility
class ErrorHandler {
  /// Handle and log errors, return user-friendly message
  static String handleError(Object error, {String? context}) {
    String userMessage;
    
    if (error is NetworkException) {
      userMessage = _handleNetworkError(error);
    } else if (error is ValidationException) {
      userMessage = error.message;
    } else if (error is ServiceException) {
      userMessage = 'Erreur de service: ${error.message}';
    } else if (error is SocketException) {
      userMessage = 'Problème de connexion internet. Vérifiez votre connexion.';
    } else if (error is FormatException) {
      userMessage = 'Erreur de format des données.';
    } else {
      userMessage = 'Une erreur inattendue s\'est produite.';
    }
    
    // Log the error
    AppLogger.error(
      'Error${context != null ? ' in $context' : ''}: $userMessage',
      error: error,
      stackTrace: StackTrace.current,
    );
    
    return userMessage;
  }
  
  static String _handleNetworkError(NetworkException error) {
    switch (error.statusCode) {
      case 400:
        return 'Requête invalide. Vérifiez les données saisies.';
      case 401:
        return 'Session expirée. Veuillez vous reconnecter.';
      case 403:
        return 'Accès non autorisé.';
      case 404:
        return 'Ressource non trouvée.';
      case 500:
        return 'Erreur du serveur. Veuillez réessayer plus tard.';
      case 503:
        return 'Service temporairement indisponible.';
      default:
        return 'Erreur de connexion. Veuillez réessayer.';
    }
  }
  
  /// Show error snackbar
  static void showErrorSnackBar(BuildContext context, Object error, {String? errorContext}) {
    final message = handleError(error, context: errorContext);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        action: SnackBarAction(
          label: 'OK',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }
  
  /// Validate required fields
  static void validateRequired(Map<String, dynamic> fields) {
    final errors = <String, String>{};
    
    fields.forEach((key, value) {
      if (value == null || (value is String && value.trim().isEmpty)) {
        errors[key] = 'Ce champ est obligatoire';
      }
    });
    
    if (errors.isNotEmpty) {
      throw ValidationException('Veuillez remplir tous les champs obligatoires', fieldErrors: errors);
    }
  }
  
  /// Validate network response
  static void validateResponse(int statusCode, String body, {String? operation}) {
    if (statusCode < 200 || statusCode >= 300) {
      throw NetworkException(
        'Échec de l\'opération${operation != null ? ' $operation' : ''}',
        statusCode: statusCode,
      );
    }
  }
}
