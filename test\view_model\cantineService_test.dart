import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:http/http.dart' as http;
import '../../lib/view_model/cantineService.dart';
import '../../lib/view_model/tokenService.dart';

class MockTokenService extends Mock implements TokenService {}
class MockHttpClient extends Mock implements http.Client {}

void main() {
  late MockTokenService mockTokenService;
  late MockHttpClient mockHttpClient;
  late cantineService service;

  setUpAll(() {
    registerFallbackValue(Uri.parse('http://example.com'));
  });

  setUp(() {
    mockTokenService = MockTokenService();
    mockHttpClient = MockHttpClient();
    service = cantineService(
      tokenService: mockTokenService,
      httpClient: mockHttpClient,
    );
  });

  group('CantineService Tests', () {
    test('fetchMenus should return list of menus on success', () async {
      when(() => mockTokenService.getToken())
          .thenAnswer((_) async => 'test_token');

      final mockResponse = [
        {
          'jour': '2024-01-01',
          'entree': 'Salade',
          'platPrincipale': 'Steak',
          'dessert': 'Fruit',
          'photo': 'photo_url'
        }
      ];

      when(() => mockHttpClient.get(any(), headers: any(named: 'headers')))
          .thenAnswer((_) async => http.Response(json.encode(mockResponse), 200));

      final result = await service.fetchMenus();
      
      expect(result, isA<List>());  
      expect(result.length, 1);
      expect(result.first.jour, '2024-01-01');
    });

    test('fetchMenus should throw exception on error', () async {
      when(() => mockTokenService.getToken())
          .thenAnswer((_) async => 'test_token');
          
      when(() => mockHttpClient.get(any(), headers: any(named: 'headers')))
          .thenAnswer((_) async => http.Response('Error', 400));

      expect(() => service.fetchMenus(), throwsException);
    });

    test('authentication headers should be correct', () async {
      when(() => mockTokenService.getToken())
          .thenAnswer((_) async => 'test_token');

      final headers = await service.getAuthHeaders();
      
      expect(headers['Authorization'], 'Bearer test_token');
      expect(headers['Content-Type'], 'application/json');
    });
  });
}