class Comment {
  final int id;
  String commentaire;
  final String nomUtilisateur;
  final String prenomUtilisateur;
  final DateTime createdAt;

  /// field to track if the comment is in edit mode
  bool editMode;
  final String? userPhotoUrl;
  final int? userId;
  String? tempContent;

  Comment({
    required this.id,
    required this.commentaire,
    required this.nomUtilisateur,
    required this.prenomUtilisateur,
    required this.createdAt,
    this.editMode = false,
    this.userPhotoUrl,
    this.userId,
  });

  // Factory method for creating a Comment from JSON
  factory Comment.fromJson(Map<String, dynamic> json) {
    return Comment(
      id: json['id'],
      commentaire: json['commentaire'] ?? '',
      nomUtilisateur: json['nomUtilisateur'] ?? 'Utilisateur inconnu',
      prenomUtilisateur: json['prenomUtilisateur'] ?? '',
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toString()),
      userPhotoUrl: json['userPhotoUrl'],
      userId: json['userId'],
    );
  }

  // Convert a Comment to JSON (optional, useful if you want to create new comments)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'commentaire': commentaire,
      'nomUtilisateur': nomUtilisateur,
      'prenomUtilisateur': prenomUtilisateur,
      'createdAt': createdAt.toIso8601String(),
      'userPhotoUrl': userPhotoUrl,
      'userId': userId,
    };
  }
}
