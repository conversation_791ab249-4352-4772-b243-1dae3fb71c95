import 'dart:convert';
import 'dart:io';
import 'package:bee_kids_mobile/resources/environnement/apiUrl.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:bee_kids_mobile/model/classe.dart';
import 'package:bee_kids_mobile/view_model/classeService.dart';
import 'package:bee_kids_mobile/view_model/EmploiService.dart';
import 'package:bee_kids_mobile/view/educateur/footer.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:file_picker/file_picker.dart';

typedef EmploiData = Map<String, dynamic>;

class EmploiEducateur extends StatefulWidget {
  final int? id;
  final String nomClasse;
  const EmploiEducateur({
    Key? key,
    required this.id,
    required this.nomClasse,
  }) : super(key: key);

  @override
  _EmploiEducateurState createState() => _EmploiEducateurState();
}

class PDFViewerScreen extends StatefulWidget {
  final String filePath;

  const PDFViewerScreen({Key? key, required this.filePath}) : super(key: key);

  @override
  _PDFViewerScreenState createState() => _PDFViewerScreenState();
}

class _PDFViewerScreenState extends State<PDFViewerScreen> {
  bool _isLoading = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Emploi du temps',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.green,
        elevation: 5,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Stack(
        children: [
          PDFView(
            filePath: widget.filePath,
            enableSwipe: true,
            swipeHorizontal: false,
            autoSpacing: true,
            pageFling: true,
            onRender: (pages) {
              setState(() {
                _isLoading = false;
              });
              debugPrint('PDF rendu avec $pages pages');
            },
            onError: (error) {
              debugPrint('Erreur lors de l\'ouverture du PDF : $error');
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content:
                      Text('Erreur : Impossible d\'ouvrir le fichier PDF.'),
                  backgroundColor: Colors.red,
                ),
              );
              setState(() {
                _isLoading = false;
              });
            },
            onPageChanged: (page, total) {
              debugPrint('Page actuelle : $page / $total');
            },
          ),
          if (_isLoading)
            const Center(
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }
}

class _EmploiEducateurState extends State<EmploiEducateur> {
  final EmploiService _emploiService = EmploiService();
  final classeService _classeService = classeService();

  List<Classe> classes = [];
  EmploiData? dernierEmploi;
  bool isLoading = true;
  bool hasError = false;
  String errorMessage = '';
  bool _isLoading = false;
  bool _isConsulting = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    try {
      await _fetchLatestEmploiByClasseId(widget.id!, widget.nomClasse!);
    } catch (e) {
      _handleError('Erreur d\'initialisation', e);
    }
  }

  void _handleError(String message, dynamic error) {
    debugPrint('$message: $error');
    if (mounted) {
      setState(() {
        hasError = true;
        errorMessage = message;
        isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _fetchLatestEmploiByClasseId(int id, String classeName) async {
    try {
      setState(() {
        isLoading = true;
        hasError = false;
        errorMessage = '';
      });

      final emploi = await _emploiService.getLatestEmploiByClasseId(id);

      setState(() {
        dernierEmploi = emploi;
        isLoading = false;
      });
    } catch (e) {
      debugPrint('Erreur lors du chargement du dernier emploi : $e');
      setState(() {
        hasError = true;
        errorMessage =
            'Erreur lors du chargement du dernier emploi : ${e.toString()}';
        isLoading = false;
      });
    }
  }

  Future<void> _downloadFile(
      BuildContext context, String url, String fileName) async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) => AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: const [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Téléchargement en cours...'),
            ],
          ),
        ),
      );

      String? path;

      if (Platform.isAndroid) {
        path = '/storage/emulated/0/Download/$fileName';
      } else if (Platform.isIOS) {
        Directory appDocDir = await getApplicationDocumentsDirectory();
        path = '${appDocDir.path}/$fileName';
      }

      Dio dio = Dio();

      await dio.download(
        url,
        path!,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            debugPrint(
                'Progress: ${(received / total * 100).toStringAsFixed(0)}%');
          }
        },
      );

      if (context.mounted) Navigator.pop(context);

      _showNotificationDialog(
          context, 'Fichier téléchargé avec succès', Colors.green);

      // Optional: Open or share the file
      if (Platform.isIOS) {
        OpenFile.open(path); // Use share_plus or open_file to preview or share
      }
    } catch (e) {
      debugPrint('Erreur lors du téléchargement: $e');

      if (context.mounted) Navigator.pop(context);

      _showNotificationDialog(
          context, 'Erreur lors du téléchargement', Colors.red);
    }
  }

  void _showNotificationDialog(
      BuildContext context, String message, Color color) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Row(
            children: [
              Icon(
                color == Colors.green ? Icons.check_circle : Icons.error,
                color: color,
              ),
              SizedBox(width: 10),
              Expanded(child: Text(message)),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: Text('OK'),
            ),
          ],
        );
      },
    );
  }

  void _showDetailsPopup(EmploiData emploi) {
    if (emploi['name'] == null || emploi['filePath'] == null) {
      debugPrint('Données manquantes pour l\'emploi : $emploi');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Détails incomplets pour cet emploi.')),
      );
      return;
    }
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Détails de l\'emploi',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: SingleChildScrollView(
            child: Stack(
              // Use Stack to overlay the loader
              children: [
                Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      utf8.decode(latin1.encode('Nom : ${emploi['name']}')),
                      style:
                          TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 10),
                    Text(
                      utf8.decode(
                          latin1.encode('Classe : ${emploi['classeName']}')),
                      style:
                          TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 10),
                    if (emploi['filePath'] != null &&
                        emploi['filePath'].isNotEmpty)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Fichier associé :',
                            style: TextStyle(
                                fontSize: 16, fontWeight: FontWeight.bold),
                          ),
                          SizedBox(height: 14),
                          ElevatedButton.icon(
                            icon: Icon(Icons.visibility),
                            label: Text('Consulter'),
                            onPressed: _isConsulting
                                ? null
                                : () async {
                                    print(
                                        'Bouton "Consulter" cliqué'); // Vérification de l'événement de clic
                                    setState(() {
                                      _isConsulting =
                                          true; // Désactiver le bouton
                                    });

                                    try {
                                      final filePath = emploi['filePath'] ?? '';
                                      final url = filePath.startsWith('https')
                                          ? filePath
                                          : '${ApiUrl.baseUrl}${filePath.startsWith('//') ? '' : '/'}$filePath';

                                      final directory =
                                          await getApplicationDocumentsDirectory();
                                      final localFile = File(
                                          '${directory.path}/${url.split('/').last}');

                                      if (!localFile.existsSync()) {
                                        Dio dio = Dio();
                                        await dio.download(url, localFile.path);
                                      }

                                      if (context.mounted) {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) =>
                                                PDFViewerScreen(
                                                    filePath: localFile.path),
                                          ),
                                        );
                                      }
                                    } catch (e) {
                                      debugPrint(
                                          'Erreur lors de l\'ouverture du fichier : $e');
                                      if (context.mounted) {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          SnackBar(
                                            content: Text(
                                                'Erreur : Impossible de consulter le fichier.'),
                                            backgroundColor: Colors.red,
                                          ),
                                        );
                                      }
                                    } finally {
                                      setState(() {
                                        _isConsulting =
                                            false; // Réactiver le bouton
                                      });
                                      print(
                                          'État de _isConsulting : $_isConsulting'); // Vérification de l'état après l'opération
                                    }
                                  },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ],
                      )
                    else
                      Text(
                        'Aucun fichier associé.',
                        style: TextStyle(
                            fontSize: 16, fontStyle: FontStyle.italic),
                      ),
                  ],
                ),
                if (_isLoading) // Display the loader when isLoading is true
                  Center(
                    child: CircularProgressIndicator(),
                  ),
              ],
            ),
          ),
          actions: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                ElevatedButton.icon(
                  icon: Icon(Icons.download),
                  label: Text('Télécharger'),
                  onPressed: () async {
                    try {
                      final filePath = emploi['filePath'] ?? '';
                      final url = filePath.startsWith('http')
                          ? filePath
                          : '${ApiUrl.baseUrl}${filePath.startsWith('/') ? '' : '/'}$filePath';

                      debugPrint('URL générée pour le téléchargement : $url');

                      final fileName = url.split('/').last;

                      await _downloadFile(context, url, fileName);
                    } catch (e) {
                      debugPrint('Erreur lors du téléchargement : $e');
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                                'Erreur : Impossible de télécharger le fichier.'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
                SizedBox(height: 8),
                SizedBox(height: 8),
                ElevatedButton(
                  child: Text('Fermer'),
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Emploi du temps',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.green,
        elevation: 5,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : hasError
              ? Center(child: Text(errorMessage))
              : dernierEmploi == null
                  ? const Center(
                      child: Text(
                        'Aucun emploi crée.',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    )
                  : GridView.builder(
                      padding:
                          const EdgeInsets.all(16), // Ajout d'un padding global
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        crossAxisSpacing: 16,
                        mainAxisSpacing: 16,
                        childAspectRatio: 1,
                      ),
                      itemCount: 1, // Afficher un seul emploi pour l'instant
                      itemBuilder: (context, index) {
                        return Container(
                          decoration: BoxDecoration(
                            color: const Color.fromARGB(255, 241, 128, 153),
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.shade400,
                                blurRadius: 6,
                                offset: const Offset(0, 3),
                              ),
                            ],
                          ),
                          child: InkWell(
                            onTap: () {
                              _showDetailsPopup(dernierEmploi!);
                            },
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment
                                    .center, // Centrage vertical
                                crossAxisAlignment: CrossAxisAlignment
                                    .center, // Centrage horizontal
                                children: [
                                  Text(
                                    utf8.decode(
                                        latin1.encode(dernierEmploi!['name']!)),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 18,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 8),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
      bottomNavigationBar:
          const MyFooterEducateur(currentRoute: '/educateur/menu'),
    );
  }
}
