import 'dart:convert';
import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:http/http.dart' as http; 
import 'package:bee_kids_mobile/view_model/tokenService.dart';
import 'package:bee_kids_mobile/view_model/EmploiService.dart';
import 'package:mockito/annotations.dart';
import 'package:bee_kids_mobile/resources/environnement/apiUrl.dart';
import 'dart:async';

// Mock des classes nécessaires
class MockTokenService extends Mock implements TokenService {}
class MockHttpClient extends Mock implements http.Client {}

void main() {
  late MockTokenService mockTokenService;
  late MockHttpClient mockHttpClient;
  late EmploiService emploiService;

  setUp(() {
    mockTokenService = MockTokenService();
    mockHttpClient = MockHttpClient();
    emploiService = EmploiService(); // Injecter le service dans le constructeur si nécessaire
  });

  group('EmploiService Tests', () {
    test('getAllEmplois should return a list of emplois when successful', () async {
      // Simulation du comportement de TokenService
      when(() => mockTokenService.getToken()).thenAnswer((_) async => 'test_token');
      
      // Simulation de la réponse HTTP
      final mockResponse = [
        {'id': 1, 'name': 'Emploi 1'},
        {'id': 2, 'name': 'Emploi 2'},
      ];

      when(() => mockHttpClient.get(any(), headers: any(named: 'headers')))
          .thenAnswer((_) async => http.Response(jsonEncode(mockResponse), 200));

      // Appel à la méthode getAllEmplois
      final emplois = await emploiService.getAllEmplois();

      // Vérifications
      expect(emplois, isA<List<Map<String, dynamic>>>());
      expect(emplois.length, 2);
      expect(emplois[0]['name'], 'Emploi 1');
    });

    test('getAllEmplois should throw an exception when the status code is not 200', () async {
      when(() => mockTokenService.getToken()).thenAnswer((_) async => 'test_token');

      when(() => mockHttpClient.get(any(), headers: any(named: 'headers')))
          .thenAnswer((_) async => http.Response('Error', 400));

      expect(() => emploiService.getAllEmplois(), throwsA(isA<Exception>()));
    });

    test('getEmploiById should return an emploi when successful', () async {
      when(() => mockTokenService.getToken()).thenAnswer((_) async => 'test_token');
      
      final mockResponse = {'id': 1, 'name': 'Emploi 1'};

      when(() => mockHttpClient.get(any(), headers: any(named: 'headers')))
          .thenAnswer((_) async => http.Response(jsonEncode(mockResponse), 200));

      final emploi = await emploiService.getEmploiById(1);

      expect(emploi, isA<Map<String, dynamic>>());
      expect(emploi['name'], 'Emploi 1');
    });

    test('getEmploiById should throw an exception when the status code is not 200', () async {
      when(() => mockTokenService.getToken()).thenAnswer((_) async => 'test_token');

      when(() => mockHttpClient.get(any(), headers: any(named: 'headers')))
          .thenAnswer((_) async => http.Response('Error', 404));

      expect(() => emploiService.getEmploiById(1), throwsA(isA<Exception>()));
    });

    test('createEmploi should create an emploi successfully', () async {
      when(() => mockTokenService.getToken()).thenAnswer((_) async => 'test_token');

      final mockResponse = {'id': 1, 'name': 'Emploi 1'};

      when(() => mockHttpClient.post(any(), headers: any(named: 'headers'), body: any(named: 'body')))
          .thenAnswer((_) async => http.Response(jsonEncode(mockResponse), 201));

      final file = File('path/to/file');
      final emploi = await emploiService.createEmploi('Emploi 1', file, 1);

      expect(emploi, isA<Map<String, dynamic>>());
      expect(emploi['name'], 'Emploi 1');
    });

    test('deleteEmploi should delete an emploi successfully', () async {
      when(() => mockTokenService.getToken()).thenAnswer((_) async => 'test_token');

      when(() => mockHttpClient.delete(any(), headers: any(named: 'headers')))
          .thenAnswer((_) async => http.Response('Deleted', 200));

      await emploiService.deleteEmploi(1); // Aucune exception attendue
    });
  });
}
