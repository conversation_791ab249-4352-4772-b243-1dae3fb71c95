import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp();

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'BEEKIDS',
      home: SplashScreen(),
    );
  }
}

class SplashScreen extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _checkVersion();
  }

  Future<void> _checkVersion() async {
    final remoteConfig = FirebaseRemoteConfig.instance;

    await remoteConfig.setConfigSettings(RemoteConfigSettings(
      fetchTimeout: const Duration(seconds: 10),
      minimumFetchInterval: const Duration(hours: 1),
    ));

    await remoteConfig.fetchAndActivate();

    final minVersion = remoteConfig.getString('min_app_version');
    final androidUrl = remoteConfig.getString('android_store_url');
    final iosUrl = remoteConfig.getString('ios_store_url');

    final packageInfo = await PackageInfo.fromPlatform();
    final currentVersion = packageInfo.version;

    print("Current version: $currentVersion — Min required: $minVersion");

    if (_isVersionLower(currentVersion, minVersion)) {
      _showForceUpdateDialog(androidUrl, iosUrl);
    } else {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (_) => const HomeScreen()),
      );
    }
  }

  bool _isVersionLower(String current, String min) {
    List<int> currentParts = current.split('.').map(int.parse).toList();
    List<int> minParts = min.split('.').map(int.parse).toList();

    for (int i = 0; i < minParts.length; i++) {
      if (currentParts.length <= i || currentParts[i] < minParts[i]) {
        return true;
      } else if (currentParts[i] > minParts[i]) {
        return false;
      }
    }
    return false;
  }

  void _showForceUpdateDialog(String androidUrl, String iosUrl) {
    showDialog(
      context: context,
      barrierDismissible: false, // Bloquant
      builder: (context) {
        return AlertDialog(
          title: const Text("Mise à jour requise"),
          content: const Text(
              "Veuillez mettre à jour l'application pour continuer."),
          actions: [
            TextButton(
              onPressed: () async {
                final url = Theme.of(context).platform == TargetPlatform.iOS
                    ? iosUrl
                    : androidUrl;
                if (await canLaunchUrl(Uri.parse(url))) {
                  await launchUrl(Uri.parse(url),
                      mode: LaunchMode.externalApplication);
                }
              },
              child: const Text("Mettre à jour"),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(child: CircularProgressIndicator()),
    );
  }
}

class HomeScreen extends StatelessWidget {
  const HomeScreen();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Accueil BEEKIDS")),
      body: const Center(child: Text("Bienvenue dans BEEKIDS")),
    );
  }
}
