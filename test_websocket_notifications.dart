import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:bee_kids_mobile/view_model/WebSocketService.dart';
import 'package:bee_kids_mobile/view_model/notificationService.dart';
import 'package:bee_kids_mobile/model/Notification.dart';

/// Test page pour vérifier le fonctionnement des notifications WebSocket en temps réel
class WebSocketNotificationTest extends StatefulWidget {
  const WebSocketNotificationTest({Key? key}) : super(key: key);

  @override
  _WebSocketNotificationTestState createState() => _WebSocketNotificationTestState();
}

class _WebSocketNotificationTestState extends State<WebSocketNotificationTest> {
  final WebSocketService _webSocketService = WebSocketService();
  final NotificationService _notificationService = NotificationService();
  
  List<String> _receivedMessages = [];
  List<NotificationModel> _notifications = [];
  StreamSubscription? _webSocketSubscription;
  StreamSubscription? _notificationSubscription;
  bool _isConnected = false;
  String _connectionStatus = "Déconnecté";

  @override
  void initState() {
    super.initState();
    _initializeTest();
  }

  void _initializeTest() {
    // Test de connexion WebSocket
    _checkConnectionStatus();
    
    // Écouter les messages WebSocket bruts
    _webSocketSubscription = _webSocketService.notificationUpdates.listen(
      (message) {
        print("🧪 TEST: Message WebSocket reçu: $message");
        setState(() {
          _receivedMessages.insert(0, message);
          if (_receivedMessages.length > 10) {
            _receivedMessages.removeLast();
          }
        });
      },
      onError: (error) {
        print("🧪 TEST: Erreur WebSocket: $error");
      }
    );

    // Écouter les notifications traitées
    _notificationSubscription = _notificationService.notificationsStream.listen(
      (notifications) {
        print("🧪 TEST: Notifications traitées reçues: ${notifications.length}");
        setState(() {
          _notifications = notifications;
        });
      },
      onError: (error) {
        print("🧪 TEST: Erreur notifications: $error");
      }
    );

    // Vérifier le statut de connexion périodiquement
    Timer.periodic(Duration(seconds: 5), (timer) {
      if (mounted) {
        _checkConnectionStatus();
      } else {
        timer.cancel();
      }
    });
  }

  void _checkConnectionStatus() {
    bool connected = _webSocketService.isConnected();
    setState(() {
      _isConnected = connected;
      _connectionStatus = connected ? "Connecté" : "Déconnecté";
    });
  }

  void _forceReconnect() {
    print("🧪 TEST: Forçage de la reconnexion WebSocket");
    _webSocketService.forceReconnect();
    _checkConnectionStatus();
  }

  void _simulateNotification() {
    // Simuler une notification pour tester
    Map<String, dynamic> testNotification = {
      "notificationId": DateTime.now().millisecondsSinceEpoch,
      "userId": 3, // ID utilisateur test
      "title": "Test Notification",
      "message": "Ceci est une notification de test",
      "isMenu": true,
      "isRead": false,
      "timeStamp": DateTime.now().toIso8601String(),
      "notifcationCounter": _notifications.length + 1,
    };

    String jsonMessage = jsonEncode(testNotification);
    print("🧪 TEST: Simulation d'une notification: $jsonMessage");
    
    // Traiter directement via le service de notifications
    _notificationService.fetchNotifications();
  }

  @override
  void dispose() {
    _webSocketSubscription?.cancel();
    _notificationSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Test WebSocket Notifications"),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Statut de connexion
            Card(
              color: _isConnected ? Colors.green.shade100 : Colors.red.shade100,
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(
                      _isConnected ? Icons.wifi : Icons.wifi_off,
                      color: _isConnected ? Colors.green : Colors.red,
                    ),
                    SizedBox(width: 8),
                    Text(
                      "Statut WebSocket: $_connectionStatus",
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: _isConnected ? Colors.green.shade800 : Colors.red.shade800,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 16),
            
            // Boutons de test
            Row(
              children: [
                ElevatedButton(
                  onPressed: _forceReconnect,
                  child: Text("Reconnecter"),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
                ),
                SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _simulateNotification,
                  child: Text("Test Notification"),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
                ),
              ],
            ),
            
            SizedBox(height: 16),
            
            // Messages WebSocket bruts
            Text(
              "Messages WebSocket reçus (${_receivedMessages.length}):",
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Expanded(
              flex: 1,
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ListView.builder(
                  itemCount: _receivedMessages.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: EdgeInsets.all(8),
                      child: Text(
                        _receivedMessages[index],
                        style: TextStyle(fontSize: 12, fontFamily: 'monospace'),
                      ),
                    );
                  },
                ),
              ),
            ),
            
            SizedBox(height: 16),
            
            // Notifications traitées
            Text(
              "Notifications traitées (${_notifications.length}):",
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Expanded(
              flex: 1,
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ListView.builder(
                  itemCount: _notifications.length,
                  itemBuilder: (context, index) {
                    final notification = _notifications[index];
                    return ListTile(
                      leading: Icon(
                        notification.isRead ? Icons.mark_email_read : Icons.mark_email_unread,
                        color: notification.isRead ? Colors.grey : Colors.blue,
                      ),
                      title: Text(notification.content),
                      subtitle: Text(notification.messageContent),
                      trailing: Text(
                        notification.timeStamp ?? "",
                        style: TextStyle(fontSize: 10),
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
