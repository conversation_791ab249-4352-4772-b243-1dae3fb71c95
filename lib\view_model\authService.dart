import 'dart:convert';
import 'package:bee_kids_mobile/routes/app_routes.dart';
import 'package:bee_kids_mobile/view_model/WebSocketService.dart';
import 'package:bee_kids_mobile/view_model/firebase_service.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import '../resources/environnement/apiUrl.dart';
import '../view_model/tokenService.dart';

class Authservice extends GetxController {
  final TokenService tokenService = TokenService();

  // Get headers with Authorization token
  Future<Map<String, String>> _getAuthHeaders() async {
    final token = await tokenService.getToken();
    if (token != null) {
      return {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };
    }
    return {
      'Content-Type': 'application/json',
    };
  }

// Update Password
  Future<void> updatePassword(BuildContext context, String oldPassword,
      String newPassword, String confirmPassword) async {
    final headers = await _getAuthHeaders();
    final baseUrl = ApiUrl.baseUrl;
    int? userId = await TokenService().getId();
    final url = Uri.parse("${baseUrl}users/$userId/change-password");

    final response = await http.put(
      url,
      headers: headers,
      body: jsonEncode({
        "oldPassword": oldPassword,
        "newPassword": newPassword,
        "confirmPassword": confirmPassword
      }),
    );

    if (response.statusCode == 200) {
      String encodedRes = utf8.decode(latin1.encode(response.body));

      final responseData = jsonDecode(response.body);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Votre mot de passe a ete reinsialiser avec succes'),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
        ),
      );
      Navigator.pop(context);
    } else {
      final responseData = jsonDecode(response.body);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(responseData.toString()),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  ////////////Reset password
  Future<void> resetPassword(BuildContext context, String userEmail,
      String resetPasswordtoken, String newPassword) async {
    final baseUrl = ApiUrl.baseUrl;
    final url = Uri.parse("${baseUrl}auth/reset-password");

    try {
      final response = await http.post(
        url,
        headers: {"Content-Type": "application/json"},
        body: jsonEncode({
          "userEmail": userEmail,
          "resetPasswordToken": resetPasswordtoken,
          "newPassword": newPassword
        }),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData == true) {
          // Password reset successful
          Navigator.pop(context);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Mot de passe réinitialisé avec succès'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );
        } else {
          throw Exception("Échec de la réinitialisation du mot de passe");
        }
      } else {
        throw Exception("Erreur serveur: ${response.statusCode}");
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur: ${e.toString()}'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

//Generate reset password token
  Future<void> generateResetPasswordToken(
      BuildContext context, String userEmail) async {
    final baseUrl = ApiUrl.baseUrl;

    final url = Uri.parse("${baseUrl}auth/generate/$userEmail");
    try {
      final response = await http.post(
        url,
        headers: <String, String>{
          "Content-Type": "application/json",
        },
        body: jsonEncode({"userEmail": userEmail}),
      );

      if (response.statusCode == 200) {
//print('response.body => ${response.body}');
        final responseData = jsonDecode(response.body);
//print('responseData => $responseData');
      } else {
        throw Exception("Invalid response data");
      }
    } catch (e) {
      print(e);
    }
  }

  Future<bool> login(
      BuildContext context, String userEmail, String userPassword) async {
    final baseUrl = ApiUrl.baseUrl;
    final url = Uri.parse("${baseUrl}auth/signin");
    final tokenService = TokenService();

    try {
      final response = await http.post(
        url,
        headers: <String, String>{
          "Content-Type": "application/json",
        },
        body:
            jsonEncode({"userEmail": userEmail, "userPassword": userPassword}),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final String role = responseData['roles'][0]; // Rôle de l'utilisateur
        final String? token = responseData['accessToken'];
        final int? userId = responseData['userId'];
        final String userName = responseData['userName'] ?? '';
        print(
            'Login successful - ROLE: $role, UserID: $userId, Token exists: ${token != null}');

        if (token != null && userId != null) {
          await tokenService.saveToken(token, userId, userName, role);
          String CurrentUserId = userId.toString();
          // Connect to WebSocket after successful login
          WebSocketService().connect(CurrentUserId);
          final fcmToken = await FirebaseMessaging.instance.getToken();
          print ("Auth FCM Token: $fcmToken");
          await FirebaseService().registerFCMTokenWithBackend(fcmToken!);

          // Redirection basée sur le rôle
          if (role == 'Directeur') {
            Navigator.pushReplacementNamed(context, AppRoutes.directriceHome);
          } else if (role == 'Parent') {
            Navigator.pushReplacementNamed(context, AppRoutes.parentHome);
          } else if (role == 'Formateur') {
            Navigator.pushReplacementNamed(context, AppRoutes.educateurHome);
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Rôle non reconnu : $role'),
                backgroundColor: Colors.red,
                behavior: SnackBarBehavior.floating,
              ),
            );
            return false;
          }
          return true;
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Données invalides'),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
          return false;
        }
      } else {
        print('Invalid response data');
        print('Response status code: ${response.statusCode}');
        print('Response body: ${response.body}');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text("Vérifier vos données et réessayer"),
            backgroundColor: Colors.red,
          ),
        );
        return false;
      }
    } catch (e) {
      print('Error: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text("Problème de connexion"),
          backgroundColor: Colors.red,
        ),
      );
      return false;
    }
  }
}
