import 'package:bee_kids_mobile/services/auth_guard.dart';
import 'package:bee_kids_mobile/view/directrice/directrice_routes.dart';
import 'package:bee_kids_mobile/view/educateur/educateur_routes.dart';
import 'package:bee_kids_mobile/view/parent/parent_routes.dart';
import 'package:flutter/material.dart';
import '../view/login.dart';

class AppRoutes {
  static const login = '/';
  static const directriceHome = '/directrice';
  static const parentHome = '/parent';
  static const educateurHome = '/educateur';

  static Route<dynamic>? generateRoute(RouteSettings settings) {
  if (settings.name == login) {
    return MaterialPageRoute(builder: (_) => const LoginScreen());
  }

  if (settings.name?.startsWith(directriceHome) == true) {
    final directriceRoute = DirectriceRoutes.onGenerateRoute(settings);
    if (directriceRoute != null) {
      return AuthGuard.guardRoute(directriceRoute);
    }
  }

  if (settings.name?.startsWith(educateurHome) == true) {
    final educateurRoute = EducateurRoutes.onGenerateRoute(settings);
    if (educateurRoute != null) {
      return AuthGuard.guardRoute(educateurRoute);
    }
  }

  if (settings.name?.startsWith(parentHome) == true) {
    final parentRoute = ParentRoutes.onGenerateRoute(settings);
    if (parentRoute != null) {
      return AuthGuard.guardRoute(parentRoute);
    }
  }

  return MaterialPageRoute(
    builder: (_) => Scaffold(
      body: Center(
        child: Text('Route non trouvée: ${settings.name}'),
      ),
    ),
  );
}
}