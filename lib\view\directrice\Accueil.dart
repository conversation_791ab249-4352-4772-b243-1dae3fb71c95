import 'dart:async';
import 'dart:convert';

import 'package:bee_kids_mobile/model/Comment.dart';
import 'package:bee_kids_mobile/model/Like.dart';
import 'package:bee_kids_mobile/model/Post.dart';
import 'package:bee_kids_mobile/view/FullScreenImageView.dart';
import 'package:bee_kids_mobile/view/InlineVideoPlayer.dart';
import 'package:bee_kids_mobile/view/PDFViewerScreen.dart';
import 'package:bee_kids_mobile/view/VideoPlayerScreen.dart';
import 'package:bee_kids_mobile/view/directrice/footer.dart';
import 'package:bee_kids_mobile/view_model/WebSocketService.dart';
import 'package:bee_kids_mobile/view_model/postService.dart';
import 'package:bee_kids_mobile/view_model/tokenService.dart';
import 'package:bee_kids_mobile/view_model/userService.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:dio/dio.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'dart:io';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';


class AccueilScreen extends StatefulWidget {
  final int? postId;
  const AccueilScreen({super.key, this.postId});

  @override
  _AccueilScreenState createState() => _AccueilScreenState();
}

class _AccueilScreenState extends State<AccueilScreen>
    with TickerProviderStateMixin {
  final TextEditingController _commentController = TextEditingController();
  final TextEditingController _editPostController = TextEditingController();
  final PostService postService = Get.put(PostService());
  late Future<List<Post>> postsFuture;
  List<Post> posts = [];
  int page = 0;
  final int size = 3;
  bool isLoading = true;
  final UserService userService = UserService();
  String? userPhotoUrl;
  int? userConnectedId;
  bool _submitedComment = false;
  bool isUpdating = false;
  Set<int> _deletingComments = {};
  Map<String, bool> _downloadStates = {};
  Map<int, bool> _deleteStates = {};
  Map<int, bool> _editStates = {};
  StreamSubscription? _wsSubscription;
  int _photoCounter = 0;
  int _videoCounter = 0;
  int _documentCounter = 0;
  int _fileCounter = 0;
  String? _directriceFirstName;
  String? _directriceLastName;
  late AnimationController _animationController;
  late AnimationController _deleteAnimationController;
  late AnimationController _likeAnimationController;
  // NEW: Comment pagination variables
  Map<int, int> _commentPages = {}; // postId -> current page
  Map<int, bool> _commentLoadingStates = {}; // postId -> loading state
  Map<int, List<Comment>> _allComments = {}; // postId -> all comments
  Map<int, List<Comment>> _displayedComments =
      {}; // postId -> displayed comments
  final int _commentsPerPage = 4; // Number of comments to show per page
  Map<int, bool> _hasMoreComments = {}; // postId -> has more comments
  late AnimationController _commentPaginationController;
  Map<String, int> _currentImageIndexes = {};
  // Save counter to SharedPreferences
  Future<void> _saveCounter(String key, int value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(key, value);
  }

  // Load all counters from SharedPreferences
  Future<void> _loadCounters() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _photoCounter = prefs.getInt('photo_counter') ?? 0;
      _videoCounter = prefs.getInt('video_counter') ?? 0;
      _documentCounter = prefs.getInt('document_counter') ?? 0;
      _fileCounter = prefs.getInt('file_counter') ?? 0;
    });
  }

  void _setupWebSocketListener() {
    _wsSubscription = WebSocketService().conversationUpdates.listen((message) {
      if (mounted) {
        setState(() {
          loadPosts();
        });
      }
    });
  }

  @override
  void dispose() {
    _wsSubscription?.cancel();
    _animationController.dispose();
    _deleteAnimationController.dispose();
    _likeAnimationController.dispose();
    _commentPaginationController.dispose(); // NEW
    _editPostController.dispose();
    _commentController.dispose();
    super.dispose();
  }

  void _fetchUserId() async {
    userConnectedId = await TokenService().getId();
    setState(() {});
  }

  Future<void> _fetchdirectriceDetails() async {
    try {
      final user = await userService.fetchUsersById();
      setState(() {
        _directriceFirstName = user.nom;
        _directriceLastName = user.prenom;
      });
    } catch (e) {
      print("Erreur lors de la récupération des détails de l'éducateur : $e");
    }
  }

  Future<bool> _requestPermissions() async {
    if (await Permission.storage.request().isGranted) {
      return true;
    }

    if (await Permission.manageExternalStorage.request().isGranted) {
      return true;
    }

    if (await Permission.photos.request().isGranted &&
        await Permission.videos.request().isGranted &&
        await Permission.audio.request().isGranted) {
      return true;
    }

    return false;
  }

  Future<void> _downloadFile(BuildContext context, String fileUrl) async {
    try {
      setState(() {
        _downloadStates[fileUrl] = true;
      });

      // Extract original filename and extension from URL
      final originalFileName = fileUrl.split('/').last;
      final fileExtension = originalFileName.split('.').last.toLowerCase();

      // Determine file type and create custom name
      String customFileName;

      if (['jpg', 'jpeg', 'png', 'gif', 'webp'].contains(fileExtension)) {
        // It's an image
        _photoCounter++;
        await _saveCounter('photo_counter', _photoCounter);
        customFileName = "BeekIds_Photo_${_photoCounter}.$fileExtension";
      } else if (['mp4', 'mov', 'avi', 'mkv', 'webm'].contains(fileExtension)) {
        // It's a video
        _videoCounter++;
        await _saveCounter('video_counter', _videoCounter);
        customFileName = "BeekIds_Video_${_videoCounter}.$fileExtension";
      } else if (fileExtension == 'pdf') {
        // It's a PDF
        _documentCounter++;
        await _saveCounter('document_counter', _documentCounter);
        customFileName = "BeekIds_Document_${_documentCounter}.$fileExtension";
      } else {
        // Other file types
        _fileCounter++;
        await _saveCounter('file_counter', _fileCounter);
        customFileName = "BeekIds_File_${_fileCounter}.$fileExtension";
      }

      if (Platform.isAndroid) {
        // Request permissions for Android
        bool hasPermission = await _requestAndroidPermissions();
        if (!hasPermission) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Permission de stockage refusée'),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
          return;
        }

        // Download file to temporary location first
        final dio = Dio();
        final tempDir = await getTemporaryDirectory();
        final tempPath = '${tempDir.path}/$customFileName';

        // Download the file
        await dio.download(fileUrl, tempPath,
            onReceiveProgress: (received, total) {
          if (total != -1) {
            print('${(received / total * 100).toStringAsFixed(0)}%');
          }
        });

        // Save to gallery using ImageGallerySaver for all media types
        if (['jpg', 'jpeg', 'png', 'gif', 'webp'].contains(fileExtension)) {
          // For images - save to gallery
          final result =
              await ImageGallerySaver.saveFile(tempPath, name: customFileName);
          if (result['isSuccess']) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Image sauvegardée dans la galerie'),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
              ),
            );
          } else {
            throw Exception('Failed to save image to gallery');
          }
        } else if (['mp4', 'mov', 'avi', 'mkv', 'webm']
            .contains(fileExtension)) {
          // For videos - save to gallery
          final result =
              await ImageGallerySaver.saveFile(tempPath, name: customFileName);
          if (result['isSuccess']) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Vidéo sauvegardée dans la galerie'),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
              ),
            );
          } else {
            throw Exception('Failed to save video to gallery');
          }
        } else {
          // For other files (PDFs, etc.), save to Downloads directory
          final directory = Directory('/storage/emulated/0/Download');
          final beekidsDir = Directory('${directory.path}/BeekIds');
          if (!await beekidsDir.exists()) {
            await beekidsDir.create(recursive: true);
          }

          final finalPath = '${beekidsDir.path}/$customFileName';
          await File(tempPath).copy(finalPath);

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Fichier téléchargé dans Téléchargements/BeekIds'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }

        // Clean up temp file
        try {
          await File(tempPath).delete();
        } catch (e) {
          print('Could not delete temp file: $e');
        }
      } else if (Platform.isIOS) {
        // iOS implementation (keep your existing iOS code)
        final dio = Dio();
        final tempDir = await getTemporaryDirectory();
        final savePath = '${tempDir.path}/$customFileName';

        // Download the file to temp directory
        await dio.download(fileUrl, savePath,
            onReceiveProgress: (received, total) {
          if (total != -1) {
            print('${(received / total * 100).toStringAsFixed(0)}%');
          }
        });

        // Save to Photos library
        if (['jpg', 'jpeg', 'png', 'gif', 'webp'].contains(fileExtension)) {
          // For images
          final result = await ImageGallerySaver.saveFile(savePath);
          if (result['isSuccess']) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Image sauvegardée dans Photos'),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
              ),
            );
          } else {
            throw Exception('Failed to save image to gallery');
          }
        } else if (['mp4', 'mov', 'avi', 'mkv', 'webm']
            .contains(fileExtension)) {
          // For videos
          final result = await ImageGallerySaver.saveFile(savePath);
          if (result['isSuccess']) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Vidéo sauvegardée dans Photos'),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
              ),
            );
          } else {
            throw Exception('Failed to save video to gallery');
          }
        } else {
          // For other files (PDFs, etc.), save to Documents directory
          final appDocDir = await getApplicationDocumentsDirectory();
          final beekidsDir = Directory('${appDocDir.path}/BeekIds');
          if (!await beekidsDir.exists()) {
            await beekidsDir.create(recursive: true);
          }

          final finalPath = '${beekidsDir.path}/$customFileName';
          await File(savePath).copy(finalPath);

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Fichier téléchargé dans l\'application'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      } else {
        // For other platforms, use the app's documents directory
        final appDocDir = await getApplicationDocumentsDirectory();
        final beekidsDir = Directory('${appDocDir.path}/BeekIds');
        if (!await beekidsDir.exists()) {
          await beekidsDir.create(recursive: true);
        }

        final savePath = '${beekidsDir.path}/$customFileName';

        // Download the file
        final dio = Dio();
        await dio.download(fileUrl, savePath,
            onReceiveProgress: (received, total) {
          if (total != -1) {
            print('${(received / total * 100).toStringAsFixed(0)}%');
          }
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fichier téléchargé dans l\'application'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      print('Error downloading file: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur de téléchargement: $e'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    } finally {
      // Reset download state
      setState(() {
        _downloadStates[fileUrl] = false;
      });
    }
  }

// Add this new method for Android permissions
  Future<bool> _requestAndroidPermissions() async {
    if (Platform.isAndroid) {
      // For Android 13+ (API 33+)
      if (await Permission.photos.request().isGranted &&
          await Permission.videos.request().isGranted) {
        return true;
      }

      // For Android 11-12 (API 30-32)
      if (await Permission.storage.request().isGranted) {
        return true;
      }

      // For Android 11+ with manage external storage
      if (await Permission.manageExternalStorage.request().isGranted) {
        return true;
      }

      return false;
    }
    return true;
  }

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _deleteAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _likeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    // NEW: Comment pagination animation controller
    _commentPaginationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    loadPosts();
    _fetchUserPhoto();
    _refreshPosts();
    _fetchUserId();
    _setupWebSocketListener();
    _loadCounters();
    _fetchdirectriceDetails();

    if (widget.postId != null) {
      Future.delayed(Duration(milliseconds: 500), () {
        if (mounted) {
          _showPostDetailsPopup(widget.postId!);
        }
      });
    }
  }

  Future<Post> _fetchPostById(int id) async {
    return await postService.getPostById(id);
  }

  void _showPostDetailsPopup(int postId) async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            padding: EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
                ),
                SizedBox(height: 15),
                Text(
                  "Chargement...",
                  style: TextStyle(
                    color: Colors.green[800],
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );

    try {
      Post post = await _fetchPostById(postId);
      Navigator.pop(context);

      showDialog(
        context: context,
        builder: (BuildContext context) {
          return Dialog(
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
            child: Container(
              width: MediaQuery.of(context).size.width * 0.9,
              constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [Colors.white, Colors.green.shade50],
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.green.shade600, Colors.green.shade400],
                      ),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.article, color: Colors.white, size: 24),
                        SizedBox(width: 10),
                        Text(
                          'Détails du Post',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        Spacer(),
                        IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: Icon(Icons.close, color: Colors.white),
                        ),
                      ],
                    ),
                  ),
                  Flexible(
                    child: SingleChildScrollView(
                      padding: EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            padding: EdgeInsets.all(15),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(15),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withOpacity(0.1),
                                  spreadRadius: 1,
                                  blurRadius: 5,
                                ),
                              ],
                            ),
                            child: Row(
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                        color: Colors.green, width: 2),
                                  ),
                                  child: post.authorPhotoUrl != null &&
                                          post.authorPhotoUrl!.isNotEmpty
                                      ? CircleAvatar(
                                          backgroundImage: NetworkImage(
                                              post.authorPhotoUrl!),
                                          radius: 25,
                                        )
                                      : const CircleAvatar(
                                          backgroundImage: AssetImage(
                                              'lib/resources/images/avatar_girl.png'),
                                          radius: 25,
                                        ),
                                ),
                                const SizedBox(width: 15),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        post.authorName ??
                                            'Utilisateur inconnu',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                      Text(
                                        '${(post.role == 'Formateur' ? 'directrice' : post.role) ?? 'Membre'} • ${_timeAgo(post.createdAt ?? DateTime.now())}',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey[600],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 20),
                          Container(
                            width: double.infinity,
                            padding: EdgeInsets.all(15),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(15),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withOpacity(0.1),
                                  spreadRadius: 1,
                                  blurRadius: 5,
                                ),
                              ],
                            ),
                            child: Text(
                              _decodeContent(post.content) ?? 'Pas de contenu',
                              style: TextStyle(fontSize: 16, height: 1.5),
                            ),
                          ),
                          const SizedBox(height: 20),
                          if (post.photoUrl != null &&
                              post.photoUrl!.isNotEmpty)
                            Container(
                              height: 250,
                              width: double.infinity,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(15),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.grey.withOpacity(0.1),
                                    spreadRadius: 1,
                                    blurRadius: 5,
                                  ),
                                ],
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(15),
                                child: GridView.builder(
                                  gridDelegate:
                                      SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount: 2,
                                    crossAxisSpacing: 8,
                                    mainAxisSpacing: 8,
                                  ),
                                  shrinkWrap: true,
                                  physics: ClampingScrollPhysics(),
                                  itemCount: post.photoUrl!.length,
                                  itemBuilder: (context, index) {
                                    return GestureDetector(
                                      onTap: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) =>
                                                FullScreenImageView(
                                              imageUrl: post.photoUrl![index],
                                            ),
                                          ),
                                        );
                                      },
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(10),
                                        child: Image.network(
                                          post.photoUrl![index],
                                          fit: BoxFit.cover,
                                          errorBuilder:
                                              (context, error, stackTrace) =>
                                                  Container(
                                            color: Colors.grey[300],
                                            child: Icon(Icons.broken_image),
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    } catch (e) {
      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur lors du chargement du post: $e'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  Future<void> _fetchUserPhoto() async {
    try {
      final photoUrl = await userService.fetchPhotoUsersId();
      setState(() {
        userPhotoUrl = photoUrl;
        isLoading = false;
      });
    } catch (e) {
      print('Error fetching user photo: $e');
      setState(() {
        isLoading = false;
      });
    }
  }

  void _refreshPosts() {
    setState(() {
      posts.clear();
      page = 0;
    });
  }

  Future<void> _deletePost(int postId, int index) async {
    setState(() {
      _deleteStates[postId] = true;
    });

    try {
      _deleteAnimationController.forward();

      await postService.deletePost(postId);

      // Refresh the posts after successful deletion
      await _refreshAndReloadPosts();

      _showSuccessSnackBar('Post supprimé avec succès', Icons.delete_outline);
    } catch (e) {
      _showErrorSnackBar('Erreur lors de la suppression: $e');
    } finally {
      setState(() {
        _deleteStates[postId] = false;
      });
      _deleteAnimationController.reset();
    }
  }

// Add this new method to refresh and reload posts
  Future<void> _refreshAndReloadPosts() async {
    setState(() {
      posts.clear();
      page = 0;
      isLoading = true;
    });

    await loadPosts();
  }

  Future<void> _editPost(Post post, int index) async {
    _editPostController.text = post.content ?? '';
    bool? shouldEdit = await _showEditPostDialog(post);

    if (shouldEdit == true) {
      setState(() {
        _editStates[post.id] = true;
      });

      try {
        // Fix: Add empty list for files parameter
        await postService
            .updatePost(post.id, _editPostController.text.trim(), []);

        // Fix: Create a new Post object with updated content instead of modifying the final field
        setState(() {
          posts[index] = Post(
            id: post.id,
            content: _editPostController.text.trim(), // Updated content
            photoUrl: post.photoUrl,
            videoUrl: post.videoUrl,
            pdfUrl: post.pdfUrl,
            createdAt: post.createdAt,
            authorName: post.authorName,
            authorPhotoUrl: post.authorPhotoUrl,
            role: post.role,
            authorId: post.authorId,
            likeCount: post.likeCount,
            liked: post.liked,
            likedByUsers: post.likedByUsers,
            detailedLikedByUsers: post.detailedLikedByUsers,
            commentCount: post.commentCount,
            comments: post.comments,
            showComments: post.showComments,
          );
          _editStates[post.id] = false;
        });

        _showSuccessSnackBar('Post modifié avec succès', Icons.edit_outlined);
      } catch (e) {
        _showErrorSnackBar('Erreur lors de la modification: $e');
        setState(() {
          _editStates[post.id] = false;
        });
      }
    }
  }

  Future<bool?> _showEditPostDialog(Post post) {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Icon(Icons.edit, color: Colors.blue),
              SizedBox(width: 10),
              Text('Modifier le post'),
            ],
          ),
          content: Container(
            width: double.maxFinite,
            child: TextField(
              controller: _editPostController,
              maxLines: 5,
              decoration: InputDecoration(
                hintText: 'Modifiez votre contenu...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(15),
                  borderSide: BorderSide(color: Colors.blue, width: 2),
                ),
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: Text(
                'Annuler',
                style: TextStyle(color: Colors.grey[600]),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context, true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: Text(
                'Modifier',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showSuccessSnackBar(String message, IconData icon) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(icon, color: Colors.white),
            SizedBox(width: 10),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        duration: Duration(seconds: 3),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.error, color: Colors.white),
            SizedBox(width: 10),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        duration: Duration(seconds: 4),
      ),
    );
  }

  Future<void> loadPosts() async {
    int? userId = await TokenService().getId();
    if (userId == null) return;

    // Check if widget is still mounted before setting state
    if (!mounted) return;

    setState(() {
      isLoading = true;
    });

    try {
      List<Post> newPosts = await postService.getPaginatedPosts(page, size);

      for (var post in newPosts) {
        post.commentCount = await postService.getCommentCountForPost(post.id);
        post.likeCount = await postService.getLikeCountForPost(post.id);
        post.liked = await postService.isPostLiked(post.id, userId);
        // Use the detailed method to get users with their information
        post.likedByUsers =
            (await postService.getDetailedLikedUsersWithPhotos(post.id))
                .cast<LikeDTO>();
      }

      // Check if widget is still mounted before setting state
      if (!mounted) return;

      setState(() {
        posts.addAll(newPosts);
        page++;
      });
    } catch (e) {
      print("Error loading posts: $e");
    } finally {
      // Check if widget is still mounted before setting state
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  Future<void> loadMorePosts() async {
    loadPosts();
  }

  void toggleLike(Post post, int index) async {
    int? userId = await TokenService().getId();
    if (userId == null) return;

    bool originalLiked = post.liked;

    setState(() {
      post.liked = !post.liked;
    });

    if (post.liked) {
      _likeAnimationController.forward();
    }

    try {
      await postService.toggleLike(post.id, userId);
      print('Successfully toggled like');

      int updatedLikeCount = await postService.getLikeCountForPost(post.id);
      bool updatedLikedStatus = await postService.isPostLiked(post.id, userId);

      setState(() {
        post.likeCount = updatedLikeCount;
        post.liked = updatedLikedStatus;
      });
    } catch (e) {
      setState(() {
        post.liked = originalLiked;
      });
      print('Error toggling like: $e');
    }
  }

  void fetchComments(Post post) async {
    try {
      setState(() {
        _commentLoadingStates[post.id] = true;
      });

      final comments = await postService.getCommentsForPost(post.id);
      final commentCount = await postService.getCommentCountForPost(post.id);

      setState(() {
        // Store all comments
        _allComments[post.id] = comments;

        // Initialize pagination for this post
        _commentPages[post.id] = 0;

        // Show first page of comments
        _displayedComments[post.id] = _getCommentsForPage(post.id, 0);

        // Check if there are more comments
        _hasMoreComments[post.id] = comments.length > _commentsPerPage;

        post.comments = _displayedComments[post.id] ?? [];
        post.showComments = !post.showComments;
        post.commentCount = commentCount;

        _commentLoadingStates[post.id] = false;
      });
    } catch (e) {
      print('Error fetching comments: $e');
      setState(() {
        _commentLoadingStates[post.id] = false;
      });
      _showErrorSnackBar('Erreur lors du chargement des commentaires');
    }
  }

// NEW: Helper method to get comments for a specific page
  List<Comment> _getCommentsForPage(int postId, int page) {
    final allComments = _allComments[postId] ?? [];
    final startIndex = page * _commentsPerPage;
    final endIndex =
        (startIndex + _commentsPerPage).clamp(0, allComments.length);

    if (startIndex >= allComments.length) return [];

    return allComments.sublist(startIndex, endIndex);
  }

// NEW: Load more comments for a specific post
  void _loadMoreComments(int postId) {
    final currentPage = _commentPages[postId] ?? 0;
    final nextPage = currentPage + 1;

    final newComments = _getCommentsForPage(postId, nextPage);

    if (newComments.isNotEmpty) {
      setState(() {
        _commentPages[postId] = nextPage;
        _displayedComments[postId] = [
          ...(_displayedComments[postId] ?? []),
          ...newComments
        ];

        // Update has more comments status
        final allComments = _allComments[postId] ?? [];
        final totalDisplayed = _displayedComments[postId]?.length ?? 0;
        _hasMoreComments[postId] = totalDisplayed < allComments.length;

        // Update post comments
        final postIndex = posts.indexWhere((p) => p.id == postId);
        if (postIndex != -1) {
          posts[postIndex].comments = _displayedComments[postId] ?? [];
        }
      });

      // Animate the new comments
      _commentPaginationController.forward().then((_) {
        _commentPaginationController.reset();
      });
    }
  }

// NEW: Load previous comments for a specific post
  void _loadPreviousComments(int postId) {
    final currentPage = _commentPages[postId] ?? 0;

    if (currentPage > 0) {
      final previousPage = currentPage - 1;

      setState(() {
        _commentPages[postId] = previousPage;
        _displayedComments[postId] = _getCommentsForPage(postId, previousPage);

        // Update has more comments status
        final allComments = _allComments[postId] ?? [];
        _hasMoreComments[postId] =
            _displayedComments[postId]!.length < allComments.length;

        // Update post comments
        final postIndex = posts.indexWhere((p) => p.id == postId);
        if (postIndex != -1) {
          posts[postIndex].comments = _displayedComments[postId] ?? [];
        }
      });
    }
  }

  void submitComment(Post post) async {
    int? userId = await TokenService().getId();
    final content = _commentController.text.trim();

    if (content.isEmpty) {
      _showErrorSnackBar('Veuillez saisir un commentaire');
      return;
    }

    // AJOUTEZ CETTE LIGNE : Masquer le clavier immédiatement
    FocusScope.of(context).unfocus();

    setState(() {
      _submitedComment = true;
    });

    try {
      final newComment =
          await postService.addComment(post.id, userId!, content);

      setState(() {
        // Add to all comments
        if (_allComments[post.id] != null) {
          _allComments[post.id]!.add(newComment);
        } else {
          _allComments[post.id] = [newComment];
        }

        // Add to displayed comments if we're on the last page or if it's the first comment
        final currentPage = _commentPages[post.id] ?? 0;
        final allComments = _allComments[post.id] ?? [];
        final totalPages = (allComments.length / _commentsPerPage).ceil();

        // If we're on the last page, add the new comment to displayed comments
        if (currentPage == totalPages - 1 || allComments.length == 1) {
          if (_displayedComments[post.id] != null) {
            _displayedComments[post.id]!.add(newComment);
          } else {
            _displayedComments[post.id] = [newComment];
          }
          post.comments = _displayedComments[post.id] ?? [];
        }

        // Update pagination info
        _hasMoreComments[post.id] = allComments.length > _commentsPerPage;

        post.commentCount++;
        _commentController.clear();
      });

      _showSuccessSnackBar(
          'Commentaire ajouté avec succès', Icons.comment_outlined);
    } catch (e) {
      print('Error adding comment: $e');
      _showErrorSnackBar('Erreur lors de l\'ajout du commentaire');
    } finally {
      setState(() {
        _submitedComment = false;
      });
    }
  }

  Future<void> updateComment(Comment comment, String updatedContent) async {
    int? userId = await TokenService().getId();

    if (updatedContent.trim().isEmpty) {
      _showErrorSnackBar('Le commentaire ne peut pas être vide');
      return;
    }

    if (isUpdating) return;

    setState(() => isUpdating = true);

    try {
      await postService.updateComment(
          comment.id, updatedContent.trim(), userId!);

      setState(() {
        // Update in all comments
        for (var postId in _allComments.keys) {
          final commentIndex =
              _allComments[postId]!.indexWhere((c) => c.id == comment.id);
          if (commentIndex != -1) {
            _allComments[postId]![commentIndex].commentaire =
                updatedContent.trim();
            _allComments[postId]![commentIndex].editMode = false;
            _allComments[postId]![commentIndex].tempContent = null;
            break;
          }
        }

        // Update in displayed comments
        for (var postId in _displayedComments.keys) {
          final commentIndex =
              _displayedComments[postId]!.indexWhere((c) => c.id == comment.id);
          if (commentIndex != -1) {
            _displayedComments[postId]![commentIndex].commentaire =
                updatedContent.trim();
            _displayedComments[postId]![commentIndex].editMode = false;
            _displayedComments[postId]![commentIndex].tempContent = null;
            break;
          }
        }

        // Update the original comment object
        comment.commentaire = updatedContent.trim();
        comment.editMode = false;
        comment.tempContent = null;
      });

      _showSuccessSnackBar(
          'Commentaire mis à jour avec succès', Icons.edit_outlined);
    } catch (e) {
      print('Error updating comment: $e');
      _showErrorSnackBar('Erreur lors de la mise à jour du commentaire');
    } finally {
      setState(() => isUpdating = false);
    }
  }

  Future<void> deleteComment(Post post, Comment comment) async {
    int? userId = await TokenService().getId();

    if (_deletingComments.contains(comment.id)) return;

    // AJOUTEZ CETTE LIGNE : Masquer le clavier avant la suppression
    FocusScope.of(context).unfocus();

    setState(() {
      _deletingComments.add(comment.id);
    });

    try {
      await postService.deleteComment(comment.id, userId!);

      setState(() {
        // Remove from all comments
        if (_allComments[post.id] != null) {
          _allComments[post.id]!.remove(comment);
        }

        // Remove from displayed comments
        if (_displayedComments[post.id] != null) {
          _displayedComments[post.id]!.remove(comment);
        }

        // Remove from post comments
        post.comments.remove(comment);

        if (post.commentCount > 0) {
          post.commentCount--;
        }

        // Recalculate pagination after deletion
        final allComments = _allComments[post.id] ?? [];
        final currentPage = _commentPages[post.id] ?? 0;

        // If current page is empty and we're not on the first page, go to previous page
        if (_displayedComments[post.id]!.isEmpty && currentPage > 0) {
          _commentPages[post.id] = currentPage - 1;
          _displayedComments[post.id] =
              _getCommentsForPage(post.id, currentPage - 1);
          post.comments = _displayedComments[post.id] ?? [];
        }

        // Update has more comments status
        final totalDisplayed = _displayedComments[post.id]?.length ?? 0;
        _hasMoreComments[post.id] = totalDisplayed < allComments.length;
      });

      _showSuccessSnackBar(
          'Commentaire supprimé avec succès', Icons.delete_outline);
    } catch (e) {
      print('Error deleting comment: $e');
      _showErrorSnackBar('Erreur lors de la suppression du commentaire');
    } finally {
      setState(() {
        _deletingComments.remove(comment.id);
      });
    }
  }

  String _timeAgo(DateTime date) {
    // Ajouter 1 heure à la date avant de calculer la différence
    final adjustedDate = date.add(Duration(hours: 1));
    final difference = DateTime.now().difference(adjustedDate);

    if (difference.inDays > 1) {
      return '${difference.inDays} jours';
    } else if (difference.inDays == 1) {
      return '1 jour';
    } else if (difference.inHours > 1) {
      return '${difference.inHours} heures';
    } else if (difference.inHours == 1) {
      return '1 heure';
    } else if (difference.inMinutes > 1) {
      return '${difference.inMinutes} minutes';
    } else {
      return 'À l\'instant';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: CustomScrollView(
        slivers: [
          _buildModernAppBar(),
          SliverToBoxAdapter(child: _buildWelcomeSection()),
          SliverToBoxAdapter(child: _buildPostInputForm()),
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                if (index == posts.length) {
                  return Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: isLoading
                        ? _buildLoadingCard()
                        : _buildLoadMoreButton(),
                  );
                }
                final post = posts[index];
                return _buildModernPost(context, post, index);
              },
              childCount: posts.length + 1,
            ),
          ),
        ],
      ),
      bottomNavigationBar: const MyFooter(currentRoute: '/directrice/accueil'),
    );
  }

  Widget _buildModernAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: Colors.transparent,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.green.shade600,
                Colors.green.shade400,
                Colors.teal.shade300,
              ],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: CircleAvatar(
                      backgroundImage:
                          AssetImage('lib/resources/images/logo.png'),
                      radius: 20,
                    ),
                  ),
                  SizedBox(width: 15),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text.rich(
                          TextSpan(
                            children: [
                              TextSpan(
                                text: 'BEE',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 24,
                                ),
                              ),
                              TextSpan(
                                text: '-KIDS',
                                style: TextStyle(
                                  color: Colors.pink.shade200,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 24,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Text(
                          'Partagez les moments précieux',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.9),
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Icon(
                      Icons.notifications_outlined,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Container(
      margin: EdgeInsets.all(16),
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.white, Colors.green.shade50],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: Colors.green, width: 3),
              boxShadow: [
                BoxShadow(
                  color: Colors.green.withOpacity(0.2),
                  spreadRadius: 2,
                  blurRadius: 8,
                ),
              ],
            ),
            child: CircleAvatar(
              radius: 30,
              backgroundImage: isLoading
                  ? const AssetImage('lib/resources/images/avatar_girl.png')
                      as ImageProvider
                  : (userPhotoUrl != null
                      ? NetworkImage(userPhotoUrl!)
                      : const AssetImage(
                          'lib/resources/images/avatar_girl.png')),
            ),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Bonjour ! 👋",
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  "${_directriceFirstName ?? 'directrice'} ${_directriceLastName ?? ''}",
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 22,
                    color: Colors.green,
                  ),
                ),
                SizedBox(height: 5),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.green.shade100,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    "Espace Directrice",
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.green.shade700,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green.shade100,
              borderRadius: BorderRadius.circular(15),
            ),
            child: Icon(
              Icons.school,
              color: Colors.green.shade600,
              size: 28,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPostInputForm() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          children: [
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.green.shade300, width: 2),
              ),
              child: CircleAvatar(
                radius: 22,
                backgroundImage: isLoading
                    ? const AssetImage('lib/resources/images/avatar_girl.png')
                        as ImageProvider
                    : (userPhotoUrl != null
                        ? NetworkImage(userPhotoUrl!)
                        : const AssetImage(
                            'lib/resources/images/avatar_girl.png')),
              ),
            ),
            SizedBox(width: 15),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  Navigator.pushNamed(context, '/directrice/poster');
                },
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: Text(
                    'Quoi de neuf ?',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(width: 12),
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.pink.shade400, Colors.pink.shade600],
                ),
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: Colors.pink.withOpacity(0.3),
                    spreadRadius: 1,
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: IconButton(
                onPressed: () {
                  Navigator.pushNamed(context, '/directrice/poster');
                },
                icon: const Icon(Icons.add, color: Colors.white, size: 24),
                padding: EdgeInsets.all(12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingCard() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
          ),
          SizedBox(height: 15),
          Text(
            "Chargement des publications...",
            style: TextStyle(
              color: Colors.green[800],
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadMoreButton() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ElevatedButton(
        onPressed: loadMorePosts,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green,
          foregroundColor: Colors.white,
          padding: EdgeInsets.symmetric(vertical: 15),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          elevation: 2,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.refresh, size: 20),
            SizedBox(width: 8),
            Text(
              "Voir plus de publications",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernPost(BuildContext context, Post post, int index) {
    if (userConnectedId == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 15,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Post Header
          Container(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.green.shade300, width: 2),
                  ),
                  child: post.authorPhotoUrl != null &&
                          post.authorPhotoUrl!.isNotEmpty
                      ? CircleAvatar(
                          backgroundImage: NetworkImage(post.authorPhotoUrl!),
                          radius: 25,
                        )
                      : const CircleAvatar(
                          backgroundImage: AssetImage(
                              'lib/resources/images/avatar_girl.png'),
                          radius: 25,
                        ),
                ),
                const SizedBox(width: 15),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        post.authorName ?? 'Utilisateur inconnu',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      SizedBox(height: 4),
                      Row(
                        children: [
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.blue.shade100,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              (post.role == 'Formateur'
                                      ? 'directrice'
                                      : post.role) ??
                                  'Membre',
                              style: TextStyle(
                                fontSize: 11,
                                color: Colors.blue.shade700,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          SizedBox(width: 8),
                          Text(
                            _timeAgo(post.createdAt ?? DateTime.now()),
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Action buttons for post author
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Edit button
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: _editStates[post.id] == true
                          ? Padding(
                              padding: EdgeInsets.all(12),
                              child: SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.blue.shade600,
                                ),
                              ),
                            )
                          : IconButton(
                              icon: Icon(Icons.edit,
                                  color: Colors.blue.shade600, size: 20),
                              onPressed: () => _editPost(post, index),
                              constraints:
                                  BoxConstraints(minWidth: 40, minHeight: 40),
                            ),
                    ),
                    SizedBox(width: 8),
                    // Delete button
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.red.shade50,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: _deleteStates[post.id] == true
                          ? Padding(
                              padding: EdgeInsets.all(12),
                              child: SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.red.shade600,
                                ),
                              ),
                            )
                          : IconButton(
                              icon: Icon(Icons.delete,
                                  color: Colors.red.shade600, size: 20),
                              onPressed: () async {
                                bool? confirmDelete = await showDialog<bool>(
                                  context: context,
                                  builder: (BuildContext context) {
                                    return AlertDialog(
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      title: Row(
                                        children: [
                                          Icon(Icons.warning,
                                              color: Colors.orange),
                                          SizedBox(width: 10),
                                          Text('Confirmation'),
                                        ],
                                      ),
                                      content: Text(
                                        'Êtes-vous sûr de vouloir supprimer ce post ?',
                                        style: TextStyle(fontSize: 16),
                                      ),
                                      actions: [
                                        TextButton(
                                          onPressed: () {
                                            Navigator.pop(context, false);
                                          },
                                          child: Text(
                                            'Annuler',
                                            style: TextStyle(
                                                color: Colors.grey[600]),
                                          ),
                                        ),
                                        ElevatedButton(
                                          onPressed: () {
                                            Navigator.pop(context, true);
                                          },
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.red,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                            ),
                                          ),
                                          child: Text(
                                            'Supprimer',
                                            style:
                                                TextStyle(color: Colors.white),
                                          ),
                                        ),
                                      ],
                                    );
                                  },
                                );

                                if (confirmDelete == true) {
                                  await _deletePost(post.id, index);
                                }
                              },
                              constraints:
                                  BoxConstraints(minWidth: 40, minHeight: 40),
                            ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Post Content
          if (post.content != null && post.content!.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Container(
                width: double.infinity,
                padding: EdgeInsets.all(15),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Text(
                  post.content ?? '',
                  style: TextStyle(
                    fontSize: 15,
                    height: 1.5,
                    color: Colors.grey[800],
                  ),
                ),
              ),
            ),

          SizedBox(height: 15),

          // Media Content
          if (post.photoUrl != null && post.photoUrl!.isNotEmpty)
            _buildModernMediaGrid(context, post.photoUrl!, 'photo'),
          if (post.pdfUrl != null && post.pdfUrl!.isNotEmpty)
            _buildModernMediaGrid(context, post.pdfUrl!, 'pdf'),
          if (post.videoUrl != null && post.videoUrl!.isNotEmpty)
            _buildModernMediaGrid(context, post.videoUrl!, 'video'),

          // Action Buttons
          // Action Buttons
          Container(
            padding: EdgeInsets.all(20),
            child: Row(
              children: [
                _buildModernLikeButton(post, index),
                SizedBox(width: 15),
                _buildModernViewLikesButton(post),
                SizedBox(width: 15),
                _buildModernCommentButton(post),
                Spacer(),
              ],
            ),
          ),

          // Comment Section
          if (post.showComments) _buildModernCommentSection(post),
        ],
      ),
    );
  }

  Widget _buildModernLikeButton(Post post, int index) {
    return GestureDetector(
      onTap: () => toggleLike(post, index),
      child: AnimatedContainer(
        duration: Duration(milliseconds: 200),
        padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
        decoration: BoxDecoration(
          color: post.liked ? Colors.red.shade50 : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: post.liked ? Colors.red.shade200 : Colors.grey.shade300,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedSwitcher(
              duration: Duration(milliseconds: 200),
              child: Icon(
                post.liked ? Icons.favorite : Icons.favorite_border,
                key: ValueKey(post.liked),
                color: post.liked ? Colors.red.shade600 : Colors.grey[600],
                size: 18,
              ),
            ),
            SizedBox(width: 8),
            Text(
              'J\'aime',
              style: TextStyle(
                color: post.liked ? Colors.red.shade600 : Colors.grey[600],
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernViewLikesButton(Post post) {
    return GestureDetector(
      onTap: () => _showLikedUsers(post),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
        decoration: BoxDecoration(
          color: Colors.purple.shade50,
          borderRadius: BorderRadius.circular(25),
          border: Border.all(color: Colors.purple.shade200, width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.visibility,
              color: Colors.purple.shade600,
              size: 18,
            ),
            SizedBox(width: 8),
            Text(
              '${post.likeCount}',
              style: TextStyle(
                color: Colors.purple.shade600,
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernCommentButton(Post post) {
    return GestureDetector(
      onTap: () => fetchComments(post),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
        decoration: BoxDecoration(
          color: Colors.green.shade50,
          borderRadius: BorderRadius.circular(25),
          border: Border.all(color: Colors.green.shade200, width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              color: Colors.green.shade600,
              size: 18,
            ),
            SizedBox(width: 8),
            Text(
              '${post.commentCount}',
              style: TextStyle(
                color: Colors.green.shade600,
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showLikedUsers(Post post) async {
    // Show loading dialog first
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            padding: EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
                ),
                SizedBox(height: 15),
                Text(
                  "Chargement des utilisateurs...",
                  style: TextStyle(
                    color: Colors.green[800],
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );

    try {
      // Fetch detailed liked users with photos
      List<UserLikeDTO> detailedLikedUsers =
          await postService.getDetailedLikedUsersWithPhotos(post.id);

      // Close loading dialog
      Navigator.pop(context);

      print(
          'Detailed liked users: ${detailedLikedUsers.map((u) => '${u.prenom} ${u.nom}').toList()}');

      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return Container(
            height: MediaQuery.of(context).size.height * 0.7,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(25),
                topRight: Radius.circular(25),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.3),
                  spreadRadius: 1,
                  blurRadius: 10,
                  offset: Offset(0, -2),
                ),
              ],
            ),
            child: Column(
              children: [
                // Handle bar
                Container(
                  margin: EdgeInsets.only(top: 12),
                  width: 50,
                  height: 5,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),

                // Header
                Container(
                  padding: EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.red.shade50, Colors.pink.shade50],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(25),
                      topRight: Radius.circular(25),
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.red.shade100,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child:
                            Icon(Icons.favorite, color: Colors.red, size: 24),
                      ),
                      SizedBox(width: 15),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Personnes ayant aimé',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey[800],
                              ),
                            ),
                            Text(
                              '${detailedLikedUsers.length} personne${detailedLikedUsers.length > 1 ? 's' : ''}',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: Icon(Icons.close, color: Colors.grey[600]),
                        ),
                      ),
                    ],
                  ),
                ),

                // List of users
                Expanded(
                  child: detailedLikedUsers.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                padding: EdgeInsets.all(20),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Icon(
                                  Icons.thumb_up_outlined,
                                  size: 64,
                                  color: Colors.grey[400],
                                ),
                              ),
                              SizedBox(height: 20),
                              Text(
                                'Aucun like pour le moment',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.grey[600],
                                ),
                              ),
                              SizedBox(height: 8),
                              Text(
                                'Soyez le premier à aimer ce post !',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[500],
                                ),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          padding: EdgeInsets.symmetric(
                              horizontal: 20, vertical: 10),
                          itemCount: detailedLikedUsers.length,
                          itemBuilder: (context, index) {
                            final user = detailedLikedUsers[index];
                            return Container(
                              margin: EdgeInsets.only(bottom: 12),
                              padding: EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(color: Colors.grey.shade200),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.grey.withOpacity(0.1),
                                    spreadRadius: 1,
                                    blurRadius: 6,
                                    offset: Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Row(
                                children: [
                                  Container(
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                          color: Colors.red.shade300, width: 2),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.red.withOpacity(0.2),
                                          spreadRadius: 1,
                                          blurRadius: 4,
                                        ),
                                      ],
                                    ),
                                    child: CircleAvatar(
                                      radius: 28,
                                      backgroundImage: user.userPhotoUrl !=
                                                  null &&
                                              user.userPhotoUrl!.isNotEmpty
                                          ? NetworkImage(user.userPhotoUrl!)
                                          : AssetImage(
                                                  'lib/resources/images/avatar_girl.png')
                                              as ImageProvider,
                                    ),
                                  ),
                                  SizedBox(width: 16),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          '${user.prenom} ${user.nom}',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                            color: Colors.grey[800],
                                          ),
                                        ),
                                        SizedBox(height: 6),
                                        Container(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 10, vertical: 4),
                                          decoration: BoxDecoration(
                                            color: Colors.blue.shade100,
                                            borderRadius:
                                                BorderRadius.circular(12),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Container(
                                    padding: EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.red.shade50,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Icon(
                                      Icons.favorite,
                                      color: Colors.red,
                                      size: 20,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
          );
        },
      );
    } catch (e) {
      // Close loading dialog if still open
      Navigator.pop(context);

      print('Error fetching liked users: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.error, color: Colors.white),
              SizedBox(width: 10),
              Expanded(
                child: Text('Erreur lors du chargement des utilisateurs'),
              ),
            ],
          ),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }

  Widget _buildModernMediaGrid(
      BuildContext context, List<String> media, String type) {
    // Créer une clé unique pour ce carousel
    final carouselKey = '${type}_${media.hashCode}';

    // Initialiser seulement l'index si nécessaire
    if (!_currentImageIndexes.containsKey(carouselKey)) {
      _currentImageIndexes[carouselKey] = 0;
    }

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          CarouselSlider.builder(
            // SUPPRIMEZ CETTE LIGNE :
            // carouselController: _carouselControllers[carouselKey],
            options: CarouselOptions(
              height: 300,
              enableInfiniteScroll: false,
              autoPlay: false,
              enlargeCenterPage: true,
              viewportFraction: 0.9,
              onPageChanged: (index, reason) {
                setState(() {
                  _currentImageIndexes[carouselKey] = index;
                });
              },
            ),
            itemCount: media.length,
            itemBuilder: (context, index, realIndex) {
              final mediaUrl = media[index];
              final isDownloading = _downloadStates[mediaUrl] ?? false;

              return Container(
                margin: EdgeInsets.symmetric(horizontal: 5),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.2),
                      spreadRadius: 1,
                      blurRadius: 10,
                      offset: Offset(0, 3),
                    ),
                  ],
                ),
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(20),
                      child: _buildMediaContent(
                          context, mediaUrl, type, isDownloading),
                    ),

                    // Badge du nombre total d'images (en haut à droite)
                    if (media.length > 1)
                      Positioned(
                        top: 12,
                        right: 12,
                        child: Container(
                          padding:
                              EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.7),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.2),
                                spreadRadius: 1,
                                blurRadius: 4,
                              ),
                            ],
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.photo_library,
                                color: Colors.white,
                                size: 14,
                              ),
                              SizedBox(width: 4),
                              Text(
                                '${index + 1}/${media.length}',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 11,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              );
            },
          ),

          // Indicateurs de pagination modernes (seulement si plus d'une image)
          if (media.length > 1) ...[
            SizedBox(height: 15),
            _buildSimplePageIndicators(
                media.length, _currentImageIndexes[carouselKey] ?? 0),
          ],
        ],
      ),
    );
  }
// Méthode simplifiée pour les indicateurs sans navigation
Widget _buildSimplePageIndicators(int itemCount, int currentIndex) {
  return Container(
    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
    decoration: BoxDecoration(
      gradient: LinearGradient(
        colors: [
          Colors.grey.shade100,
          Colors.white,
        ],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      borderRadius: BorderRadius.circular(25),
      boxShadow: [
        BoxShadow(
          color: Colors.grey.withOpacity(0.2),
          spreadRadius: 1,
          blurRadius: 8,
          offset: Offset(0, 2),
        ),
      ],
      border: Border.all(color: Colors.grey.shade200),
    ),
    child: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Points indicateurs avec animation
        Row(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(
            itemCount,
            (index) => AnimatedContainer(
              duration: Duration(milliseconds: 300),
              margin: EdgeInsets.symmetric(horizontal: 3),
              width: index == currentIndex ? 24 : 8,
              height: 8,
              decoration: BoxDecoration(
                gradient: index == currentIndex
                    ? LinearGradient(
                        colors: [
                          Colors.blue.shade400,
                          Colors.blue.shade600
                        ],
                      )
                    : null,
                color: index == currentIndex ? null : Colors.grey.shade300,
                borderRadius: BorderRadius.circular(4),
                boxShadow: index == currentIndex
                    ? [
                        BoxShadow(
                          color: Colors.blue.withOpacity(0.3),
                          spreadRadius: 1,
                          blurRadius: 4,
                        ),
                      ]
                    : null,
              ),
            ),
          ),
        ),

        SizedBox(width: 12),

        // Compteur
        Container(
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.photo_camera,
                color: Colors.blue.shade600,
                size: 14,
              ),
              SizedBox(width: 4),
              Text(
                '${currentIndex + 1}/$itemCount',
                style: TextStyle(
                  color: Colors.blue.shade600,
                  fontSize: 11,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    ),
  );
}

// Méthode pour créer les indicateurs modernes avec navigation
/*   Widget _buildModernPageIndicators(
      int itemCount, int currentIndex, String carouselKey) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.grey.shade100,
            Colors.white,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Bouton précédent
          GestureDetector(
            onTap: currentIndex > 0
                ? () {
                    _carouselControllers[carouselKey]?.previousPage(
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  }
                : null,
            child: Container(
              padding: EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: currentIndex > 0
                    ? Colors.blue.shade100
                    : Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.chevron_left,
                color: currentIndex > 0
                    ? Colors.blue.shade600
                    : Colors.grey.shade400,
                size: 18,
              ),
            ),
          ),

          SizedBox(width: 12),

          // Points indicateurs avec animation
          Row(
            mainAxisSize: MainAxisSize.min,
            children: List.generate(
              itemCount,
              (index) => GestureDetector(
                onTap: () {
                  _carouselControllers[carouselKey]?.animateToPage(
                    index,
                    duration: Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                },
                child: AnimatedContainer(
                  duration: Duration(milliseconds: 300),
                  margin: EdgeInsets.symmetric(horizontal: 3),
                  width: index == currentIndex ? 24 : 8,
                  height: 8,
                  decoration: BoxDecoration(
                    gradient: index == currentIndex
                        ? LinearGradient(
                            colors: [
                              Colors.blue.shade400,
                              Colors.blue.shade600
                            ],
                          )
                        : null,
                    color: index == currentIndex ? null : Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(4),
                    boxShadow: index == currentIndex
                        ? [
                            BoxShadow(
                              color: Colors.blue.withOpacity(0.3),
                              spreadRadius: 1,
                              blurRadius: 4,
                            ),
                          ]
                        : null,
                  ),
                ),
              ),
            ),
          ),

          SizedBox(width: 12),

          // Compteur
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.photo_camera,
                  color: Colors.blue.shade600,
                  size: 14,
                ),
                SizedBox(width: 4),
                Text(
                  '${currentIndex + 1}/$itemCount',
                  style: TextStyle(
                    color: Colors.blue.shade600,
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(width: 12),

          // Bouton suivant
          GestureDetector(
            onTap: currentIndex < itemCount - 1
                ? () {
                    _carouselControllers[carouselKey]?.nextPage(
                      duration: Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    );
                  }
                : null,
            child: Container(
              padding: EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: currentIndex < itemCount - 1
                    ? Colors.blue.shade100
                    : Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.chevron_right,
                color: currentIndex < itemCount - 1
                    ? Colors.blue.shade600
                    : Colors.grey.shade400,
                size: 18,
              ),
            ),
          ),
        ],
      ),
    );
  }
 */
  Widget _buildMediaContent(
      BuildContext context, String mediaUrl, String type, bool isDownloading) {
    if (type == 'photo') {
      return GestureDetector(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => FullScreenImageView(imageUrl: mediaUrl),
            ),
          );
        },
        child: Stack(
          fit: StackFit.expand,
          children: [
            Image.network(
              mediaUrl,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                color: Colors.grey[300],
                child:
                    Icon(Icons.broken_image, size: 50, color: Colors.grey[600]),
              ),
            ),
            if (isDownloading)
              Container(
                color: Colors.black.withOpacity(0.3),
                child: Center(
                  child: Container(
                    padding: EdgeInsets.all(15),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CircularProgressIndicator(color: Colors.green),
                        SizedBox(height: 10),
                        Text('Téléchargement...',
                            style: TextStyle(fontWeight: FontWeight.w600)),
                      ],
                    ),
                  ),
                ),
              ),
            Positioned(
              top: 15,
              right: 15,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.9),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: IconButton(
                  icon: isDownloading
                      ? SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.green,
                          ),
                        )
                      : Icon(Icons.download, color: Colors.green, size: 20),
                  onPressed: isDownloading
                      ? null
                      : () => _downloadFile(context, mediaUrl),
                ),
              ),
            ),
          ],
        ),
      );
    } else if (type == 'video') {
      return Stack(
        fit: StackFit.expand,
        children: [
          InlineVideoPlayer(videoUrl: mediaUrl),
          if (isDownloading)
            Container(
              color: Colors.black.withOpacity(0.3),
              child: Center(
                child: Container(
                  padding: EdgeInsets.all(15),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CircularProgressIndicator(color: Colors.green),
                      SizedBox(height: 10),
                      Text('Téléchargement...',
                          style: TextStyle(fontWeight: FontWeight.w600)),
                    ],
                  ),
                ),
              ),
            ),
          Positioned(
            top: 15,
            right: 15,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.9),
                borderRadius: BorderRadius.circular(25),
              ),
              child: IconButton(
                icon: isDownloading
                    ? SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.green,
                        ),
                      )
                    : Icon(Icons.download, color: Colors.green, size: 20),
                onPressed: isDownloading
                    ? null
                    : () => _downloadFile(context, mediaUrl),
              ),
            ),
          ),
        ],
      );
    } else if (type == 'pdf') {
      return GestureDetector(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => PDFViewerScreen(pdfUrl: mediaUrl),
            ),
          );
        },
        child: Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [Colors.red.shade100, Colors.red.shade50],
                ),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.red.withOpacity(0.2),
                            spreadRadius: 1,
                            blurRadius: 10,
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.picture_as_pdf,
                        color: Colors.red.shade600,
                        size: 50,
                      ),
                    ),
                    SizedBox(height: 15),
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(25),
                      ),
                      child: Text(
                        'Voir le PDF',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.red.shade600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (isDownloading)
              Container(
                color: Colors.black.withOpacity(0.3),
                child: Center(
                  child: Container(
                    padding: EdgeInsets.all(15),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CircularProgressIndicator(color: Colors.green),
                        SizedBox(height: 10),
                        Text('Téléchargement...',
                            style: TextStyle(fontWeight: FontWeight.w600)),
                      ],
                    ),
                  ),
                ),
              ),
            Positioned(
              top: 15,
              right: 15,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.9),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: IconButton(
                  icon: isDownloading
                      ? SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.green,
                          ),
                        )
                      : Icon(Icons.download, color: Colors.green, size: 20),
                  onPressed: isDownloading
                      ? null
                      : () => _downloadFile(context, mediaUrl),
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      return Container(
        color: Colors.grey[300],
        child: Icon(Icons.broken_image, size: 50, color: Colors.grey[600]),
      );
    }
  }

  Widget _buildModernCommentSection(Post post) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Comment input
          Container(
            padding: EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.green.shade300, width: 2),
                  ),
                  child: CircleAvatar(
                    radius: 18,
                    backgroundImage: isLoading
                        ? const AssetImage(
                                'lib/resources/images/avatar_girl.png')
                            as ImageProvider
                        : (userPhotoUrl != null
                            ? NetworkImage(userPhotoUrl!)
                            : const AssetImage(
                                'lib/resources/images/avatar_girl.png')),
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(25),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: TextField(
                      controller: _commentController,
                      // AJOUTEZ CES LIGNES : Gérer le focus manuellement
                      focusNode: FocusNode(),
                      onTap: () {
                        // S'assurer que le focus est correctement géré
                      },
                      onSubmitted: (value) {
                        // Masquer le clavier après soumission
                        FocusScope.of(context).unfocus();
                        if (value.trim().isNotEmpty) {
                          submitComment(post);
                        }
                      },
                      decoration: InputDecoration(
                        hintText: 'Ajouter un commentaire...',
                        hintStyle: TextStyle(color: Colors.grey[500]),
                        border: InputBorder.none,
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 8),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: IconButton(
                    onPressed: _submitedComment
                        ? null
                        : () {
                            // AJOUTEZ CETTE LIGNE : Masquer le clavier après envoi
                            FocusScope.of(context).unfocus();
                            submitComment(post);
                          },
                    icon: _submitedComment
                        ? SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                        : Icon(Icons.send, color: Colors.white, size: 20),
                  ),
                ),
              ],
            ),
          ),

          // NEW: Comment pagination info
          _buildCommentPaginationInfo(post),

          // Comments list
          _buildModernComments(post),

          // NEW: Comment pagination controls
          _buildCommentPaginationControls(post),
        ],
      ),
    );
  }

// NEW: Comment pagination info widget
  Widget _buildCommentPaginationInfo(Post post) {
    final allCommentsCount = _allComments[post.id]?.length ?? 0;
    final displayedCount = _displayedComments[post.id]?.length ?? 0;
    final currentPage = _commentPages[post.id] ?? 0;
    final totalPages =
        allCommentsCount > 0 ? (allCommentsCount / _commentsPerPage).ceil() : 0;

    if (allCommentsCount <= _commentsPerPage) return SizedBox.shrink();

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.blue.shade100,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.comment, size: 16, color: Colors.blue.shade700),
                SizedBox(width: 6),
                Text(
                  'Page ${currentPage + 1} sur $totalPages',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.blue.shade700,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(width: 10),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.green.shade100,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              '$displayedCount sur $allCommentsCount commentaires',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Colors.green.shade700,
              ),
            ),
          ),
        ],
      ),
    );
  }

// NEW: Comment pagination controls widget
  Widget _buildCommentPaginationControls(Post post) {
    final allCommentsCount = _allComments[post.id]?.length ?? 0;
    final currentPage = _commentPages[post.id] ?? 0;
    final totalPages =
        allCommentsCount > 0 ? (allCommentsCount / _commentsPerPage).ceil() : 0;
    final hasMore = _hasMoreComments[post.id] ?? false;
    final hasPrevious = currentPage > 0;

    if (allCommentsCount <= _commentsPerPage) return SizedBox.shrink();

    return Container(
      padding: EdgeInsets.all(20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Previous button
          // Previous button
          Expanded(
            child: AnimatedContainer(
              duration: Duration(milliseconds: 200),
              child: ElevatedButton.icon(
                onPressed:
                    hasPrevious ? () => _loadPreviousComments(post.id) : null,
                icon: Icon(
                  Icons.arrow_back_ios,
                  size: 16,
                  color: hasPrevious ? Colors.white : Colors.grey.shade50,
                ),
                label: Text(
                  'Précédent',
                  style: TextStyle(
                    color: hasPrevious ? Colors.white : Colors.grey.shade50,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      hasPrevious ? Colors.blue.shade600 : Colors.grey.shade300,
                  foregroundColor:
                      hasPrevious ? Colors.white : Colors.grey.shade400,
                  elevation: hasPrevious ? 2 : 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                  padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                ),
              ),
            ),
          ),

          SizedBox(width: 15),

          // Page indicator
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.grey.shade300),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  spreadRadius: 1,
                  blurRadius: 3,
                ),
              ],
            ),
            child: Text(
              '${currentPage + 1}/$totalPages',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade700,
                fontSize: 14,
              ),
            ),
          ),

          SizedBox(width: 15),

          // Next button
          Expanded(
            child: AnimatedContainer(
              duration: Duration(milliseconds: 200),
              child: ElevatedButton.icon(
                onPressed: hasMore ? () => _loadMoreComments(post.id) : null,
                icon: Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: hasMore ? Colors.white : Colors.grey.shade400,
                ),
                label: Text(
                  'Suivant',
                  style: TextStyle(
                    color: hasMore ? Colors.white : Colors.grey.shade400,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      hasMore ? Colors.green.shade600 : Colors.grey.shade300,
                  foregroundColor:
                      hasMore ? Colors.white : Colors.grey.shade400,
                  elevation: hasMore ? 2 : 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                  padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernComments(Post post) {
    if (userConnectedId == null) {
      return Container(
        padding: EdgeInsets.all(20),
        child: Center(child: CircularProgressIndicator()),
      );
    }

    // Check if comments are loading
    if (_commentLoadingStates[post.id] == true) {
      return Container(
        padding: EdgeInsets.all(20),
        child: Center(
          child: Column(
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
              ),
              SizedBox(height: 10),
              Text(
                'Chargement des commentaires...',
                style: TextStyle(
                  color: Colors.green[800],
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      );
    }

    final displayedComments = _displayedComments[post.id] ?? post.comments;
    final allCommentsCount =
        _allComments[post.id]?.length ?? post.comments.length;

    if (displayedComments.isEmpty) {
      return Container(
        padding: EdgeInsets.all(20),
        child: Center(
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  Icons.chat_bubble_outline,
                  size: 48,
                  color: Colors.grey[400],
                ),
              ),
              SizedBox(height: 15),
              Text(
                'Aucun commentaire pour le moment',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 5),
              Text(
                'Soyez le premier à commenter !',
                style: TextStyle(
                  color: Colors.grey[500],
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return AnimatedBuilder(
      animation: _commentPaginationController,
      builder: (context, child) {
        return Column(
          children: [
            // Comments count indicator (only show if there are more comments than displayed)
            if (allCommentsCount > displayedComments.length)
              Container(
                margin: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                padding: EdgeInsets.symmetric(horizontal: 15, vertical: 8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.orange.shade100, Colors.orange.shade50],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: Colors.orange.shade200),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.info_outline,
                        size: 16, color: Colors.orange.shade700),
                    SizedBox(width: 8),
                    Text(
                      '${allCommentsCount - displayedComments.length} commentaire(s) supplémentaire(s)',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.orange.shade700,
                      ),
                    ),
                  ],
                ),
              ),

            // Comments list with animation
            ...displayedComments.asMap().entries.map((entry) {
              final index = entry.key;
              final comment = entry.value;

              return AnimatedContainer(
                duration: Duration(milliseconds: 300 + (index * 50)),
                curve: Curves.easeOutBack,
                transform: Matrix4.translationValues(
                  0,
                  _commentPaginationController.value * 20 * (1 - (index * 0.1)),
                  0,
                ),
                child: AnimatedOpacity(
                  duration: Duration(milliseconds: 400 + (index * 100)),
                  opacity: 1.0 - (_commentPaginationController.value * 0.3),
                  child: Container(
                    margin: EdgeInsets.only(left: 20, right: 20, bottom: 15),
                    padding: EdgeInsets.all(15),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(15),
                      border: Border.all(color: Colors.grey.shade200),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.1),
                          spreadRadius: 1,
                          blurRadius: 5,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header avec photo et nom
                        Row(
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                    color: Colors.blue.shade200, width: 2),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.blue.withOpacity(0.1),
                                    spreadRadius: 1,
                                    blurRadius: 3,
                                  ),
                                ],
                              ),
                              child: comment.userPhotoUrl != null &&
                                      comment.userPhotoUrl!.isNotEmpty
                                  ? CircleAvatar(
                                      backgroundImage:
                                          NetworkImage(comment.userPhotoUrl!),
                                      radius: 18,
                                    )
                                  : const CircleAvatar(
                                      backgroundImage: AssetImage(
                                          'lib/resources/images/avatar_girl.png'),
                                      radius: 18,
                                    ),
                            ),
                            SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '${comment.prenomUtilisateur} ${comment.nomUtilisateur}',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 15,
                                      color: Colors.blue.shade700,
                                    ),
                                  ),
                                  SizedBox(height: 2),
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 2),
                                    decoration: BoxDecoration(
                                      color: Colors.grey.shade100,
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    child: Text(
                                      'Posté le ${DateFormat('dd/MM/yyyy HH:mm').format(comment.createdAt.add(Duration(hours: 1)).toLocal())}',
                                      style: TextStyle(
                                        fontSize: 11,
                                        color: Colors.grey[600],
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // Boutons d'action avec animations
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // Edit button - Only for comment owner
                                if (userConnectedId == comment.userId)
                                  AnimatedContainer(
                                    duration: Duration(milliseconds: 200),
                                    decoration: BoxDecoration(
                                      color: Colors.blue.shade50,
                                      borderRadius: BorderRadius.circular(10),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.blue.withOpacity(0.1),
                                          spreadRadius: 1,
                                          blurRadius: 3,
                                        ),
                                      ],
                                    ),
                                    child: IconButton(
                                      icon: Icon(Icons.edit_outlined,
                                          size: 18,
                                          color: Colors.blue.shade600),
                                      onPressed: () {
                                        setState(() {
                                          comment.editMode = true;
                                          comment.tempContent =
                                              comment.commentaire;
                                        });
                                      },
                                      constraints: BoxConstraints(
                                          minWidth: 36, minHeight: 36),
                                      tooltip: 'Modifier le commentaire',
                                    ),
                                  ),
                                if (userConnectedId == comment.userId)
                                  SizedBox(width: 8),
                                // Delete button - Available for ALL comments (Directrice can delete any comment)
                                AnimatedContainer(
                                  duration: Duration(milliseconds: 200),
                                  decoration: BoxDecoration(
                                    color: Colors.red.shade50,
                                    borderRadius: BorderRadius.circular(10),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.red.withOpacity(0.1),
                                        spreadRadius: 1,
                                        blurRadius: 3,
                                      ),
                                    ],
                                  ),
                                  child: IconButton(
                                    icon: _deletingComments.contains(comment.id)
                                        ? SizedBox(
                                            width: 18,
                                            height: 18,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              color: Colors.red.shade600,
                                            ),
                                          )
                                        : Icon(Icons.delete_outline,
                                            size: 18,
                                            color: Colors.red.shade600),
                                    onPressed:
                                        _deletingComments.contains(comment.id)
                                            ? null
                                            : () => _showDeleteCommentDialog(
                                                post, comment),
                                    constraints: BoxConstraints(
                                        minWidth: 36, minHeight: 36),
                                    tooltip: userConnectedId == comment.userId
                                        ? 'Supprimer mon commentaire'
                                        : 'Supprimer ce commentaire (Directrice)',
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        SizedBox(height: 12),

                        // Contenu du commentaire ou champ d'édition
                        AnimatedSwitcher(
                          duration: Duration(milliseconds: 300),
                          transitionBuilder:
                              (Widget child, Animation<double> animation) {
                            return FadeTransition(
                              opacity: animation,
                              child: SlideTransition(
                                position: Tween<Offset>(
                                  begin: Offset(0.0, 0.1),
                                  end: Offset.zero,
                                ).animate(animation),
                                child: child,
                              ),
                            );
                          },
                          child: comment.editMode
                              ? _buildEditCommentField(comment)
                              : _buildCommentContent(comment),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ],
        );
      },
    );
  }

  Widget _buildCommentContent(Comment comment) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(15),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.grey.shade50, Colors.white],
        ),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.grey.shade200, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.05),
            spreadRadius: 1,
            blurRadius: 3,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            comment.commentaire,
            style: TextStyle(
              fontSize: 15,
              height: 1.5,
              color: Colors.grey[800],
              fontWeight: FontWeight.w400,
            ),
          ),
          SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.access_time,
                        size: 12, color: Colors.blue.shade600),
                    SizedBox(width: 4),
                    Text(
                      _timeAgo(comment.createdAt),
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.blue.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEditCommentField(Comment comment) {
    final TextEditingController editController = TextEditingController(
      text: comment.tempContent ?? comment.commentaire,
    );

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.blue.shade50, Colors.white],
        ),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.blue.shade200, width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header for edit mode
          Container(
            padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
            decoration: BoxDecoration(
              color: Colors.blue.shade100,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(13),
                topRight: Radius.circular(13),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.edit, size: 16, color: Colors.blue.shade700),
                SizedBox(width: 8),
                Text(
                  'Mode édition',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.blue.shade700,
                  ),
                ),
              ],
            ),
          ),

          // Text field
          Container(
            padding: EdgeInsets.all(15),
            child: TextField(
              controller: editController,
              onChanged: (value) {
                comment.tempContent = value;
              },
              maxLines: 4,
              decoration: InputDecoration(
                hintText: 'Modifier votre commentaire...',
                hintStyle: TextStyle(color: Colors.grey[500]),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.blue.shade400, width: 2),
                ),
                filled: true,
                fillColor: Colors.white,
                contentPadding: EdgeInsets.all(15),
              ),
              style: TextStyle(
                fontSize: 14,
                height: 1.4,
              ),
            ),
          ),

          // Action buttons
          Container(
            padding: EdgeInsets.symmetric(horizontal: 15, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(13),
                bottomRight: Radius.circular(13),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // Cancel button
                TextButton.icon(
                  onPressed: () {
                    setState(() {
                      comment.editMode = false;
                      comment.tempContent = comment.commentaire;
                    });
                  },
                  icon: Icon(Icons.close, size: 16, color: Colors.grey[600]),
                  label: Text(
                    'Annuler',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),
                SizedBox(width: 12),

                // Save button
                ElevatedButton.icon(
                  onPressed: isUpdating
                      ? null
                      : () => updateComment(
                          comment, comment.tempContent ?? comment.commentaire),
                  icon: isUpdating
                      ? SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : Icon(Icons.save, size: 16, color: Colors.white),
                  label: Text(
                    isUpdating ? 'Sauvegarde...' : 'Sauvegarder',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green.shade600,
                    foregroundColor: Colors.white,
                    elevation: 2,
                    padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteCommentDialog(Post post, Comment comment) {
    // AJOUTEZ CETTE LIGNE : Masquer le clavier avant d'afficher le dialog
    FocusScope.of(context).unfocus();

    showDialog(
      context: context,
      barrierDismissible: true, // Permettre de fermer en cliquant à l'extérieur
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Icon(Icons.warning, color: Colors.orange),
              SizedBox(width: 10),
              Text('Confirmation'),
            ],
          ),
          content: Text(
            'Êtes-vous sûr de vouloir supprimer ce commentaire ?',
            style: TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () {
                // AJOUTEZ CETTE LIGNE : Masquer le clavier lors de l'annulation
                FocusScope.of(context).unfocus();
                Navigator.pop(context);
              },
              child: Text(
                'Annuler',
                style: TextStyle(color: Colors.grey[600]),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                // AJOUTEZ CETTE LIGNE : Masquer le clavier avant la suppression
                FocusScope.of(context).unfocus();
                Navigator.pop(context);
                deleteComment(post, comment);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: Text(
                'Supprimer',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }

  // Add this helper method to safely decode content
  String? _decodeContent(String? content) {
    if (content == null || content.isEmpty) return null;

    try {
      // First try to decode as UTF-8
      print(
          'Attempting to decode content as UTF-8: $utf8.decode(content as List<int>)');
      return utf8.decode(content as List<int>);
    } catch (e) {
      try {
        // If that fails, try the latin1 approach
        print(
            'Failed to decode as UTF-8, trying latin1: $latin1.encode(content)');
        return utf8.decode(latin1.encode(content));
      } catch (e2) {
        // If both fail, return the original content
        return content;
      }
    }
  }
}
