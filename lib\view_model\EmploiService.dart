import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../resources/environnement/apiUrl.dart';
import '../view_model/tokenService.dart';
import 'package:flutter/material.dart';



class EmploiService {
  final baseUrl = ApiUrl.baseUrl;
  final TokenService tokenService = TokenService();

  Future<Map<String, String>> _getAuthHeaders() async {
    final token = await tokenService.getToken();
    return {
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
  }


  /// Récupère tous les emplois.
  Future<List<Map<String, dynamic>>> getAllEmplois() async {
    try {
      final headers = await _getAuthHeaders();
      final response =
          await http.get(Uri.parse('${baseUrl}api/emplois'), headers: headers);

      debugPrint('Response status: ${response.statusCode}');
      debugPrint('Response body: ${response.body}');

      if (response.statusCode == 200) {
        final List<dynamic> jsonResponse = jsonDecode(response.body);
        return jsonResponse.map((item) {
          debugPrint('Item from server: $item');
          return Map<String, dynamic>.from(item);
        }).toList();
      } else {
        throw Exception(
            'Erreur ${response.statusCode}: ${response.reasonPhrase}');
      }
    } catch (e) {
      debugPrint('Erreur dans getAllEmplois: $e');
      throw Exception('Erreur lors de la récupération des emplois : $e');
    }
  }
 
 /// Récupère un emploi par son ID.
Future<Map<String, dynamic>> getEmploiById(int emploiId) async {
  try {
    final headers = await _getAuthHeaders();
    final response = await http.get(
      Uri.parse('${baseUrl}api/emplois/$emploiId'),
      headers: headers,
    );

    debugPrint('Response status: ${response.statusCode}');
    debugPrint('Response body: ${response.body}');

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Erreur ${response.statusCode}: ${response.reasonPhrase}');
    }
  } catch (e) {
    debugPrint('Erreur dans getEmploiById: $e');
    throw Exception('Erreur lors de la récupération de l\'emploi : $e');
  }
}

  /// Crée un emploi en téléchargeant un fichier et en l'associant à une classe.
  Future<Map<String, dynamic>> createEmploi(
      String name, File file, int classeId) async {
    try {
      final headers = await _getAuthHeaders();
      // Modification: Utilisation de Long dans Spring, donc on doit convertir classeId en String
      final uri = Uri.parse('${baseUrl}api/emplois/create/$classeId');

      var request = http.MultipartRequest('POST', uri);

      // Suppression du Content-Type des headers car MultipartRequest le gère automatiquement
      request.headers.addAll({
        'Authorization': headers['Authorization']!,
        'Accept': 'application/json',
      });

      // Modification: Alignement avec les paramètres du backend
      request.fields['name'] = name;
      // Pas besoin d'envoyer classeId dans les fields car il est déjà dans l'URL

      // Modification: Le nom du champ file doit correspondre au paramètre du backend
      var multipartFile = await http.MultipartFile.fromPath('file', file.path);
      request.files.add(multipartFile);

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 201 || response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
            'Erreur lors de la création de l\'emploi. Status: ${response.statusCode}, Body: ${response.body}');
      }
    } catch (e) {
      debugPrint('Erreur dans createEmploi: $e');
      throw Exception('Erreur lors de la création de l\'emploi : $e');
    }
  }

  /// Met à jour un emploi (nom et/ou fichier).
  Future<Map<String, dynamic>> updateEmploi(int emploiId,
      {String? name, File? file}) async {
    try {
      final headers = await _getAuthHeaders();
      final uri = Uri.parse('${baseUrl}api/emplois/update/$emploiId');
      final request = http.MultipartRequest('PUT', uri);

      request.headers.addAll({
        'Authorization': headers['Authorization']!,
        'Accept': 'application/json',
      });

      // Modification: Alignement avec les paramètres du backend
      if (name != null) {
        request.fields['name'] = name;
      }

      if (file != null) {
        request.files.add(await http.MultipartFile.fromPath('file', file.path));
      }

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
            'Erreur lors de la mise à jour de l\'emploi : ${response.reasonPhrase}');
      }
    } catch (e) {
      throw Exception('Erreur lors de la mise à jour de l\'emploi : $e');
    }
  }

 /// Supprime un emploi par son ID.
Future<void> deleteEmploi(int emploiId) async {
  try {
    final headers = await _getAuthHeaders();
    final url = Uri.parse('${baseUrl}api/emplois/$emploiId');
    debugPrint('URL de suppression : $url');

    final response = await http.delete(url, headers: headers);

    if (response.statusCode == 200 || response.statusCode == 204) {
      // Succès - pas besoin de décoder la réponse
      return;
    } else {
      throw Exception('Échec de la suppression (${response.statusCode}): ${response.reasonPhrase}');
    }
  } catch (e) {
    debugPrint('Erreur lors de la suppression: $e');
    throw Exception('Erreur lors de la suppression de l\'emploi');
  }
}

/// Récupère les emplois pour une classe donnée par son ID.
Future<List<Map<String, dynamic>>> getEmploisByClasseId(int classeId) async {
  try {
    final headers = await _getAuthHeaders();
    final response = await http.get(
      Uri.parse('${baseUrl}api/emplois/classe/$classeId'),
      headers: headers,
    );

    debugPrint('Response status: ${response.statusCode}');
    debugPrint('Response body: ${response.body}');

    if (response.statusCode == 200) {
      final List<dynamic> jsonResponse = jsonDecode(response.body);
      return jsonResponse.map((item) {
        debugPrint('Item from server: $item');
        return Map<String, dynamic>.from(item);
      }).toList();
    } else {
      throw Exception(
          'Erreur ${response.statusCode}: ${response.reasonPhrase}');
    }
  } catch (e) {
    debugPrint('Erreur dans getEmploisByClasseId: $e');
    throw Exception('Erreur lors de la récupération des emplois : $e');
  }
}


  /// Récupère le dernier emploi pour une classe donnée.
  Future<Map<String, dynamic>> getLatestEmploiByClasseId(int classe_id) async {
    try {
      final headers = await _getAuthHeaders();
      final response = await http.get(
          Uri.parse('${baseUrl}api/emplois/classe/$classe_id/latest'),
          headers: headers);
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
            'Erreur lors de la récupération du dernier emploi : ${response.reasonPhrase}');
      }
    } catch (e) {
      throw Exception('Erreur lors de la récupération du dernier emploi : $e');
    }
  }
}
